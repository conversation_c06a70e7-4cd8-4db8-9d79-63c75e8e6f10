<script setup lang="ts">
import type { DialogProps } from 'element-plus'
import { ElDialog } from 'element-plus'
import { computed, defineModel, useSlots, watch } from 'vue'

export interface HrtDialogProps extends Partial<DialogProps> {
  /** 弹窗大小 */
  size?: 'xs' | 'md' | 'lg' | 'xl'
}
defineOptions({
  name: 'HrtDialog',
})
const { size = 'lg', modalClass, showClose = true, modelValue, ...restProps } = defineProps<HrtDialogProps>()
const emit = defineEmits<{
  open: []
  opened: []
  close: []
  closed: []
  openAutoFocus: []
  closeAutoFocus: []
}>()
const slots = useSlots()
const show = defineModel({ default: false })

const modalClassValue = computed(() => {
  let sizeClass
  switch (size) {
    case 'xs':
      sizeClass = 'hrt-dialog-xs'
      break
    case 'md':
      sizeClass = 'hrt-dialog-md'
      break
    case 'lg':
      sizeClass = 'hrt-dialog-lg'
      break
    case 'xl':
      sizeClass = 'hrt-dialog-xl'
      break
    default:
      sizeClass = ''
      break
  }
  return `hrt-dialog ${sizeClass} ${modalClass ?? ''}`
})

const columnSlots = computed(() => {
  if (slots) {
    return Object.keys(slots)
      .map((t) => {
        return { name: t, slot: slots[t] }
      })
  }
  return []
})

function openModal() {
  document.body.classList.add('hrt-body-no-scroll')
}

function closeModal() {
  document.body.classList.remove('hrt-body-no-scroll')
}

watch(show, (val) => {
  if (val) {
    openModal()
  }
  else {
    closeModal()
  }
})
</script>

<template>
  <ElDialog
    v-model="show"
    v-bind="restProps"
    :modal-class="modalClassValue"
    :show-close="showClose"
    @open="emit('open')"
    @opened="emit('opened')"
    @close="emit('close')"
    @closed="emit('closed')"
    @open-auto-focus="emit('openAutoFocus')"
    @close-auto-focus="emit('closeAutoFocus')"
  >
    <template v-for="slot in columnSlots" :key="slot.name" #[slot.name]="{ record }">
      <slot :name="slot.name" :record="record" />
    </template>
  </ElDialog>
</template>

<style lang="css">
.hrt-dialog {
  .el-dialog {
    @apply hrt-rounded hrt-shadow-lg hrt-flex hrt-flex-col;
    .el-dialog__body {
      flex: 1;
      overflow: auto;
    }
  }
}

.hrt-dialog-xl {
  .el-dialog {
    height: 560px;
    width: 800px;
  }
}

.hrt-dialog-lg {
  .el-dialog {
    width: 600px;
    height: 400px;
  }
}

.hrt-dialog-md {
  .el-dialog {
    width: 400px;
    height: 240px;
  }
}

.hrt-dialog-xs {
  .el-dialog {
    width: 320px;
    height: 160px;
  }
}

.el-overlay-dialog {
  background-color: var(--el-overlay-color-lighter);
}

.hrt-body-no-scroll {
  overflow: hidden;
}
</style>
