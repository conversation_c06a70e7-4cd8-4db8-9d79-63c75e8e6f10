<script setup lang="ts">
import { UploadFilled } from '@element-plus/icons-vue'
import { HrtButton, HrtUpload, type HrtUploadModelValue } from '@hrt/components'
import { ElIcon } from 'element-plus'
import { ref } from 'vue'

const fileList1 = ref<HrtUploadModelValue>([])
const fileList2 = ref<HrtUploadModelValue>([])
const fileList3 = ref<HrtUploadModelValue>([])
</script>

<template>
  <div class="hrt-w-[350px]">
    <div class="hrt-mb-[20px]">
      <HrtUpload
        v-model="fileList1"
        list-type="text"
        :limit="5"
        :limit-size="5 * 1024 * 1024"
        :auto-upload="false"
        tip="最多上传5个文件，每个文件大小不超过5MB"
      >
        <HrtButton type="primary">
          点击上传
        </HrtButton>
      </HrtUpload>
    </div>
    <div class="hrt-mb-[20px]">
      <HrtUpload
        v-model="fileList2"
        list-type="picture"
        :limit="5"
        :auto-upload="false"
        drag
      >
        <div class="hrt-upload-drag">
          <ElIcon class="el-icon--upload">
            <UploadFilled />
          </ElIcon>
          <div class="el-upload__text">
            点击上传，或将文件拖动到此区域，只支持图片
          </div>
        </div>
      </HrtUpload>
    </div>
    <div>
      <HrtUpload
        v-model="fileList3"
        list-type="picture-card"
        :limit="5"
        :auto-upload="false"
      >
        <ElIcon>
          <UploadFilled />
        </ElIcon>
      </HrtUpload>
    </div>
  </div>
</template>

<style lang="less">
@import url(./style.less);
</style>
