# management-web

## 介绍

哈瑞特后台管理系统，体系内账号&权限、患者、医院【工作室】、订单等模块管理。

## 安装
```bash
$ npm i / yarn                # 推荐使用yarn，不推荐npm
$ npm run dev / yarn dev      # 本地运行
$ npm run build / yarn build  # 打包
```

## 技术栈

- 前端：Vue2、Webpack、JavaScript、Element

## 新增路由

1. 在 `src/views` 中新增页面文件夹，创建相关页面
2. 在 `src/views/menuAbout.js` 中新增路由信息
3. 用户登录账号能够访问哪些页面有登录接口 `/admins/doLogin` 返回的 `routeList` 字段决定，存储在 `sessionStorage` 中，找后端配置路由权限。
