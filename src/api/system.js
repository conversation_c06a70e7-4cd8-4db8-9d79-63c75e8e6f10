//查询角色列表
import { httpReq } from "@/http";
export function getRoleList(data) {
  return httpReq({
    url: "/api/role/query/list",
    method: "post",
    data
  });
}
//创建角色
export function createRole(data) {
  return httpReq({
    url: "/api/role/insert",
    method: "post",
    data
  });
}
//编辑角色
export function updateRole(data) {
  return httpReq({
    url: "/api/role/update/info",
    method: "patch",
    data
  });
}
//删除角色
export function deleteRole(data) {
  return httpReq({
    url: "/api/role/delete",
    method: "delete",
    data
  });
}
//获取权限列表
export function getPermissionList(data) {
  return httpReq({
    url: "/api/role/type/query/permission",
    method: "post",
    data
  });
}
//查询当前角色权限详情
export function getCurrentRolePermissionDetails(data) {
  return httpReq({
    url: "/api/role/query/info",
    method: "post",
    data
  });
}

// 获取字典配置列表
export function getDictConfigList() {
  return httpReq({
    url: "/project/config/dict-config/query/list",
    method: "post",
    data: {
      type: null
    }
  });
}

// 获取项目配置列表
export function getProjectQueryPage(data) {
  return httpReq({
    url: "/project/query/page",
    method: "post",
    data
  });
}

// 查询单个项目详情
export function getProjectDetails(data) {
  return httpReq({
    url: "/project/query",
    method: "post",
    data: {
      ...data,
      needProjectTeam: true,
      needProjectGroup: true,
      needProjectBranchCenter: true,
      needProjectRandomConfig: true,
      needProjectDictConfig: true,
      needCaseEnrollDesc: true
    }
  });
}

// 从已有项目创建科研项目

export function copyCreate(data) {
  return httpReq({
    url: "/project/copy/create",
    method: "post",
    data
  });
}

// 获取所有医院列表

export function getAllHospitalList(data = { type: null }) {
  return httpReq({
    url: "/doctors/getHospitalList",
    method: "get"
  });
  // return httpReq({
  //   url: '/project/branch/groups/hospital/query/page',
  //   method: 'post',
  //   data
  // })
}

// 分页查询项目名称及id

export function getProjectNameAndId(data) {
  return httpReq({
    url: "/project/name/query/list",
    method: "post",
    data
  });
}

// 分页查询项目下分中心列表
export function getSubCenterList(data) {
  return httpReq({
    url: "/project/branch/query/page",
    method: "post",
    data
  });
}

// 分页查询组长单位
export function getBranchHospitalList(data) {
  return httpReq({
    url: "/project/branch/groups/hospital/query/page",
    method: "post",
    data
  });
}

// 分页查询组长单位下的工作室列表
export function getProjectBranchList(data) {
  return httpReq({
    url: "/project/branch/groups/query/page",
    method: "post",
    data
  });
}

// 新建项目基本信息
export function createProject(data) {
  return httpReq({
    url: "/project/create",
    method: "post",
    data
  });
}
// 编辑项目基本信息

export function editProject(data) {
  return httpReq({
    url: "/project/edit",
    method: "post",
    data
  });
}

// 新建单个项目的分中心
export function createProjectBranch(data) {
  return httpReq({
    url: "/project/branch/create",
    method: "post",
    data
  });
}
// 删除分中心
export function deleteProjectBranch(data) {
  return httpReq({
    url: "/project/branch/delete",
    method: "post",
    data
  });
}
// 删除科研项目
export function deleteProject(data) {
  return httpReq({
    url: "/project/delete",
    method: "post",
    data
  });
}
// 启动科研项目
export function openStart(data) {
  return httpReq({
    url: "/project/start",
    method: "post",
    data
  });
}

//编辑项目分中心
export function editProjectBranch(data) {
  return httpReq({
    url: "/project/branch/edit",
    method: "post",
    data
  });
}

// 修改分中心状态(没用)
export function updateProjectBranchStatus(data) {
  return httpReq({
    url: "/project/branch/subCenter/status/update",
    method: "post",
    data
  });
}
// 暂停分中心
export function suspendBranchCenter(data) {
  return httpReq({
    url: "/project/branch/suspend",
    method: "post",
    data
  });
}
// 重启分中心
export function restartBranchCenter(data) {
  return httpReq({
    url: "/project/branch/restart",
    method: "post",
    data
  });
}

// 科研项目变更为创建状态
export function changeProjectStatus(data) {
  return httpReq({
    url: "/project/confirm",
    method: "post",
    data
  });
}

// 批量新建分中心
export function createProjectBranchList(data) {
  return httpReq({
    url: "/project/branch/create/list",
    method: "post",
    data
  });
}

// 查询主要研究者
export function getMainDoctorList(data) {
  return httpReq({
    url: "/project/doctor/query/page",
    method: "post",
    data
  });
}

// 方案配置

// 新建或编辑科研项目方案配置

export function createOrEditProjectScheme(data) {
  return httpReq({
    url: "/project/config/plan-config/edit",
    method: "post",
    data
  });
}

// 新建项目分组设置
export function createProjectGroup(data) {
  return httpReq({
    url: "/project/config/group-config/create",
    method: "post",
    data
  });
}

// 编辑项目分组设置
export function editProjectGroup(data) {
  return httpReq({
    url: "/project/config/group-config/edit",
    method: "post",
    data
  });
}
// 删除项目分组设置
export function deleteProjectGroup(data) {
  return httpReq({
    url: "/project/config/group-config/delete",
    method: "post",
    data
  });
}

// 获取所有产品服务包（需过滤硬件设备）
export function getAllProductsReq(data) {
  return httpReq({
    url: "/product/productList",
    method: "post",
    data
  });
}
// 获取服务包详情
export function getProductDetails(data) {
  return httpReq({
    url: "/product/getProductDetails",
    method: "post",
    data
  });
}

// 分页查询成员角色信息

export function getMemberRoleList(data) {
  return httpReq({
    url: "/project/branch/groups/members/query/page",
    method: "post",
    data
  });
}

// 编辑科研项团队配置
export function editProjectTeamConfig(data) {
  return httpReq({
    url: "/project/config/team-config/edit",
    method: "post",
    data
  });
}
// 获取项目CRF配置
export function getCRFConfigDetail(data) {
  return httpReq({
    url: "/project/config/crf/config/record/query",
    method: "post",
    data
  });
}

// 编辑（新增）项目CRF配置
export function editCRFConfig(data) {
  return httpReq({
    url: "/project/config/crf-config/edit",
    method: "post",
    data
  });
}

// 新建、编辑科研项目随机化方案配置

export function createOrEditRandomConfig(data) {
  return httpReq({
    url: "/project/config/random-config/edit",
    method: "post",
    data
  });
}

// 创建科研项目字典

export function createProjectDict(data) {
  return httpReq({
    url: "/project/config/dict-config/create",
    method: "post",
    data
  });
}
// 编辑科研项目字典
export function editProjectDict(data) {
  return httpReq({
    url: "/project/config/dict-config/edit",
    method: "post",
    data
  });
}
// 删除科研项目字典
export function deleteProjectDict(data) {
  return httpReq({
    url: "/project/config/dict-config/delete",
    method: "post",
    data
  });
}

// 编辑科研项目病例筛选配置
export function editProjectCaseFilterConfig(data) {
  return httpReq({
    url: "/project/config/case-config/edit",
    method: "post",
    data
  });
}

// 暂停科研项目
export function pauseProject(data) {
  return httpReq({
    url: "/project/suspend",
    method: "post",
    data
  });
}
// 重新开启科研项目
export function reopenProject(data) {
  return httpReq({
    url: "/project/restart",
    method: "post",
    data
  });
}

// 归档科研项目

export function archiveProject(data) {
  return httpReq({
    url: "/project/achieve",
    method: "post",
    data
  });
}

// 模糊查询医生(研究人员)

export function getDoctorList(data) {
  return httpReq({
    url: "/project/config/fuzzy/query/simple-doctor",
    method: "post",
    data
  });
}

// 查询工作室详情

export function getGroupDetailReq(data) {
  return httpReq({
    url: "/doctor/group/detail/query",
    method: "post",
    data
  });
}

// 工作室人员绑定信息

export function getGroupPersonBindReq(data) {
  return httpReq({
    url: "/project/branch/subCenter/group/binding/info",
    method: "post",
    data
  });
}
