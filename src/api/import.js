import { httpReq } from '@/http'

/**
 * 分页查询员工培训列表
 * @param {object} data 查询参数
 * @param {number | undefined} data.pageNum 页码
 * @param {number | undefined} data.pageSize 每页条数
 * @param {string | undefined} data.monthStart 开始月份
 * @param {string | undefined} data.monthEnd 结束月份
 * @returns
 */
export function fetchTrainingMaterialsList(data) {
  return httpReq({
    url: '/import/state/train/page',
    method: 'post',
    data,
  })
}

/**
 * 分页查询员工主观能动性列表
 * @param {object} data 查询参数
 * @param {number | undefined} data.pageNum 页码
 * @param {number | undefined} data.pageSize 每页条数
 * @param {string | undefined} data.monthStart 开始月份
 * @param {string | undefined} data.monthEnd 结束月份
 * @returns
 */
export function fetchProactiveList(data) {
  return httpReq({
    url: '/import/state/proactive/page',
    method: 'post',
    data,
  })
}

/**
 * 分页查询员工绩效列表
 * @param {object} data 查询参数
 * @param {number | undefined} data.pageNum 页码
 * @param {number | undefined} data.pageSize 每页条数
 * @param {string | undefined} data.monthStart 开始月份
 * @param {string | undefined} data.monthEnd 结束月份
 * @returns
 */
export function fetchPerformanceList(data) {
  return httpReq({
    url: '/import/state/perf/page',
    method: 'post',
    data,
  })
}

/**
 * 删除员工绩效
 * @param {number} id 员工绩效🆔
 */
export function deletePerformanceById(id) {
  return httpReq({
    url: '/import/state/perf/delete',
    method: 'post',
    data: { id },
  })
}
