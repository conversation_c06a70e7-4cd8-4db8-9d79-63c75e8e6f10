import { Hono } from 'hono'

const video = new Hono()

// POST /video 接口 - 接收视频URL并返回blob对象
video.post('/', async (c) => {
  try {
    // 获取请求体中的URL
    const body = await c.req.json()
    const { url } = body

    // 验证URL是否存在
    if (!url) {
      return c.json({ error: '缺少视频URL参数' }, 400)
    }

    // 验证URL格式
    let videoUrl: URL
    try {
      videoUrl = new URL(url)
    } catch (error) {
      return c.json({ error: '无效的URL格式' }, 400)
    }

    // 检查URL协议是否为http或https
    if (!['http:', 'https:'].includes(videoUrl.protocol)) {
      return c.json({ error: 'URL必须使用http或https协议' }, 400)
    }

    // 获取视频数据
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    })

    // 检查响应状态
    if (!response.ok) {
      return c.json({ 
        error: `获取视频失败: ${response.status} ${response.statusText}` 
      }, response.status)
    }

    // 检查内容类型是否为视频
    const contentType = response.headers.get('content-type')
    if (!contentType || !contentType.startsWith('video/')) {
      return c.json({ 
        error: 'URL指向的不是视频文件' 
      }, 400)
    }

    // 获取视频数据
    const videoBuffer = await response.arrayBuffer()
    
    // 设置响应头
    const headers = new Headers()
    headers.set('Content-Type', contentType)
    headers.set('Content-Length', videoBuffer.byteLength.toString())
    
    // 如果原始响应包含文件名信息，保留它
    const contentDisposition = response.headers.get('content-disposition')
    if (contentDisposition) {
      headers.set('Content-Disposition', contentDisposition)
    }

    // 添加CORS头
    headers.set('Access-Control-Allow-Origin', '*')
    headers.set('Access-Control-Allow-Methods', 'POST, OPTIONS')
    headers.set('Access-Control-Allow-Headers', 'Content-Type')

    // 返回视频blob
    return new Response(videoBuffer, {
      status: 200,
      headers
    })

  } catch (error) {
    console.error('视频处理错误:', error)
    return c.json({ 
      error: '服务器内部错误',
      details: error instanceof Error ? error.message : '未知错误'
    }, 500)
  }
})

// OPTIONS 请求处理 (CORS预检)
video.options('/', (c) => {
  return new Response(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400'
    }
  })
})

export { video }
