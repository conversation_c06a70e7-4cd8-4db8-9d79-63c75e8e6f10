import Vue from 'vue'
import dayjs from 'dayjs'

export function timeMode(time, symbol) {
  symbol = symbol ? symbol : "-";
  //  console.log(time)
  let dates = new Date(time);
  let year = String(dates.getFullYear());
  let month =
    String(dates.getMonth() + 1) < 10
      ? "0" + String(dates.getMonth() + 1)
      : String(dates.getMonth() + 1);
  let date =
    String(dates.getDate()) < 10
      ? "0" + String(dates.getDate())
      : String(dates.getDate());
  let hour =
    String(dates.getHours()) < 10
      ? "0" + String(dates.getHours())
      : String(dates.getHours());
  let minutes =
    String(dates.getMinutes()) < 10
      ? "0" + String(dates.getMinutes())
      : String(dates.getMinutes());
  let seconds =
    String(dates.getSeconds()) < 10
      ? "0" + String(dates.getSeconds())
      : String(dates.getSeconds());
  let datestr = year + symbol + month + symbol + date;
  let dateMonth = month + symbol + date;
  let dateYear = year;
  let getStartTime = year + symbol + month + symbol + date + " " + "00:00";
  let getEndTime = year + symbol + month + symbol + date + " " + "24:59";
  let dateMin =
    year +
    symbol +
    month +
    symbol +
    date +
    " " +
    hour +
    ":" +
    minutes +
    ":" +
    seconds;
  let dateMinu =
    year + symbol + month + symbol + date + " " + hour + ":" + minutes;
  return {
    datestr,
    dateMonth,
    dateYear,
    getStartTime,
    getEndTime,
    dateMin,
    dateMinu
  };
}

//身份证号合法性验证
//支持15位和18位身份证号
//支持地址编码、出生日期、校验位验证
export function checkIDCardByJS(idCard) {
  //15位和18位身份证号码的正则表达式
  var regIdCard = /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/;

  //如果通过该验证，说明身份证格式正确，但准确性还需计算
  if (regIdCard.test(idCard)) {
    if (idCard.length == 18) {
      var idCardWi = new Array(
        7,
        9,
        10,
        5,
        8,
        4,
        2,
        1,
        6,
        3,
        7,
        9,
        10,
        5,
        8,
        4,
        2
      ); //将前17位加权因子保存在数组里
      var idCardY = new Array(1, 0, 10, 9, 8, 7, 6, 5, 4, 3, 2); //这是除以11后，可能产生的11位余数、验证码，也保存成数组
      var idCardWiSum = 0; //用来保存前17位各自乖以加权因子后的总和
      for (var i = 0; i < 17; i++) {
        idCardWiSum += idCard.substring(i, i + 1) * idCardWi[i];
      }
      var idCardMod = idCardWiSum % 11; //计算出校验码所在数组的位置
      var idCardLast = idCard.substring(17); //得到最后一位身份证号码
      //如果等于2，则说明校验码是10，身份证号码最后一位应该是X
      if (idCardMod == 2) {
        if (idCardLast == "X" || idCardLast == "x") {
          console.log("恭喜通过验证啦！");
          return { flag: true, msg: "身份证号码格式正确" };
        } else {
          console.log("身份证号码错误！");
          return { flag: false, msg: "身份证号码格式错误" };
        }
      } else {
        //用计算出的验证码与最后一位身份证号码匹配，如果一致，说明通过，否则是无效的身份证号码
        if (idCardLast == idCardY[idCardMod]) {
          console.log("恭喜通过验证啦！");
          return { flag: true, msg: "身份证号码格式正确" };
        } else {
          console.log("身份证号码错误！");
          return { flag: false, msg: "身份证号码格式错误" };
        }
      }
    }
  } else {
    console.log("身份证格式不正确!");
    return { flag: false, msg: "身份证号码格式错误" };
  }
}

//手机号验证
export function phoneNumber(value) {
  console.log(value);
  if (
    value &&
    (!/^[1][3456789]\d{9}$/.test(value) ||
      !/^[1-9]\d*$/.test(value) ||
      value.length !== 11)
  ) {
    console.log("手机号码不符合规范");
    return { flag: false, msg: "手机号码格式不对" };
  } else {
    console.log("手机号码ok");
    return { flag: true, msg: "" };
  }
}

export function debounce(func, wait = 500, args) {
  //可以放入项目中的公共方法中进行调用
  console.log("jinru 防抖");
  let timeout;
  return function() {
    if (timeout) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(() => {
      this[fnName].apply(this, [...args, ...arguments]);
    }, time);
  };
}

//字符串去重
export function uniqueStr(str) {
  let arr = str.split("");
  let newArr = [...new Set(arr)];
  return newArr.join("");
}

/**
 * vue防抖，兼容vue3,vue2和普通js
 * delay：延迟时间（毫秒）
 * **/

export default class Debounce {
  constructor(delay) {
    this.delay = delay ? delay : 500;
    this.timeOut = null;
  }
  debounceEnd() {
    return new Promise((resolve, reject) => {
      if (this.timeout) {
        clearTimeout(this.timeout);
      }
      this.timeout = setTimeout(() => {
        resolve("success");
      }, this.delay);
    });
  }
}

/**
 *
 * @param dateNow :Date类newDta
 * @param intervalDays ：间隔天数
 * @param bolPastTime  ：Boolean,判断在参数date之前，还是之后，
 * @param singleDays  ：num,判断在参数date之前，还是之后，
 */
export function getDateRange(dateNow, intervalDays, bolPastTime, singleDays) {
  let oneDayTime = 24 * 60 * 60 * 1000;
  let list = [];
  let lastDay;

  if (bolPastTime == true) {
    lastDay = new Date(dateNow.getTime() - intervalDays * oneDayTime);
    list.push(formateDate(lastDay) + " 00:00:00");
    list.push(formateDate(dateNow) + " 23:59:59");
  } else {
    lastDay = new Date(dateNow.getTime() + intervalDays * oneDayTime);
    list.push(formateDate(dateNow) + " 00:00:00");
    list.push(formateDate(lastDay) + " 23:59:59");
  }
  return list;
}
function formateDate(time) {
  let year = time.getFullYear();
  let month = time.getMonth() + 1;
  let day = time.getDate();

  if (month < 10) {
    month = "0" + month;
  }

  if (day < 10) {
    day = "0" + day;
  }

  return year + "/" + month + "/" + day + "";
}

/**
 *
 * @param date :number 获取指定天数范围
 *
 */
export function getBeforeDateRange(date) {
  let s = [];
  let data = date;
  let d = new Date();
  let year = d.getFullYear();
  let mon = d.getMonth() + 1;
  let day = d.getDate();
  if (day <= data) {
    if (mon > 1) {
      mon = mon - 1;
    } else {
      year = year - 1;
      mon = 12;
    }
  }
  d.setDate(d.getDate() - data);
  year = d.getFullYear();
  mon = d.getMonth() + 1;
  day = d.getDate();
  s.push(
    year +
      "/" +
      (mon < 10 ? "0" + mon : mon) +
      "/" +
      (day < 10 ? "0" + day : day) +
      " 00:00:00"
  );
  s.push(
    year +
      "/" +
      (mon < 10 ? "0" + mon : mon) +
      "/" +
      (day < 10 ? "0" + day : day) +
      " 23:59:59"
  );
  return s;
}

// 数字补零
export function prefixInteger(num, n) {
  return (Array(n).join(0) + num).slice(-n);
}

//全局过滤器
Vue.filter("ellipsis", function(value) {
  if (!value) return "";
  if (value.length > 15) {
    return value.slice(0, 15) + "...";
  }
  return value;
});

export function getMapData(list,key='value',value='name') {
  let maps = new Map();
  list.forEach(item=>{
    maps.set(item[key],item[value]);
  })
  return Object.fromEntries(maps)
}

export function formatTime(minutes) {
  const hours = Math.floor(minutes / 60);
  const restMinutes = minutes % 60;
  if (hours > 0) {
    if (restMinutes===0){
      return `${hours}小时`
    }else {
      return `${hours}小时${restMinutes}分钟`;
    }
  } else {
    return `${minutes}分钟`
  }
}

/**
 * 格式化时间范围
 * @param {[string,string]} date 时间范围
 * @returns
 */
export function formatDateRange(date, formatStr = 'YYYYMM') {
  return {
    monthStart: date[0] ? Number(dayjs(date[0]).startOf('month').format(formatStr)) : undefined,
    monthEnd: date[1] ? Number(dayjs(date[1]).endOf('month').format(formatStr)) : undefined,
  }
}
