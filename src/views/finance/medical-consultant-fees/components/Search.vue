<template>
  <div>
    <!-- 所在地区 -->
    <el-form
      ref="searchFrom"
      size="mini"
      :inline="true"
      :model="searchFrom"
      class="demo-form-inline"
    >
      <el-form-item label="所在地区" v-if="type !== 3">
        <el-select
          v-model="searchFrom.regionId"
          filterable
          placeholder="请选择所在地区"
        >
          <el-option label="全部" value=""></el-option>
          <el-option
            v-for="(item, index) in allAddressList"
            :key="index"
            :label="item.name"
            :value="item.region_id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属医院" v-if="type !== 3">
        <el-select
          v-model="searchFrom.hospitalId"
          filterable
          placeholder="请选择医院"
        >
          <el-option label="全部" value=""></el-option>
          <el-option
            v-for="(item, index) in allHospitalList"
            :key="index"
            :label="item.name"
            :value="item.hospitalId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="工作室" v-if="type !== 3">
        <el-select
          v-model="searchFrom.groupId"
          filterable
          placeholder="请选择工作室"
        >
          <el-option label="全部" value=""></el-option>
          <el-option
            v-for="(item, index) in allGroupList"
            :key="index"
            :label="item.groupName"
            :value="item.groupId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="type === 3" label="公司名称">
        <el-select
          v-model="searchFrom.companyId"
          filterable
          placeholder="请选择公司名称"
        >
          <el-option label="全部" value=""></el-option>
          <el-option
            v-for="(item, index) in allCompanyList"
            :key="index"
            :label="item.companyName"
            :value="item.companyId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="type !== 1" label="医生">
        <el-select
          v-model="searchFrom.doctorId"
          filterable
          placeholder="请选择医生"
        >
          <el-option label="全部" value=""></el-option>
          <el-option
            v-for="(item, index) in allDoctorList"
            :key="index"
            :label="item.doctorName"
            :value="item.doctorId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="type === 2" label="是否正常发放">
        <el-select
          v-model="searchFrom.normal"
          filterable
          placeholder="请选择是否正常发放"
        >
          <el-option label="正常" value="0"></el-option>
          <el-option label="延迟" value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">查询</el-button>
        <el-button @click="resetForm('searchFrom')">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { httpReq } from "@/http";
export default {
  data() {
    return {
      searchFrom: {},
      companyLocation: "",
      allHospitalList: [],
      allGroupList: [],
      allDoctorList: [],
      allCompanyList: [],
      allAddressList: []
    };
  },
  mounted() {
    this.getAllHosoitalList();
    this.getAllGroupList();
    this.getHospitalRegion();
    if (this.type != 1) this.getAllDoctor();
    if (this.type == 3) this.getAllCompanyList();
  },
  props: ["type"], // 1--工作室明细  2--医生明细  3--发放汇总
  methods: {
    // 查询
    onSubmit() {
      this.$emit("queryData", this.searchFrom);
    },

    // 重置
    resetForm() {
      this.searchFrom = {};
      this.companyLocation = "";
      this.$emit("resetData");
    },

    //地区切换
    handleChange(e) {
      this.searchFrom.province = e[0];
      this.searchFrom.city = e.length == 1 ? "" : e[1];
    },

    // 获取所有医院列表
    getAllHosoitalList() {
      httpReq({
        url: "/doctors/getHospitalList"
      }).then(res => {
        this.allHospitalList = res.data;
      });
    },

    // 获取所有工作室列表
    getAllGroupList() {
      httpReq({
        url: "/doctors/getGroupList"
      }).then(res => {
        this.allGroupList = res.data;
      });
    },

    // 获取地区
    getHospitalRegion() {
      httpReq({
        url: "/hospital/getRegion"
      }).then(res => {
        this.allAddressList = res.data;
      });
    },

    // 获取所有医生
    getAllDoctor() {
      httpReq({
        url: "/order/allDoctor"
      }).then(res => {
        this.allDoctorList = res.data;
      });
    },

    //获取所有公司列表
    getAllCompanyList() {
      httpReq({
        url: "/company/getAllCompanys"
      }).then(res => {
        this.allCompanyList = res.data;
      });
    }
  }
};
</script>
