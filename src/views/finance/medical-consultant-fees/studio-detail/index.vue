<template>
  <div class="index">
    <div class="search">
      <Search @queryData="queryData" @resetData="resetData" :type="1" />
      <Export @exportData="exportData" :closeDialog="closeDialog" />
    </div>
    <div class="content">
      <div class="tree">
        <TimeTree @handleNodeClick="handleNodeClick" />
      </div>
      <div class="table">
        <el-table :data="tableData" size="mini" max-height="530" ref="table">
          <el-table-column align="center" label="序号" width="70">
            <template slot-scope="scope">
              &nbsp;{{
                pageInfo.currentPage === 1
                  ? prefixInteger(
                      (pageInfo.currentPage - 1) * pageInfo.pageSize +
                        scope.$index +
                        1,
                      2
                    )
                  : (pageInfo.currentPage - 1) * pageInfo.pageSize +
                    scope.$index +
                    1
              }}
            </template>
          </el-table-column>
          <el-table-column prop="regionName" align="center" label="所在地区">
          </el-table-column>
          <el-table-column prop="hospitalName" align="center" label="所在医院">
          </el-table-column>
          <el-table-column prop="groupName" align="center" label="工作室">
          </el-table-column>
          <el-table-column
            prop="ordinaryPurchaseNum"
            align="center"
            label="新购"
          >
            <template slot-scope="scope">
              {{ scope.row.ordinaryPurchaseNum }}笔
            </template>
          </el-table-column>
          <el-table-column prop="ordinaryRenewNum" align="center" label="续费">
            <template slot-scope="scope">
              {{ scope.row.ordinaryRenewNum }}笔
            </template>
          </el-table-column>
          <el-table-column align="center" label="转入">
            <el-table-column
              prop="transferInPurchaseNum"
              align="center"
              label="新购"
            >
              <template slot-scope="scope">
                {{ scope.row.transferInPurchaseNum }}笔
              </template>
            </el-table-column>
            <el-table-column
              prop="transferInRenewNum"
              align="center"
              label="续费"
            >
              <template slot-scope="scope">
                {{ scope.row.transferInRenewNum }}笔
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column align="center" label="退费">
            <el-table-column
              prop="refundPurchaseNum"
              align="center"
              label="新购"
            >
              <template slot-scope="scope">
                {{ scope.row.refundPurchaseNum }}笔
              </template>
            </el-table-column>
            <el-table-column prop="refundRenewNum" align="center" label="续费">
              <template slot-scope="scope">
                {{ scope.row.refundRenewNum }}笔
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column align="center" label="转出">
            <el-table-column
              prop="transferOutPurchaseNum"
              align="center"
              label="新购"
            >
              <template slot-scope="scope">
                {{ scope.row.transferOutPurchaseNum }}笔
              </template>
            </el-table-column>
            <el-table-column
              prop="transferOutRenewNum"
              align="center"
              label="续费"
            >
              <template slot-scope="scope">
                {{ scope.row.transferOutRenewNum }}笔
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column
            prop="currentDeductionFee"
            align="center"
            label="本期抵扣费用"
          >
            <template slot-scope="scope">
              {{ scope.row.currentDeductionFee }}元
            </template>
          </el-table-column>
          <el-table-column
            prop="consultFee"
            align="center"
            label="工作室顾问费"
          >
            <template slot-scope="scope">
              {{ scope.row.consultFee.toLocaleString() }}元
            </template>
          </el-table-column>
          <el-table-column
            prop="operation"
            align="left"
            label="操作"
            width="90"
          >
            <template slot-scope="scope">
              <div class="edit-btns">
                <el-button
                  @click="queryDetails(scope.row)"
                  type="text"
                  size="small"
                >
                  详情
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <Pagination @changePagination="changePagination" :pageInfo="pageInfo" />
      </div>
    </div>
  </div>
</template>
<script>
import Search from "../components/Search.vue";
import Export from "../components/Export.vue";
import TimeTree from "../components/TimeTree.vue";
import Pagination from "../components/Pagination.vue";
import { httpReq } from "@/http";
import { prefixInteger } from "@/util/util";
export default {
  components: {
    Search,
    Export,
    TimeTree,
    Pagination
  },
  data() {
    return {
      tableData: [],
      prefixInteger,
      pageInfo: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      queryInfo: {},
      timeData: "",
      closeDialog: true
    };
  },
  methods: {
    // 查询
    queryData(val) {
      this.pageInfo = {
        currentPage: 1,
        pageSize: 10,
        total: 0
      };
      this.queryInfo = val;
      this.getList();
    },

    getList() {
      let { pageSize, currentPage } = this.pageInfo;
      let { groupId, hospitalId, regionId } = this.queryInfo;
      httpReq({
        url: "/group/fee/list",
        method: "post",
        data: {
          pageNumber: currentPage,
          pageSize,
          groupId,
          regionId,
          hospitalId,
          queryDate: this.timeData
        }
      }).then(res => {
        this.tableData = res.data.contents;
        this.pageInfo.total = res.data.total;
      });
    },

    // 重置
    resetData() {
      this.pageInfo = {
        currentPage: 1,
        pageSize: 10,
        total: 50
      };
      this.queryInfo = {};
      this.getList();
    },

    // 导出
    exportData(val) {
      this.exportDataTips(val);
    },

    //当前页面下载
    dowloadFiles(url) {
      const link = document.createElement("a");

      link.setAttribute("download", "xxx.xlsx");

      link.setAttribute("href", url);

      link.style.display = "none";

      document.body.appendChild(link);

      link.click();

      link.remove();
    },

    // 导出数据提示
    exportDataTips(val) {
      let allYearMonth = this.getAllMonthsBetween(val[0], val[1]);
      httpReq({
        url: "/group/fee/export/date/list",
        method: "post",
        data: {
          exportStartTime: val[0],
          exportEndTime: val[1]
        }
      }).then(res => {
        if (res.code == 1) {
          let arr = res.data.map(item => {
            let str = String(item);
            return str.slice(0, 6);
          });
          if (allYearMonth.length === arr.length) {
            this.$message({
              message: "当前区间无数据，无需导出！",
              type: "warning"
            });
          } else {
            const params = {
              exportStartTime: val[0],
              exportEndTime: val[1]
            };
            let dataArr = [];
            for (let i in params) {
              dataArr.push(`${i}=${params[i] || ""}`);
            }
            let url = `${
              process.env.VUE_APP_baseUrl
            }group/fee/export/list?${dataArr.join("&")}`;
            this.dowloadFiles(url);

            let message =
              "导出成功！" + (arr.length ? `${arr.join(",")}无数据` : "");
            this.$message({
              message,
              type: "success"
            });
            this.closeDialog = false;
          }
        }
      });
    },

    // 获取指定年月之间的所有年月
    getAllMonthsBetween(startDateStr, endDateStr) {
      const startDate = new Date(startDateStr + "-01"); // 设置日期为当月的第一天
      const endDate = new Date(endDateStr + "-01"); // 设置日期为当月的第一天
      endDate.setMonth(endDate.getMonth()); // 将结束日期设置为下一个月的第一天，以确保包含结束月份

      // 确保起始日期小于或等于结束日期
      if (startDate > endDate) {
        throw new Error("起始日期必须小于或等于结束日期");
      }

      let currentMonth = new Date(startDate);
      let allMonths = [];

      // 添加起始年月到数组
      allMonths.push(startDate.toISOString().slice(0, 7));

      // 当前月份加一个月，直到达到或超过结束月份
      while (currentMonth < endDate) {
        currentMonth.setMonth(currentMonth.getMonth() + 1);
        allMonths.push(currentMonth.toISOString().slice(0, 7));
      }

      return allMonths;
    },

    // 时间树
    handleNodeClick(data) {
      this.timeData = data;
      this.getList();
    },

    // 分页
    changePagination(data) {
      this.pageInfo = data;
      this.getList();
    },

    // 详情
    queryDetails(val) {
      this.$router.push({
        path: "/index/studio-detail-details",
        query: {
          groupId: val.groupId,
          timeData: this.timeData
        }
      });
    }
  }
};
</script>
<style lang="less" scoped>
.index {
  text-align: left;
  .search {
    background: #fff;
    width: 100%;
    margin: 0 24px;
    padding: 20px;
    border-radius: 5px;
  }
  .content {
    margin: 24px;
    background: #fff;
    border-top: 1px solid #f5f7fa;
    display: flex;
    border-radius: 5px;
    .tree {
      width: 200px;
      border-right: 1px solid #f5f7fa;
      padding-top: 24px;
    }
    .table {
      padding: 24px;
      width: calc(100% - 250px);
    }
  }
}
</style>
