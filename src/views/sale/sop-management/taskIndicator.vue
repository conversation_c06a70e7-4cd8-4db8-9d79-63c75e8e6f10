<template>
  <div class="staff-wrapper">
    <div class="seach-box box">
      <el-form
        ref="searchFrom"
        :inline="true"
        :model="searchFrom"
        class="demo-form-inline"
        size="mini"
      >
        <el-form-item label="健康顾问">
          <el-select
            v-model="searchFrom.sellerId"
            filterable
            placeholder="请选择健康顾问"
          >
            <el-option
              v-for="item in allSellerList"
              :key="item.seller_id"
              :label="item.seller_name"
              :value="item.seller_id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="地区">
          <el-select
            v-model="searchFrom.regionId"
            filterable
            placeholder="请选择地区"
          >
            <el-option
              v-for="item in searchCityList"
              :key="item.region_id"
              :label="item.name"
              :value="item.region_id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="医院">
          <el-select
            v-model="searchFrom.hospitalId"
            filterable
            placeholder="请选择医院"
          >
            <el-option
              v-for="(item, index) in allHospitalList"
              :key="index"
              :label="item.name"
              :value="item.hospitalId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="minclass2" label="指标月份">
          <el-date-picker
            v-model="searchFrom.quotaDate"
            type="month"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="制定人">
          <el-select
            v-model="searchFrom.enactSellerId"
            filterable
            placeholder="请选择制定人"
          >
            <el-option
              v-for="item in allSellerList"
              :key="item.seller_id"
              :label="item.seller_name"
              :value="item.seller_id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="minclass2" label="制定时间">
          <el-date-picker
            v-model="searchFrom.createDate"
            :picker-options="pickerOptions"
            align="right"
            end-placeholder="结束日期"
            range-separator="至"
            start-placeholder="开始日期"
            type="daterange"
            unlink-panels
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchFrom.status"
            filterable
            placeholder="请选择状态"
          >
            <el-option label="待审核" value="1"></el-option>
            <el-option label="已通过" value="2"></el-option>
            <el-option label="已驳回" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">查询</el-button>
          <el-button @click="resetForm('searchFrom')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="data-box box">
      <div class="table-box">
        <div class="table">
          <el-table :data="tableData" max-height="750" style="width: 100%">
            <el-table-column
              align="center"
              label="序号"
              prop="userId"
              width="50"
            >
              <template slot-scope="scope">
                {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="健康顾问" prop="sellerName">
            </el-table-column>
            <el-table-column align="center" label="地区" prop="regionName">
            </el-table-column>
            <el-table-column
              align="center"
              label="医院"
              prop="hospitalName"
              width="180"
            >
            </el-table-column>
            <el-table-column align="center" label="指标月份" prop="year">
              <template slot-scope="scope">
                {{
                  scope.row.year +
                    "-" +
                    (scope.row.month < 10
                      ? "0" + scope.row.month
                      : scope.row.month)
                }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="当月指标" prop="quota">
            </el-table-column>
            <el-table-column align="center" label="达成数" prop="orderNum">
            </el-table-column>
            <el-table-column align="center" label="达成率" prop="orderNum">
              <template slot-scope="scope">
                {{
                  scope.row.quota
                    ? Math.round((scope.row.orderNum / scope.row.quota) * 100) +
                      "%"
                    : "0%"
                }}
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="制定时间"
              prop="enactDate"
              width="180"
            >
            </el-table-column>
            <el-table-column
              align="center"
              label="制定人"
              prop="enactSellerName"
            >
            </el-table-column>
            <el-table-column align="center" label="当前状态" prop="status">
              <template slot-scope="scope">
                {{ getPlanStatus(scope.row.status) }}
              </template>
            </el-table-column>

            <el-table-column
              align="center"
              fixed="right"
              label="操作"
              width="180"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="queryDetails(scope.row)"
                  >详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          :current-page.sync="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          :total="total"
          layout="total,sizes, prev, pager, next"
          size="mini"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>

    <el-dialog :visible.sync="dialogVisible" title="任务指标详情" width="60%">
      <div>
        <el-descriptions :column="2" title="基础信息">
          <el-descriptions-item label="健康顾问">{{
            detailsInfo.sellerName
          }}</el-descriptions-item>
          <el-descriptions-item label="当前地区">{{
            detailsInfo.regionName
          }}</el-descriptions-item>
          <el-descriptions-item label="当前医院">{{
            detailsInfo.hospitalName
          }}</el-descriptions-item>
          <el-descriptions-item label="指标月份">{{
            detailsInfo.year +
              "-" +
              (detailsInfo.month < 10
                ? "0" + detailsInfo.month
                : detailsInfo.month)
          }}</el-descriptions-item>
          <el-descriptions-item label="指标数量"
            >{{ detailsInfo.quota }}
            <span class="remark">顾问需在当月完成的成交量</span>
          </el-descriptions-item>
          <el-descriptions-item label="制定人">{{
            detailsInfo.enactSellerName
          }}</el-descriptions-item>
          <el-descriptions-item label="制定时间">{{
            detailsInfo.enactDate
          }}</el-descriptions-item>
          <el-descriptions-item label="审核人">{{
            detailsInfo.auditorSellerName || "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="审核时间">{{
            detailsInfo.disposeTime || "--"
          }}</el-descriptions-item>
          <el-descriptions-item label="状态">{{
            getPlanStatus(detailsInfo.status)
          }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions :column="2" title="达成情况">
          <el-descriptions-item label="当月达成数">{{
            detailsInfo.orderNum
          }}</el-descriptions-item>
          <el-descriptions-item label="指标达成率">
            {{
              detailsInfo.quota
                ? Math.round((detailsInfo.orderNum / detailsInfo.quota) * 100) +
                  "%"
                : "0%"
            }}
            <span class="remark">达成数/指标数量*100%</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { httpReq } from "@/http";
import { timeMode } from "@/util/util";

export default {
  name: "Patient",
  data() {
    return {
      timeMode,
      searchFrom: {
        sellerId: null,
        regionId: null,
        hospitalId: null,
        createDate: [], //制定时间
        quotaDate: null,
        enactSellerId: null,
        status: null
      },
      dialogVisible: false,
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 1,
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      },
      allHospitalList: [],
      detailsInfo: {},
      allSellerList: [],
      searchCityList: []
    };
  },

  mounted() {
    this.getPatientList(this.currentPage, this.pageSize, this.searchFrom);
    this.getAllHosoitalList();
    this.getAllSellerList();
    this.getAllCitys();
  },

  methods: {
    onSubmit() {
      this.getPatientList(1, this.pageSize, this.searchFrom);
    },

    //获取所有地区
    getAllCitys() {
      httpReq({
        url: "/hospital/getRegion"
      }).then(res => {
        this.searchCityList = res.data;
      });
    },

    // 获取所有销售列表
    getAllSellerList() {
      httpReq({
        url: "/order/getAllSeller"
      }).then(res => {
        this.allSellerList = res.data;
      });
    },

    // 查看详情
    queryDetails(item) {
      httpReq({
        url: "/seller/quota/query",
        method: "post",
        data: { sellerQuotaId: item.sellerQuotaId }
      }).then(res => {
        let { code, data } = res;

        if (code == 1) {
          this.detailsInfo = data;
          this.dialogVisible = true;
        }
      });
    },
    resetForm(formName) {
      this.searchFrom = {
        sellerId: null,
        hospitalId: null,
        createDate: [], //制定时间
        quotaDate: null,
        regionId: null,
        enactSellerId: null,
        status: null
      };
      this.getPatientList(this.currentPage, this.pageSize, this.searchFrom);
    },

    // 切换每页条数
    handleSizeChange(val) {
      this.pageSize = val;
      this.getPatientList(1, this.pageSize, this.searchFrom);
    },
    // 切换页码
    handleCurrentChange(val) {
      this.getPatientList(val, this.pageSize, this.searchFrom);
    },
    // 获取列表
    getPatientList(page, pageSize, info) {
      let data = info;
      data.pageNumber = page;
      data.pageSize = pageSize;
      data.enactStartDate = info.createDate.length
        ? timeMode(info.createDate[0]).datestr
        : "";
      data.enactEndDate = info.createDate.length
        ? timeMode(info.createDate[1]).datestr
        : "";
      httpReq({
        url: "/seller/quota/list",
        method: "post",
        data
      }).then(res => {
        this.tableData = res.data.data;
        this.total = res.data.records;
      });
    },
    // 获取所有医院列表
    getAllHosoitalList() {
      httpReq({
        url: "/doctors/getHospitalList"
      }).then(res => {
        this.allHospitalList = res.data;
      });
    }
  },
  computed: {
    // 获取状态
    getPlanStatus() {
      return function(status) {
        let title = "";
        if (status == 1) {
          title = "待审核";
        }
        if (status == 2) {
          title = "已通过";
        }
        if (status == 3) {
          title = "已驳回";
        }
        return title;
      };
    }
  }
};
</script>
<style lang="less" scoped>
.staff-wrapper {
  text-align: left;
  display: flex;
  flex-direction: column;

  .box {
    background: #fff;
    margin: 0 24px;
    padding: 16px;
    border-radius: 5px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    margin-bottom: 20px;
  }

  .seach-box {
    text-align: left;

    /deep/ .el-form {
      .minclass .el-form-item__content {
        width: 120px;
        margin-right: 10px;

        .el-date-editor.el-input {
          width: 120px;
        }
      }

      .minclass2 {
        .el-input__inner {
          width: 220px;
        }
      }
    }
  }

  .data-box {
    flex: 1;
    display: flex;
    flex-direction: column;

    .add-btn {
      text-align: left;
      margin-bottom: 10px;
    }

    .table-box {
      flex: 1;
      display: flex;
      flex-direction: column;

      .table {
        margin-bottom: 10px;
        flex: 1;

        .el-table {
        }

        .el-table__body-wrapper {
        }
      }

      .el-pagination {
        text-align: right;
      }
    }
  }
}

/deep/ .el-dialog__body {
  padding: 10px 20px;
}

.el-descriptions {
  margin-bottom: 24px;
}

/deep/ .el-descriptions-item__cell {
  padding-left: 66px;
  padding-bottom: 16px;

  .remark {
    margin-left: 30px;
    font-size: 12px;
    color: #999;
  }
}
</style>
