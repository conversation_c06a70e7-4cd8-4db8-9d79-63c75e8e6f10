<!-- 药品库 -->
<template>
  <div class="drug-storage">
    <div class="search-box box">
      <el-form ref="searchFrom" size="mini" :inline="true" :model="searchFrom">
        <el-form-item label="分类搜索" class="minclass">
          <el-input
            v-model="searchFrom.keyword"
            placeholder="请输入分类名称"
          ></el-input>
        </el-form-item>
        <span style="line-height: 40px">
          <el-button type="primary" @click="onSearch" class="search-btn"
            >查询</el-button
          >
          <el-button @click="resetForm" class="reset-btn">重置</el-button>
        </span>
      </el-form>
    </div>
    <div class="function-box">
      <el-button
        type="primary"
        size="medium"
        class="buttons2"
        @click="handleOperation(0)"
        >新建分类</el-button
      >
    </div>
    <div class="data-box box">
      <div class="table">
        <el-table
          :data="drugCategoryList"
          size="mini"
          style="width: 100%"
          max-height="530"
          ref="table"
        >
          <el-table-column align="center" label="序号" width="60">
            <template slot-scope="scope">
              {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="className" align="center" label="药品分类名称">
          </el-table-column>
          <el-table-column prop="describe" align="center" label="描述">
          </el-table-column>
          <el-table-column
            prop="operation"
            align="center"
            label="操作"
            width="150"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="handleOperation(1, scope.row)"
                v-if="scope.row.className !== '其它类'"
                >修改名称</el-button
              >
              <el-button @click="lookDetail(scope.row)" type="text" size="small"
                >查看</el-button
              >
              <el-button
                @click="handleOperation(2, scope.row)"
                type="text"
                size="small"
                v-if="scope.row.className !== '其它类'"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-sizes="[10, 20, 50]"
        :page-size="pageSize"
        layout="total,sizes, prev, pager, next"
        :total="total"
        size="mini"
      >
      </el-pagination>
    </div>
    <el-dialog
      :visible.sync="dialogFormVisibleEditBox"
      class="self-dialog"
      :show-close="false"
      @close="closeEditNameDialog"
    >
      <span slot="title" class="show-title">
        {{
          dialogType === 0
            ? "新建分类"
            : dialogType === 1
            ? "修改名称"
            : "删除分类"
        }}
      </span>
      <div class="edit-box">
        <el-form
          size="mini"
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="100px"
          class="self-rule-from"
          v-if="dialogType === 0"
        >
          <el-form-item label="分类名称" prop="className">
            <el-input
              v-model.trim="ruleForm.className"
              placeholder="请输入分类名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="描述" prop="describe">
            <el-input
              v-model.trim="ruleForm.describe"
              placeholder="请输入分类描述"
            ></el-input>
          </el-form-item>
        </el-form>
        <el-form
          size="mini"
          :model="changeNameForm"
          ref="changeNameForm"
          label-width="100px"
          class="self-rule-from"
          v-if="dialogType === 1"
        >
          <el-form-item label="原名称" prop="name" class="">
            <el-input v-model="changeNameForm.oldName" disabled></el-input>
          </el-form-item>
          <el-form-item label="新名称" prop="introduction">
            <el-input
              v-model="changeNameForm.className"
              placeholder="请输入新名称"
            ></el-input>
          </el-form-item>
        </el-form>
        <div v-if="dialogType === 2" class="delete-text">
          是否确认删除该分类: {{ this.changeNameForm.oldName }}
        </div>
        <div class="sub-btn">
          <el-button size="mini" type="primary" @click="submitForm(dialogType)"
            >保存</el-button
          >
          <el-button size="mini" @click="dialogFormVisibleEditBox = false"
            >取消</el-button
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { httpReq } from "@/http";
export default {
  name: "drag-category",
  components: {},
  data() {
    return {
      searchFrom: {
        keyword: ""
      },
      ruleForm: {
        className: "",
        describe: ""
      },
      changeNameForm: {
        drugType: "",
        className: "",
        oldName: ""
      },
      rules: {
        className: [
          { required: true, message: "请输入分类名称", trigger: "blur" }
        ],
        describe: [
          { required: true, message: "请输入分类描述", trigger: "blur" }
        ]
      },
      //0新增  1修改名称   2删除
      dialogType: 0,
      dialogFormVisibleEditBox: false,
      drugCategoryList: [],
      currentPage: 1,
      pageSize: 10,
      total: 1,
      currentDrugType: ""
    };
  },
  computed: {},
  watch: {},
  created() {
    if (sessionStorage.getItem("lastRouterInfo") != null) {
      let { page, pageSize, router, searchFrom } = JSON.parse(
        sessionStorage.getItem("lastRouterInfo")
      );
      if (router == this.$route.path) {
        this.currentPage = page;
        this.pageSize = pageSize;
        this.searchFrom = searchFrom;
      } else {
        sessionStorage.removeItem("lastRouterInfo");
      }
    }
  },
  mounted() {
    this.getDrugCategoryList(this.currentPage, this.pageSize, this.searchFrom);
  },
  methods: {
    //搜索
    onSearch() {
      this.getDrugCategoryList(1, this.pageSize, this.searchFrom);
    },
    //重置
    resetForm() {
      this.searchFrom = {
        keyword: ""
      };
      this.getDrugCategoryList(
        this.currentPage,
        this.pageSize,
        this.searchFrom
      );
    },
    lookDetail(row) {
      this.$router.push({
        path: "/index/DrugLibraryCateDetails",
        query: row
      });
    },
    //请求列表
    getDrugCategoryList(page, pageSize, info) {
      httpReq({
        url: "/drug/type/getList",
        method: "post",
        data: {
          ...info,
          page,
          pageSize
        }
      }).then(res => {
        console.log(res, "------");
        this.setInfo();
        this.drugCategoryList = res.data.data;
        this.total = res.data.records;
      });
    }, //存储搜索条件
    setInfo() {
      let info = {
        pageSize: this.pageSize,
        page: this.currentPage,
        searchFrom: this.searchFrom,
        router: this.$route.path
      };
      sessionStorage.setItem("lastRouterInfo", JSON.stringify(info));
    },
    // 切换每页条数
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.pageSize = val;
      this.getDrugCategoryList(1, this.pageSize, this.searchFrom);
    },
    // 切换页码
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.getDrugCategoryList(val, this.pageSize, this.searchFrom);
    },
    //处理操作
    handleOperation(type, row = {}) {
      this.dialogType = type;
      this.dialogFormVisibleEditBox = true;
      if (Object.keys(row).length) {
        this.changeNameForm.oldName = row.className;
        this.changeNameForm.drugType = row.drugType;
        this.currentDrugType = row.drugType;
      }
    },
    submitForm(type) {
      if (type === 0) {
        console.log(type);
        this.$refs["ruleForm"].validate(valid => {
          if (valid) {
            console.log(valid);
            this.addDrugCategory(this.ruleForm);
          } else {
            console.log("error submit!!");
            this.$message.warning("请填写完整!");
            return false;
          }
        });
      }
      if (type === 1) {
        console.log(type);
        if (this.changeNameForm.className) {
          let { oldName, ...obj } = this.changeNameForm;
          this.changDrugClassName(obj);
        } else {
          this.$message.warning("请填写完整!");
        }
      }
      if (type === 2) {
        console.log(type);
        this.deleteDrugCategory(this.currentDrugType);
      }
    },
    //新增分类
    addDrugCategory(info) {
      httpReq({
        url: "/drug/type/add",
        method: "post",
        data: info
      })
        .then(res => {
          this.$message.success(`添加分类成功!`);
          this.$refs["ruleForm"].resetFields();
          this.dialogFormVisibleEditBox = false;
          this.getDrugCategoryList(
            this.currentPage,
            this.pageSize,
            this.searchFrom
          );
        })
        .catch(err => {
          this.$message.error(`添加分类失败:${err.msg}`);
        });
    },
    //修改分类名称
    changDrugClassName(info) {
      httpReq({
        url: "/drug/type/updateName",
        method: "post",
        data: info
      })
        .then(res => {
          this.$message.success(`修改分类名称成功!`);
          this.$refs["changeNameForm"].resetFields();
          this.dialogFormVisibleEditBox = false;
          this.getDrugCategoryList(
            this.currentPage,
            this.pageSize,
            this.searchFrom
          );
        })
        .catch(err => {
          this.$message.error(`修改分类名称失败:${err.msg}`);
        });
    },
    //删除分类
    deleteDrugCategory(drugType) {
      httpReq({
        url: "/drug/type/delete",
        method: "delete",
        data: {
          drugType
        }
      })
        .then(res => {
          this.$message.success(`删除分类成功!`);
          this.dialogFormVisibleEditBox = false;
          this.getDrugCategoryList(
            this.currentPage,
            this.pageSize,
            this.searchFrom
          );
        })
        .catch(err => {
          this.$message.error(`删除分类失败:${err.msg}`);
        });
    },
    closeEditNameDialog() {
      this.changeNameForm.className = "";
    }
  }
};
</script>
<style></style>
<style lang="less" scoped>
.drug-storage {
  display: flex;
  flex-direction: column;
  /deep/.el-dialog__header {
    padding: 0;
  }
  /deep/.el-dialog--center {
    margin-top: 20vh !important;
  }
  /deep/.el-dialog__wrapper.self-dialog {
    .el-dialog {
      width: 568px;
      height: 275px;
      background: #ffffff;
      box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      border: 1px solid #e4e7ed;
      box-sizing: border-box;
      margin-top: 30vh !important;
      .el-dialog__header {
        font-size: 18px;
        text-align: left;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #303133;
        padding: 20px 20px 16px;
        border-bottom: 1px solid #ebeef5;
      }
      .el-dialog__body {
        padding: 16px 20px;
      }
    }
  }
  .search-box {
    box-sizing: border-box;
    text-align: left;
    display: flex;
    align-items: center;
    /deep/.el-form {
      .el-form-item {
        margin: 0 20px 0 0;
      }
      .minclass .el-form-item__content {
        .el-date-editor.el-input {
          width: 120px;
        }
        .el-input__inner {
          height: 40px;
          font-size: 14px;
        }
      }
      .minclass .el-form-item__label {
        line-height: 40px;
        font-size: 14px;
        color: #000000;
      }
      .minclass .el-icon-arrow-up:before {
        content: "";
      }
      .minclass2 {
        .el-input__inner {
          width: 220px;
        }
      }
    }
  }
  .function-box {
    height: 40px;
    margin: 0 24px;
    text-align: left;
  }
  .data-box {
    flex: 1;
    display: flex;
    flex-direction: column;
    .table {
      margin-bottom: 10px;
      flex: 1;
    }
    .el-pagination {
      text-align: right;
    }
  }
  .box {
    background: #fff;
    padding: 20px;
    border-radius: 5px;
    margin: 8px 24px 20px;
  }
}
.search-btn {
  width: 88px;
  height: 32px;
  color: #ffffff;
  font-size: 12px;
  background: #0c88be;
  border-radius: 3px;
  padding: 0;
  &:first-of-type {
    margin: 0 16px 0 20px;
  }
}
.reset-btn {
  width: 88px;
  height: 32px;
  font-size: 12px;
  border-radius: 3px;
  padding: 0;
}
.reset-btn {
  width: 88px;
  height: 32px;
  font-size: 12px;
  border-radius: 3px;
  padding: 0;
}
.buttons {
  margin-bottom: 20px;
  background-color: #0c88be;
  width: 88px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.buttons2 {
  background-color: #0c88be;
}
///deep/.el-table thead {
//  font-size: 14px;
//}
///deep/.el-table--mini {
//  font-size: 14px;
//}
///deep/ .el-table .cell {
//  line-height: 48px;
//}
.table {
  /deep/.el-table {
    width: 100%;
    .el-table__header,
    .el-table__body {
      // table-layout: auto;
      width: 100%;
    }
    &::before {
      height: 0;
    }
    th,
    td {
      height: 50px;
      font-size: 14px;
      padding: 0;
      border-bottom: 1px solid #ebeef5;
      box-sizing: border-box;
      .cell {
        padding: 0;
      }
    }
    th .cell {
      color: #909399;
    }
    td .cell {
      color: #606266;
      .edit-btns {
        width: 100%;
        .el-button {
          color: #0c88be;
          font-size: 14px;
          margin: 0;
          box-sizing: border-box;
          padding: 0;
          &:nth-of-type(2) {
            padding-left: 6px;
            border-left: 1px solid #d8d8d8;
          }
          &:last-of-type {
            color: #f56c6c;
          }
        }
      }
    }
    .index-item {
    }
  }
}
.delebutton {
  color: red;
  border-left: 1px solid #d8d8d8;
}
.detailbutton {
  color: #0c88be;
}
.rowbtn {
  padding: 0 10px;
  line-height: 16px;
}
.btns {
  display: flex;
  align-items: center;
}
.dialog-footer {
  display: flex;
  justify-content: center;
}
.buttonscancel {
  margin-bottom: 20px;
  width: 88px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}
/deep/.selectbox .el-icon-arrow-up:before {
  content: "";
}
.edit-box {
  /deep/.el-form.self-rule-from {
    margin-bottom: 32px;
    .el-form-item__label {
      width: 86px !important;
      height: 40px;
      line-height: 40px;
      padding-right: 20px;
    }
    .el-form-item__content {
      width: 442px;
      height: 40px;
      line-height: 40px;
      border-radius: 4px;
      margin-left: 86px !important;
      .el-input__inner {
        width: 100%;
        height: 40px;
        line-height: 40px;
        padding: 13px 15px;
        font-size: 14px;
        &::placeholder {
          color: #909399;
        }
        // border-radius: 4px;
      }
    }
  }
  .sub-btn {
    text-align: center;
    padding-right: 24px;
    margin-bottom: 10px;
    .el-button {
      width: 104px;
      height: 36px;
      font-size: 14px;
      color: #ffffff;
      background: #0c88be;
      border-radius: 4px;
      margin-left: 20px;
      &:last-of-type {
        color: #303133;
        background: #fff;
        border: 1px solid #dcdfe6;
      }
    }
  }
}
.delete-text {
  width: 100%;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}
</style>
