<!-- 药品详情 -->
<template>
  <div class="drug-details">
    <div class="drug-save box">
      <div class="top-left">
        <img :src="ruleForm.pictureUrl" alt="" class="logo" />
        <span>{{ ruleForm.value }}</span>
      </div>
      <div class="top-right">
        <el-button type="primary" class="btn-right" @click="saveDetail"
          >保存</el-button
        >
        <el-button
          class="btn-right"
          style="margin:20px 0;background: none;"
          @click="goBack"
          >退出</el-button
        >
      </div>
    </div>
    <div class="drug-content box">
      <EditInfo :types="types" ref="info" :ruleForm="ruleForm"></EditInfo>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      width="30%"
      center
      :show-close="false"
    >
      <span class="dialog-content">当前页面内容可能未保存，是否确定退出？</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="goList">确 定</el-button>
        <el-button @click="dialogVisible = false" size="mini">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import EditInfo from "./components/edit-info.vue";
import { httpReq } from "@/http";
export default {
  data() {
    return {
      types: 1,
      dialogVisible: false,
      ruleForm: {
        drugId: "", //药品id
        drugAmount: "", //用量
        drugApplicability: "", //适用症
        drugEffect: "", //作用
        drugAttention: "", //注意事项
        drugFirm: "", //厂商
        drugSpec: "", //规格
        takingTime: "", //服用时间
        drugMode: "", //用药方式
        drugTaboo: "", //禁忌
        value: "", //名称
        adverseReactions: "", //不良反应
        drugValidity: "", //有效期
        drugUsage: "", //用法
        drugPictureId: "", //图片id
        pictureUrl: "" //图片
      }
    };
  },
  components: {
    EditInfo
  },
  computed: {},
  watch: {},
  created() {
    this.getDrugDetail(this.$route.query.drug_id);
  },
  mounted() {},
  methods: {
    goBack() {
      this.dialogVisible = true;
    },
    goList() {
      this.$router.push("/index/DrugLibrary");
    },
    //选择图片时触发

    //请求药品详情
    getDrugDetail(drugId) {
      httpReq({
        url: "/drug/storehouse/findDrugInfo",
        method: "post",
        data: {
          drugId
        }
      }).then(res => {
        this.ruleForm = res.data;
      });
    },
    saveDetail(drugId) {
      this.$refs["info"].addInfo("ruleForm");
      // httpReq({
      //   url: "/drug/storehouse/findDrugInfo",
      //   method: "post",
      //   data: {
      //     drugId
      //   }
      // }).then(res => {
      //   this.ruleForm = res.data;
      // });
    }
  }
};
</script>
<style lang="less" scoped>
/deep/.el-select--mini {
  width: -webkit-fill-available;
}
.drug-details {
  display: flex;
  flex-direction: column;
  flex: 1;
  .drug-save {
    height: 66px;
    display: flex;
    justify-content: space-between;

    .top-left {
      display: flex;
      img {
        width: 50px;
        height: 50px;
        margin-top: 8px;
        background: #d8d8d8;
      }
      span {
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        margin-left: 28px;
        padding-top: 20px;
        color: #000000;
        line-height: 25px;
      }
    }
    .btn-right {
      width: 88px;
      margin: 20px 28px;
      height: 32px;
      padding: 2px;
      background: #0c88be;
      border-radius: 3px;
    }
  }
  .drug-content {
    padding-top: 10px !important;
    display: flex;
    flex-direction: column;
    height: 650px;
    overflow-y: scroll;
  }
  .box {
    background: #fff;
    margin: 10px 24px 5px;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
  }
}
</style>
