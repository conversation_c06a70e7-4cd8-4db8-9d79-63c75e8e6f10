<!-- 药品库 -->
<template>
  <div class="drug-storage">
    <div class="search-box box">
      <el-form ref="searchFrom" size="mini" :inline="true" :model="searchFrom">
        <el-form-item label="药品通用名/商品名" class="minclass">
          <el-input
            v-model="searchFrom.value"
            placeholder="请输入药品通用名/商品名"
          ></el-input>
        </el-form-item>
        <el-form-item label="分类" class="minclass">
          <el-select
            v-model="searchFrom.drugType"
            class="minclass"
            placeholder="请选择分类"
            filterable
          >
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="(item, index) in allDrugsCategories"
              :key="index"
              :label="item.className"
              :value="item.drugType"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="用法" class="minclass">
          <el-select
            v-model="searchFrom.drugMode"
            class="minclass"
            placeholder="请选择用法"
            filterable
          >
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="(item, index) in drugUsage"
              :key="index"
              :label="item.name"
              :value="item.name"
            ></el-option>
          </el-select>
          <!-- <el-input
            v-model="searchFrom.drug_usage"
            placeholder="请输入服药时间"
          ></el-input> -->
        </el-form-item>
        <span style="line-height: 40px">
          <el-button type="primary" @click="onSearch" class="search-btn"
          >查询</el-button
          >
          <el-button @click="resetForm" class="reset-btn">重置</el-button>
        </span>
      </el-form>
    </div>
    <div class="function-box">
      <el-button
        type="primary"
        size="medium"
        class="buttons2"
        @click="toAddDrug(0)"
      >添加药品
      </el-button
      >
      <el-button
        type="primary"
        size="medium"
        class="buttons2"
        @click="downLoadSheet(searchFrom)"
      >导出
      </el-button
      >
    </div>
    <div class="data-box box">
      <div class="table">
        <el-table
          :data="tableData"
          size="mini"
          style="width: 100%"
          max-height="530"
          ref="table"
        >
          <el-table-column align="center" label="序号" width="60">
            <template slot-scope="scope">
              {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="value" align="center" label="药品商品名">
          </el-table-column>
          <el-table-column prop="drugName" align="center" label="药品通用名">
          </el-table-column>
          <el-table-column prop="className" align="center" label="分类">
          </el-table-column>
          <el-table-column prop="drugSpec" align="center" label="规格">
          </el-table-column>
          <el-table-column prop="drugUsage" align="center" label="频率">
          </el-table-column>
          <el-table-column prop="drugMode" align="center" label="用法">
          </el-table-column>
          <el-table-column prop="operation" align="center" label="操作">
            <template slot-scope="scope">
              <el-button
                @click="toAddDrug(2, scope.row.drugId)"
                type="text"
                size="small"
              >查看
              </el-button
              >
              <el-button
                @click="deleteCurrInfo(scope.row.drugId)"
                type="text"
                size="small"
              >删除
              </el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-sizes="[10, 20, 50]"
        :page-size="pageSize"
        layout="total,sizes, prev, pager, next"
        :total="total"
        size="mini"
      >
      </el-pagination>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      :show-close="false"
      class="self-dialog"
    >
      <span slot="title" class="show-title">
        删除药品
      </span>
      <div class="edit-box">
        <div class="delete-text">是否确认删除该药品</div>
        <div class="sub-btn">
          <el-button
            size="mini"
            type="primary"
            @click="deleteCurrentDrug(drugId)"
          >确认
          </el-button
          >
          <el-button size="mini" @click="dialogVisible = false">取消</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { httpReq } from '@/http'

export default {
  components: {},
  data () {
    return {
      dialogVisible: false,
      searchFrom: {
        value: '',
        drugType: '',
        drugMode: ''
      },
      tableData: [],
      drugUsage: [
        { name: '口服' },
        { name: '含服' },
        { name: '皮下注射' },
        { name: '鼻腔喷入' },
        { name: '餐前服' },
        { name: '随餐服' },
        { name: '餐后半小时' },
        { name: '睡前服' },
        { name: '口腔喷入' },
        { name: '喷舌下' },
        { name: '外用' }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 1,
      drugId: '',
      allDrugsCategories: []
    }
  },
  computed: {},
  watch: {},
  created () {
    this.getAllDrugsCategories()
    if (sessionStorage.getItem('lastRouterInfo') != null) {
      let { page, pageSize, router, searchFrom } = JSON.parse(
        sessionStorage.getItem('lastRouterInfo')
      )
      if (router == this.$route.path) {
        this.currentPage = page
        this.pageSize = pageSize
        this.searchFrom = searchFrom
      } else {
        sessionStorage.removeItem('lastRouterInfo')
      }
    }
  },
  mounted () {
    this.getDrugList(this.currentPage, this.pageSize, this.searchFrom)
  },
  methods: {
    //搜索
    onSearch () {
      this.getDrugList(1, this.pageSize, this.searchFrom)
    },
    //重置
    resetForm () {
      this.searchFrom = {
        value: '',
        drugType: '',
        drugMode: ''
      }
      this.getDrugList(this.currentPage, this.pageSize, this.searchFrom)
    },
    toAddDrug (type, drugId = '') {
      // 这个要改
      this.$router.push({
        path: '/index/DrugLibraryAdd',
        query: {
          type,
          drugId
        }
      })
    },

    //删除
    deleteCurrInfo (drugId) {
      this.dialogVisible = true
      this.drugId = drugId
    },
    //删除信息
    deleteCurrentDrug (drugId) {
      httpReq({
        url: '/drug/storehouse/delDrug',
        method: 'delete',
        data: {
          drugId
        }
      })
        .then(res => {
          this.$message.success(`已删除！`)
          this.dialogVisible = false
          this.getDrugList(this.currentPage, this.pageSize, this.searchFrom)
        })
        .catch(err => {
          this.dialogVisible = false
          this.$message.error(`失败：${err.msg}`)
        })
    },
    //请求列表
    getDrugList (page, pageSize, info) {
      httpReq({
        url: '/drug/storehouse/getDrugList',
        method: 'post',
        data: {
          ...info,
          page,
          pageSize
        }
      }).then(res => {
        this.setInfo()
        this.tableData = res.data.data
        this.total = res.data.records
      })
    },
    //存储搜索条件
    setInfo () {
      let info = {
        pageSize: this.pageSize,
        page: this.currentPage,
        searchFrom: this.searchFrom,
        router: this.$route.path
      }
      sessionStorage.setItem('lastRouterInfo', JSON.stringify(info))
    },
    /**
     *
     * 导出功能
     * 要修改内容
     *
     */
    downLoadSheet (info) {
      const params = {
        value: info.value,
        drugType: info.drugType,
        drugMode: info.drugMode
      }
      let dataArr = []
      for (let i in params) {
        dataArr.push(`${i}=${params[i] || ''}`)
      }
      let url = `${
        process.env.VUE_APP_baseUrl
      }drug/storehouse/download/drugInfo?${dataArr.join('&')}`
      this.dowloadFiles(url)
    },
    //当前页面下载
    dowloadFiles (url) {
      const link = document.createElement('a')

      link.setAttribute('download', 'xxx.xlsx')

      link.setAttribute('href', url)

      link.style.display = 'none'

      document.body.appendChild(link)

      link.click()

      link.remove()
    },
    // 切换每页条数
    handleSizeChange (val) {
      this.pageSize = val
      this.getDrugList(1, this.pageSize, this.searchFrom)
    },
    // 切换页码
    handleCurrentChange (val) {
      this.getDrugList(val, this.pageSize, this.searchFrom)
    },
    getAllDrugsCategories () {
      httpReq({
        url: '/drug/storehouse/getDrugType',
        method: 'post'
      }).then(res => {
        this.allDrugsCategories = res.data
      })
    }
  }
}
</script>
<style></style>
<style lang="less" scoped>
.drug-storage {
  display: flex;
  flex-direction: column;

  .sub-btn {
    text-align: center;
    padding-right: 24px;
    margin-bottom: 10px;

    .el-button {
      width: 104px;
      height: 36px;
      font-size: 14px;
      color: #ffffff;
      background: #0c88be;
      border-radius: 4px;
      margin-left: 20px;

      &:last-of-type {
        color: #303133;
        background: #fff;
        border: 1px solid #dcdfe6;
      }
    }
  }

  /deep/ .el-dialog__header {
    padding: 0;
  }

  /deep/ .el-dialog--center {
    margin-top: 20vh !important;
  }

  /deep/ .el-dialog__wrapper.self-dialog {
    .el-dialog {
      width: 568px;
      height: 275px;
      background: #ffffff;
      box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      border: 1px solid #e4e7ed;
      box-sizing: border-box;
      margin-top: 30vh !important;

      .el-dialog__header {
        font-size: 18px;
        text-align: left;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #303133;
        padding: 20px 20px 16px;
        border-bottom: 1px solid #ebeef5;
      }

      .el-dialog__body {
        padding: 16px 20px;
      }
    }
  }

  .delete-text {
    width: 100%;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
  }

  .search-box {
    box-sizing: border-box;
    text-align: left;
    display: flex;
    align-items: center;

    /deep/ .el-form {
      .el-form-item {
        margin: 0 20px 0 0;
      }

      .minclass .el-form-item__content {
        .el-date-editor.el-input {
          width: 120px;
        }

        .el-input__inner {
          height: 40px;
          font-size: 14px;
        }
      }

      .minclass .el-form-item__label {
        line-height: 40px;
        font-size: 14px;
        color: #000000;
      }

      .minclass .el-icon-arrow-up:before {
        content: "";
      }

      .minclass2 {
        .el-input__inner {
          width: 220px;
        }
      }
    }
  }

  .function-box {
    height: 40px;
    margin: 0 24px;
    text-align: left;
  }

  .data-box {
    flex: 1;
    display: flex;
    flex-direction: column;

    .table {
      margin-bottom: 10px;
      flex: 1;
    }

    .el-pagination {
      text-align: right;
    }
  }

  .box {
    background: #fff;
    padding: 20px;
    border-radius: 5px;
    margin: 8px 24px 20px;
  }
}

.search-btn {
  width: 88px;
  height: 32px;
  color: #ffffff;
  font-size: 12px;
  background: #0c88be;
  border-radius: 3px;
  padding: 0;

  &:first-of-type {
    margin: 0 16px 0 20px;
  }
}

.reset-btn {
  width: 88px;
  height: 32px;
  font-size: 12px;
  border-radius: 3px;
  padding: 0;
}

.buttons {
  margin-bottom: 20px;
  background-color: #0c88be;
  width: 88px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.buttons2 {
  background-color: #0c88be;
}

///deep/.el-table thead {
//  font-size: 14px;
//}
///deep/.el-table--mini {
//  font-size: 14px;
//}
///deep/ .el-table .cell {
//  line-height: 48px;
//}
.table {
  /deep/ .el-table {
    width: 100%;

    .el-table__header,
    .el-table__body {
      // table-layout: auto;
      width: 100%;
    }

    &::before {
      height: 0;
    }

    th,
    td {
      height: 50px;
      font-size: 14px;
      padding: 0;
      border-bottom: 1px solid #ebeef5;
      box-sizing: border-box;

      .cell {
        padding: 0;
      }
    }

    th .cell {
      color: #909399;
    }

    td .cell {
      color: #606266;

      .edit-btns {
        width: 100%;

        .el-button {
          color: #0c88be;
          font-size: 14px;
          margin: 0;
          box-sizing: border-box;
          padding: 0;

          &:nth-of-type(2) {
            padding-left: 6px;
            border-left: 1px solid #d8d8d8;
          }

          &:last-of-type {
            color: #f56c6c;
          }
        }
      }
    }

    .index-item {
    }
  }
}

.delebutton {
  color: red;
  border-left: 1px solid #d8d8d8;
}

.detailbutton {
  color: #0c88be;
}

.rowbtn {
  padding: 0 10px;
  line-height: 16px;
}

.btns {
  display: flex;
  align-items: center;
}

.dialog-footer {
  display: flex;
  justify-content: center;
}

.buttonscancel {
  margin-bottom: 20px;
  width: 88px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/deep/ .selectbox .el-icon-arrow-up:before {
  content: "";
}
</style>
