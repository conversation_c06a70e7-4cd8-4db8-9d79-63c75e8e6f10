<!-- 药品库编辑 -->
<template>
  <div class="edit-box">
    <div class="content-top">
      <div class="top-left">
        <span class="bass">基本信息</span>
        <span style="color: #f56c6c;margin-left: 5px;">*</span
        ><span class="txt">为必填项</span>
      </div>
      <!-- 这里要做条件判断 -->
      <div class="top-right" v-if="types == 2">
        <el-button style="padding:9px 20px;" @click="goList">返回</el-button>
      </div>
    </div>
    <div class="content-mian">
      <el-form
        :model="ruleForm"
        class="demo-ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="80px"
        size="mini"
      >
        <el-form-item label="药品名称" prop="value">
          <el-input
            v-model="ruleForm.value"
            clearable
            placeholder="请输入药品名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="外观" prop="appearance">
          <div class="block" style="text-align:left">
            <el-avatar
              shape="square"
              :size="100"
              :src="ruleForm.pictureUrl"
              :key="ruleForm.pictureUrl"
            ></el-avatar>
            <el-button type="text" size="mini" @click="uploadPic">
              &emsp;{{ "上传外观" }}</el-button
            >
            <input
              type="file"
              ref="evfile"
              @change="zh_uploadFile_change"
              style="display: none"
            />
          </div>
        </el-form-item>
        <el-form-item label="药品厂商" prop="drugFirm">
          <el-input
            v-model="ruleForm.drugFirm"
            placeholder="请输入药品厂商"
          ></el-input>
        </el-form-item>
        <el-form-item label="作用" prop="drugEffect">
          <el-input
            v-model="ruleForm.drugEffect"
            placeholder="请输入作用"
          ></el-input>
        </el-form-item>
        <el-form-item label="用法" prop="drugMode">
          <el-select v-model="ruleForm.drugMode" placeholder="请选择用法">
            <el-option
              v-for="(item, index) in drugMode"
              :key="index"
              :label="item.name"
              :value="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="用量" prop="drugAmount">
          <el-input
            v-model="ruleForm.drugAmount"
            placeholder="请输入用量"
          ></el-input>
        </el-form-item>
        <el-form-item label="用药时间" prop="takingTime">
          <el-select v-model="ruleForm.takingTime" placeholder="请选择服用时间">
            <el-option
              v-for="(item, index) in drugUsage"
              :key="index"
              :label="item.name"
              :value="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="频率" prop="drugUsage">
          <el-select v-model="ruleForm.drugUsage" placeholder="请选择频率">
            <el-option
              v-for="(item, index) in frequency"
              :key="index"
              :label="item.name"
              :value="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="有效期" prop="drugValidity">
          <el-input
            v-model="ruleForm.drugValidity"
            placeholder="请输入有效期"
          ></el-input>
        </el-form-item>
        <el-form-item label="适用症" prop="drugApplicability">
          <el-input
            v-model="ruleForm.drugApplicability"
            placeholder="请输入适用症"
          ></el-input>
        </el-form-item>
        <el-form-item label="不良反应" prop="adverseReactions">
          <el-input
            v-model="ruleForm.adverseReactions"
            type="textarea"
            placeholder="请输入不良反应"
          ></el-input>
        </el-form-item>
        <el-form-item label="注意事项" prop="drugAttention">
          <el-input
            v-model="ruleForm.drugAttention"
            type="textarea"
            placeholder="请输入注意事项"
          ></el-input>
        </el-form-item>
        <el-form-item label="禁忌" prop="drugTaboo">
          <el-input
            v-model="ruleForm.drugTaboo"
            placeholder="请输入禁忌"
            type="textarea"
            autosize
          ></el-input>
        </el-form-item>
        <el-form-item size="large" v-if="types == 2">
          <el-button type="primary" @click="addInfo('ruleForm')"
            >添加</el-button
          >
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import * as qiniu from "qiniu-js";
import { httpReq } from "@/http";
export default {
  components: {},
  data() {
    var validateCover = (rule, value, callback) => {
      if (this.ruleForm.pictureUrl == "") {
        return callback(new Error("请上传外观"));
      } else {
        return callback();
      }
    };
    return {
      ruleForm: {
        value: "", //名称
        drugId: "", //药品id
        drugFirm: "", //厂商
        drugEffect: "", //作用
        drugMode: "", //用法
        drugAmount: "", //用量
        drugUsage: "", //频率
        takingTime: "", //用药时间
        drugValidity: "", //有效期
        drugApplicability: "", //适用症
        adverseReactions: "", //不良反应
        drugAttention: "", //注意事项
        drugTaboo: "", //禁忌
        drugSpec: "", //规格
        drugPictureId: "", //图片id
        pictureUrl: "", //图片
        pictureUrlList: []
      },
      drugMode: [
        { name: "口服" },
        { name: "外用" },
        { name: "皮下注射" },
        { name: "舌下含化" },
        { name: "温水冲服" }
      ],
      frequency: [
        { name: "一天一次" },
        { name: "一天2次" },
        { name: "一天3次" },
        { name: "每月一次" },
        { name: "2周1次" },
        { name: "1周1次" }
      ],
      drugUsage: [
        { name: "晚上睡前" },
        { name: "空腹或餐后" },
        { name: "餐后" },
        { name: "早餐后" },
        { name: "早餐前" },
        { name: "餐后1小时" },
        { name: "进餐时" },
        { name: "餐中或餐后即可口服" },
        { name: "胸痛、胸闷时" },
        { name: "餐时与前几口食物同时嚼服" },
        { name: "三餐前" }
      ],
      rules: {
        value: [
          { required: true, message: "请输入药品名称", trigger: "blur" },
          { max: 30, message: "药品名字的长度在30个字符之内", trigger: "blur" }
        ],
        appearance: [
          { required: true, validator: validateCover, trigger: "change" }
        ],
        drugFirm: [
          { required: false, message: "请输入药品厂商", trigger: "blur" },
          { max: 30, message: "药品厂商的长度在30个字符之内", trigger: "blur" }
        ],
        drugEffect: [
          { required: true, message: "请输入作用", trigger: "blur" },
          { max: 50, message: "作用的长度在50个字符之内", trigger: "blur" }
        ],
        drugMode: [
          { required: true, message: "请选择用法", trigger: "change" }
        ],
        drugAmount: [
          { required: true, message: "请输入用量", trigger: "blur" },
          { max: 30, message: "用量的长度在30个字符之内", trigger: "blur" }
        ],
        drugUsage: [
          { required: true, message: "请选择频率", trigger: "change" }
        ],
        takingTime: [
          { required: true, message: "请选择用药时间", trigger: "change" }
        ],
        drugValidity: [
          { required: false, message: "请输入有效期", trigger: "blur" },
          { max: 30, message: "有效期的长度在30个字符之内", trigger: "blur" }
        ],
        drugApplicability: [
          { required: true, message: "请输入适用症", trigger: "blur" },
          { max: 30, message: "适用症的长度在30个字符之内", trigger: "blur" }
        ],
        adverseReactions: [
          { required: false, message: "请输入不良反应", trigger: "blur" },
          {
            max: 100,
            message: "不良反应的长度在100个字符之内",
            trigger: "blur"
          }
        ],
        drugAttention: [
          { required: false, message: "请输入注意事项", trigger: "blur" },
          {
            max: 100,
            message: "注意事项的长度在100个字符之内",
            trigger: "blur"
          }
        ],
        drugTaboo: [
          { required: false, message: "请输入禁忌", trigger: "blur" },
          { max: 100, message: "禁忌的长度在100个字符之内", trigger: "blur" }
        ]
      },
      dialogImageUrl: "",
      avatorLimitCountImg: 1,
      avatorNoneBtnImg: false,
      showAvatorBtnImg: true
    };
  },
  props: {
    types: { require: true },
    ruleForm: { require: true }
  },
  computed: {},
  watch: {},
  created() {
    this.getUploadToken();
    console.log(this.ruleForm, "type的数据，是来判断是哪个页面调用他的");
  },
  mounted() {},
  methods: {
    //选择上传文件
    uploadPic() {
      console.log(this.$refs.evfile);
      this.$refs.evfile.click();
    },
    //头像上传
    zh_uploadFile_change(evfile) {
      var loading;
      var uptoken = this.qiNiuToken;
      var file = evfile.target.files[0]; //Blob 对象，上传的文件
      var key = new Date().getTime() + file.name; // 上传后文件资源名以设置的 key 为主，如果 key 为 null 或者 undefined，则文件资源名会以 hash 值作为资源名。
      let config = {
        useCdnDomain: true, //表示是否使用 cdn 加速域名，为布尔值，true 表示使用，默认为 false。
        region: qiniu.region.z2 // 根据具体提示修改上传地区,当为 null 或 undefined 时，自动分析上传域名区域
      };
      let putExtra = {
        fname: "", //文件原文件名
        params: {}, //用来放置自定义变量
        mimeType: null //用来限制上传文件类型，为 null 时表示不对文件类型限制；限制类型放到数组里： ["image/png", "image/jpeg", "image/gif"]
      };
      var observable = qiniu.upload(file, key, uptoken, putExtra, config);
      observable.subscribe({
        next: result => {
          // 主要用来展示进度
          // console.log(result)
          loading = this.$loading({
            lock: true,
            text: "上传中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)"
          });
        },
        error: errResult => {
          // 失败报错信息
          loading.close();
          this.$message.error("上传头像图片失败，请重新上传");
          console.log(errResult);
        },
        complete: result => {
          // 接收成功后返回的信息
          loading.close();
          console.log(result);
          // https://gw.scheartmed.com //http://image.scheartmed.com
          this.ruleForm.pictureUrl =
            "http://image.scheartmed.com/" + result.key;
        }
      });
    },
    goList() {
      // this.$emit
      this.$emit("showHint", true);
      console.log("ss");
    },
    handlePictureCardPreview(file) {
      console.log(file, "222");
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },

    //获取七牛云凭证
    getUploadToken() {
      httpReq({
        url: "/admins/getUploadToken"
      }).then(res => {
        this.qiNiuToken = res.data;
      });
    },

    addInfo(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          console.log("submit!!");

          this.updateInfo(this.ruleForm);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //添加/修改信息
    updateInfo(info) {
      console.log(info, "ssssssssssssssss");
      httpReq({
        url: "/drug/storehouse/addDrug",
        method: "post",
        data: {
          value: info.value, //名称
          drugId: info.drugId, //药品id
          drugFirm: info.drugFirm, //厂商
          drugEffect: info.drugEffect, //作用
          drugMode: info.drugMode, //用法
          drugAmount: info.drugAmount, //用量
          drugUsage: info.drugUsage, //频率
          takingTime: info.takingTime, //用药时间
          drugValidity: info.drugValidity, //有效期
          drugApplicability: info.drugApplicability, //适用症
          adverseReactions: info.adverseReactions, //不良反应
          drugAttention: info.drugAttention, //注意事项
          drugTaboo: info.drugTaboo, //禁忌
          drugSpec: info.drugSpec, //规格
          drugPictureId: info.drugPictureId, //图片id
          pictureUrl: info.pictureUrl //图片
        }
      })
        .then(res => {
          console.log(res);
          this.$message.success(`${this.types == 1 ? "修改" : "新增"}成功`);
          if (this.types !== 1) {
            this.$emit("updateList");
          }
        })
        .catch(err => {
          console.log(err);
          this.$message.error(`失败：${err.msg}`);
        });
    }
  }
};
</script>
<style lang="less" scoped>
/deep/.el-select--mini {
  width: -webkit-fill-available;
}
.edit-box {
  display: flex;
  flex-direction: column;
  .content-top {
    border-bottom: solid 1px #ebeef5;
    height: 44px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    .top-left {
      padding-top: 5px;
      .bass {
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #111111;
      }
      .txt {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
      }
    }
  }
  .content-mian {
    width: 100%;
    padding-top: 16px;
    margin: 0 auto;

    .demo-ruleForm {
      width: 38%;
      margin: 0 auto;
    }
  }
}
.uoloadSty .el-upload--picture-card {
  width: 110px;
  height: 110px;
  line-height: 110px;
}
::v-deep .el-dialog/deep/.disUoloadSty .el-upload--picture-card {
  display: none; /* 上传按钮隐藏 */
}
</style>
