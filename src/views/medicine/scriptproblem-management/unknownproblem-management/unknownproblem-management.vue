<!-- 药品库 -->
<template>
  <div class="drug-storage">
    <div class="search-box box">
      <el-form ref="searchFrom" size="mini" :inline="true" :model="searchFrom">
        <el-form-item label="时间段" class="minclass">
          <el-select v-model="searchFrom.timeshort" placeholder="请选择">
            <el-option
              v-for="item in selectList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 优化1.1.1版本内容 -->
        <el-form-item label="添加人" class="minclass">
          <el-select
            v-model="searchFrom.assistantId"
            filterable
            placeholder="请选择添加人"
          >
            <el-option
              v-for="(item, index) in allAssistantList"
              :key="index"
              :label="item.name"
              :value="item.assistant_id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-button type="primary" @click="onSearch" class="search-btn"
        >查询</el-button
      >
      <el-button type="primary" @click="resetForm" class="search-btn"
        >重置</el-button
      >
    </div>
    <div class="function-box">
      <el-button
        type="primary"
        size="medium"
        @click="downloadSheet(searchFrom)"
        class="buttons2"
        >导出</el-button
      >
    </div>
    <div class="data-box box">
      <div class="table">
        <el-table
          :data="tableData"
          size="mini"
          style="width: 100%"
          max-height="530"
          ref="table"
        >
          <el-table-column align="left" label="序号">
            <template slot-scope="scope" class="index-item">
              &nbsp;{{
                currentPage === 1
                  ? prefixInteger(
                      (currentPage - 1) * pageSize + scope.$index + 1,
                      2
                    )
                  : (currentPage - 1) * pageSize + scope.$index + 1
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="unknown_question"
            align="left"
            label="用户问题"
          >
            <template slot-scope="scope">
              <el-popover
                placement="top-start"
                title="问题详情"
                width="500"
                trigger="hover"
                :content="scope.row.unknown_question"
              >
                <span slot="reference">{{
                  scope.row.unknown_question | ellipsis
                }}</span>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" align="left" label="添加时间">
            <template slot-scope="scope">
              {{
                scope.row.create_time
                  ? timeMode(scope.row.create_time).dateMin
                  : ""
              }}
            </template>
          </el-table-column>
          <el-table-column prop="name" align="left" label="添加人">
          </el-table-column>
          <el-table-column
            prop="operation"
            align="left"
            label="操作"
            width="90"
          >
            <template slot-scope="scope">
              <div class="edit-btns">
                <el-button
                  @click="toAddQuestion(scope.row)"
                  type="text"
                  size="small"
                >
                  转化
                </el-button>
                <el-button
                  @click="deleteQuestion(scope.row)"
                  type="text"
                  size="small"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-sizes="[10, 20, 50]"
        :page-size="pageSize"
        layout="total,sizes, prev, pager, next"
        :total="total"
        size="mini"
      >
      </el-pagination>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      width="30%"
      center
      :show-close="false"
    >
      <span>是否确认删除？</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmdialog" class="buttons"
          >确 定</el-button
        >
        <el-button @click="dialogVisible = false" class="buttonscancel"
          >取 消</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDateRange,
  getBeforeDateRange,
  prefixInteger
} from "../../../../util/util";
import { httpReq } from "@/http";
import { timeMode } from "@/util/util";
export default {
  components: {},
  data() {
    return {
      timeMode,
      prefixInteger,
      dialogVisible: false,
      searchFrom: {
        timeshort: "",
        assistantId: "" // 优化1.1.1 版本内容
      },
      dialogType: 0, //0是删除,1是新增
      tableData: [],
      selectList: [
        { label: "昨天", value: getBeforeDateRange(1) },
        { label: "前天", value: getBeforeDateRange(2) },
        { label: "七天内", value: getDateRange(new Date(), 6, true) },
        { label: "近十五天", value: getDateRange(new Date(), 14, true) }
      ],
      allAssistantList: [],
      currentPage: 1,
      pageSize: 10,
      total: 1,
      currentQuestion: ""
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getUnknowQuestionList(
      this.currentPage,
      this.pageSize,
      this.searchFrom
    );
  },
  mounted() {
    this.getAllAssistantList(); //获取医助信息
  },
  filters: {
    ellipsis(value) {
      if (!value) return "";
      if (value.length > 20) {
        return value.slice(0, 20) + "...";
      }
      return value;
    }
  },
  methods: {
    // 获取所有医助列表
    getAllAssistantList() {
      httpReq({
        url: "/doc/group/getAssistant"
      }).then(res => {
        this.allAssistantList = res.data;
      });
    },
    resetForm() {
      this.searchFrom = {
        timeshort: "",
        assistantId: "" // 优化1.1.1 版本内容
      };
      this.getUnknowQuestionList(
        this.currentPage,
        this.pageSize,
        this.searchFrom
      );
    },
    increase(searchFrom) {
      this.dialogType = 1;
      this.dialogVisible = true;
    },
    onSearch() {
      this.getUnknowQuestionList(
        this.currentPage,
        this.pageSize,
        this.searchFrom
      );
    },
    // 点击查看
    toAddQuestion(row) {
      console.log("转化", row);
      this.$router.push({
        path: "/index/AddScriptProblemManageLibrary",
        query: {
          actionType: 1,
          unknow_Id: row.advisory_unknown_id,
          questionContent: row.unknown_question
        }
      });
    },
    //删除
    deleteQuestion(item) {
      this.dialogVisible = true;
      console.log("删除");
      this.currentQuestion = item;
    },
    // 切换每页条数
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.pageSize = val;
      this.getUnknowQuestionList(1, this.pageSize, this.searchFrom);
    },
    // 切换页码
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.getUnknowQuestionList(val, this.pageSize, this.searchFrom);
    },
    getUnknowQuestionList(page, pageSize, searchFrom) {
      httpReq({
        url: "/AdvisoryQus/findAdvisoryUnknown",
        method: "post",
        data: {
          startDate: searchFrom.timeshort ? searchFrom.timeshort[0] : "",
          endDate: searchFrom.timeshort ? searchFrom.timeshort[1] : "",
          assistantId: searchFrom.assistantId, //优化1.1.1 版本内容
          page,
          pageSize
        }
      }).then(res => {
        this.tableData = res.data.data;
        this.total = res.data.records;
      });
    },
    //删除问题
    deleUnknowQuestion(advisoryUnknownId) {
      console.log(advisoryUnknownId);
      httpReq({
        url: "/AdvisoryQus/delAdvisoryUnknown",
        method: "delete",
        data: {
          advisoryUnknownId
        }
      }).then(res => {
        console.log(res);
        if (res.code !== 1) {
          this.$message.error(`删除失败:${err.msg}`);
        } else {
          this.$message.success(`删除成功!`);
          this.getUnknowQuestionList(
            this.currentPage,
            this.pageSize,
            this.searchFrom
          );
          this.dialogVisible = false;
        }
      });
    },
    //弹框
    confirmdialog() {
      this.deleUnknowQuestion(this.currentQuestion.advisory_unknown_id);
    },
    //导出
    downloadSheet(searchFrom) {
      const params = {
        startDate: searchFrom.timeshort ? searchFrom.timeshort[0] : null,
        endDate: searchFrom.timeshort ? searchFrom.timeshort[1] : null,
        assistantId: searchFrom.assistantId
      };
      var dataArr = [];
      for (let i in params) {
        dataArr.push(`${i}=${params[i] || ""}`);
      }
      let url = `${
        process.env.VUE_APP_baseUrl
      }AdvisoryQus/download/advisoryQus?${dataArr.join("&")}`;
      if (searchFrom.timeshort[0] && searchFrom.timeshort[1]) {
        this.dowloadFiles(url);
      } else {
        const params = {
          assistantId: searchFrom.assistantId
        };
        var dataArr = [];
        for (let i in params) {
          dataArr.push(`${i}=${params[i] || ""}`);
        }
        let url = `${
          process.env.VUE_APP_baseUrl
        }AdvisoryQus/download/advisoryQus?${dataArr.join("&")}`;
        this.dowloadFiles(url);
      }
    },
    //当前页面下载
    dowloadFiles(url) {
      const link = document.createElement("a");

      link.setAttribute("download", "xxx.xlsx");

      link.setAttribute("href", url);

      link.style.display = "none";

      document.body.appendChild(link);

      link.click();

      link.remove();
    }
  }
};
</script>
<style></style>
<style lang="less" scoped>
.drug-storage {
  display: flex;
  flex-direction: column;
  /deep/.el-dialog__header {
    padding: 0;
  }
  /deep/.el-dialog--center {
    margin-top: 20vh !important;
  }
  /deep/.el-dialog__body {
    text-align: center;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #606266;
    padding: 20px;
    line-height: 24px;
  }
  .search-box {
    text-align: left;
    display: flex;
    align-items: center;
    /deep/.el-form {
      .el-form-item {
        margin: 0;
      }
      .minclass .el-form-item__content {
        width: 240px;
        margin-right: 10px;
        .el-date-editor.el-input {
          width: 120px;
        }

        .el-input__inner {
          // height: 40px;
          font-size: 14px;
        }
      }
      .minclass .el-form-item__label {
        // line-height: 40px;
        font-size: 14px;
        color: #000000;
      }
      .minclass .el-icon-arrow-up:before {
        content: "";
      }
      .minclass2 {
        .el-input__inner {
          width: 220px;
        }
      }
    }
  }
  .function-box {
    height: 40px;
    margin: 0 24px;
    text-align: left;
  }
  .data-box {
    flex: 1;
    display: flex;
    flex-direction: column;
    .table {
      flex: 1;
    }
    .el-pagination {
      text-align: right;
    }
  }
  .box {
    background: #fff;
    margin: 8px 24px;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 20px;
  }
}
.buttons {
  margin-bottom: 20px;
  background-color: #0c88be;
  width: 88px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.buttons2 {
  background-color: #0c88be;
}
.search-btn {
  width: 88px;
  height: 32px;
  color: #ffffff;
  font-size: 12px;
  background: #0c88be;
  border-radius: 3px;
  padding: 0;
  &:first-of-type {
    margin: 0 16px 0 20px;
  }
}
.table {
  /deep/.el-table {
    width: 100%;
    .el-table__header,
    .el-table__body {
      // table-layout: auto;
      width: 100%;
    }
    &::before {
      height: 0;
    }
    th,
    td {
      height: 50px;
      font-size: 14px;
      padding: 0;
      border-bottom: 1px solid #ebeef5;
      box-sizing: border-box;
      .cell {
        padding: 0;
      }
    }
    th .cell {
      color: #909399;
    }
    td .cell {
      color: #606266;
      .edit-btns {
        width: 100%;
        .el-button {
          color: #0c88be;
          font-size: 14px;
          margin: 0;
          box-sizing: border-box;
          padding: 0;
          &:nth-of-type(2) {
            padding-left: 6px;
            border-left: 1px solid #d8d8d8;
          }
          &:last-of-type {
            color: #f56c6c;
          }
        }
      }
    }
    .index-item {
    }
  }
}
.delebutton {
  color: red;
  border-left: 1px solid #d8d8d8;
}
.detailbutton {
  color: #0c88be;
}
.rowbtn {
  padding: 0 10px;
  line-height: 16px;
}
.btns {
  display: flex;
  align-items: center;
  justify-content: center;
}
.dialog-footer {
  display: flex;
  justify-content: center;
}
.buttonscancel {
  margin-bottom: 20px;
  width: 88px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}
/deep/.selectbox .el-icon-arrow-up:before {
  content: "";
}
</style>
