<!-- 添加/修改医助信息 -->
<template>
  <div class="edit-box">
    <el-form
      size="mini"
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      :disabled="dialogShowType == 1"
      class="self-rule-from"
    >
      <el-form-item label="话术" prop="speechcraftContent" class="">
        <el-input
          type="textarea"
          v-model="ruleForm.speechcraftContent"
          placeholder="请输入话术"
        ></el-input>
      </el-form-item>
      <el-form-item label="分类" prop="content">
        <el-checkbox-group v-model="ruleForm.content">
          <el-checkbox-button
            :label="item.speechcraftTypeId"
            name="content"
            v-for="(item, i) in contents"
            :key="i"
            >{{ item.typeContent }}</el-checkbox-button
          >
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <div class="sub-btn">
      <el-button size="mini" @click="resetForm('ruleForm')">取消</el-button>
      <el-button size="mini" type="primary" @click="submitForm('ruleForm')"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import { httpReq } from "@/http";

export default {
  name: "",
  props: {
    id: { require: true },
    dialogShowType: { require: true },
    contents: { require: true }
  },
  data() {
    return {
      ruleForm: {
        speechcraftContent: "",
        speechcraftId: "",
        content: []
      },
      rules: {
        speechcraftContent: [
          { required: true, message: "请输入分类名称", trigger: "blur" }
        ],
        content: [
          {
            type: "array",
            required: true,
            message: "请至少选择一个分类",
            trigger: "change"
          }
        ]
      }
    };
  },

  components: {},

  computed: {},

  mounted() {
    console.log(this.id);
    if (this.id) {
      this.getScriptInfo(this.id);
    }
  },

  methods: {
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        console.log(this.ruleForm);
        if (valid) {
          this.updateInfo(this.ruleForm);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.$parent.$parent.dialogFormVisibleEditBox = false;
    },
    //添加/修改信息
    updateInfo(info) {
      console.log({
        speechcraftId: this.id ? this.id : "",
        speechcraftContent: info.speechcraftContent,
        speechcraftTypeId: info.content
      });
      httpReq({
        url: "/script/mgmt/addScriptInfo",
        method: "post",
        data: {
          speechcraftId: this.id ? this.id : "",
          speechcraftContent: info.speechcraftContent,
          speechcraftTypeId: info.content
        }
      })
        .then(res => {
          console.log(res);
          this.$message.success(`${this.id ? "修改" : "新增"}成功`);
          // 修改或新增后通知父组件更新数据
          this.$emit("updateList");
        })
        .catch(err => {
          console.log(err);
          this.$message.error(`失败：${err.msg}`);
        });
    },
    // 获取话术信息
    getScriptInfo(speechcraftId) {
      httpReq({
        url: "/script/mgmt/findScriptInfo",
        method: "post",
        data: {
          speechcraftId
        }
      }).then(res => {
        console.log(res, JSON.parse(res.data.content));
        this.ruleForm = {
          speechcraftId: res.data.speechcraftId,
          speechcraftContent: res.data.speechcraftContent,
          content: JSON.parse(res.data.content).map(re =>
            Number(re.speechcraftTypeId)
          )
        };
        console.log(this.ruleForm.content, this.contents); // [{ speechcraftTypeId: 1, typeContent: "冠心病" }]
      });
    }
  }
};
</script>
<style lang="less" scoped>
.edit-box {
  /deep/.el-form.self-rule-from {
    margin-bottom: 15px;
    .el-form-item {
      margin-bottom: 12px;
    }
    .el-form-item__label {
      width: 50px !important;
      height: 40px;
      line-height: 40px;
      padding-right: 10px;
    }
    .el-form-item__content {
      width: 508px;
      min-height: 40px;
      line-height: 40px;
      border-radius: 4px;
      margin-left: 50px !important;
      .el-input__inner {
        width: 100%;
        height: 40px;
        line-height: 40px;
        padding: 13px 15px;
        font-size: 14px;
        box-sizing: border-box;
        &::placeholder {
          color: #909399;
        }
        // border-radius: 4px;
      }
      .el-textarea__inner {
        width: 100%;
        height: 94px;
        line-height: 14px;
        padding: 13px 15px;
        font-size: 14px;
        box-sizing: border-box;
        margin-top: 12px;
        resize: none;
        &::placeholder {
          color: #909399;
        }
      }
      .el-checkbox-group {
        text-align: left;
        .el-checkbox-button {
          margin-right: 16px;
          box-sizing: border-box;
          border: none !important;
          .el-checkbox-button__inner {
            min-width: 70px;
            height: 30px;
            border-radius: 4px;
            color: #909399;
            padding: 8px 16px;
            border: none;
            border: 1px solid #dcdfe6;
          }
        }
        .el-checkbox-button.is-checked .el-checkbox-button__inner {
          min-width: 70px;
          height: 30px;
          background: #fff;
          color: #0c88be;
          border-radius: 4px;
          border: none;

          border: 1px solid #0c88be;
        }
      }
    }
  }
  .sub-btn {
    text-align: right;
    padding-right: 24px;
    margin-bottom: 10px;
    .el-button {
      width: 104px;
      height: 36px;
      font-size: 14px;
      color: #ffffff;
      background: #0c88be;
      border-radius: 4px;
      margin-left: 20px;
      &:first-of-type {
        color: #303133;
        background: #fff;
        border: 1px solid #dcdfe6;
      }
    }
  }
}
</style>
