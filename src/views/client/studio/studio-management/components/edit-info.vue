<!-- 添加/修改工作室信息 -->
<template>
  <div class="edit-box">
    <el-form
      ref="ruleForm"
      :disabled="dialogShowType == 1"
      :model="ruleForm"
      :rules="rules"
      class="demo-ruleForm"
      label-position="left"
      label-width="130px"
      size="mini"
    >
      <el-form-item class="minclass" label="所属医院" prop="hospital">
        <el-select
          v-model="ruleForm.hospitalId"
          clearable
          filterable
          placeholder="选择所属医院"
          @change="searchList"
        >
          <el-option
            v-for="(item, index) in hospitalList"
            :key="index"
            :label="item.name"
            :value="item.hospitalId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="minclass" label="工作室名称" prop="name">
        <el-input
          v-model="ruleForm.name"
          placeholder="请输入工作室名称"
        ></el-input>
      </el-form-item>
      <el-form-item class="minclass" label="别称">
        <el-input
          maxlength="20"
          show-word-limit
          v-model="ruleForm.alias"
          placeholder="请输入别称"
        ></el-input>
      </el-form-item>
      <el-form-item class="minclass" label="工作室类型" prop="studioType">
        <el-select v-model="ruleForm.studioType" placeholder="工作室类型">
          <el-option :value="1" label="哈瑞特工作室"></el-option>
          <el-option :value="2" label="糖网工作室"></el-option>
          <el-option :value="3" label="房颤工作室"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item class="minclass" label="工作室病区" prop="areaId">
        <el-select v-model="ruleForm.areaId" placeholder="工作室病区">
          <el-option
            v-for="(item, index) in wardList"
            :key="index"
            :label="item.name"
            :value="item.areaId"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="medclass" label="工作室标签" prop="realType">
        <el-radio-group v-model="ruleForm.realType">
          <el-radio :label="1">真实工作室</el-radio>
          <el-radio :label="0">测试工作室</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="工作室简介" prop="remarks">
        <el-input v-model="ruleForm.remarks" type="textarea"></el-input>
      </el-form-item>
      <el-divider></el-divider>
      <el-form-item
        class="minclass"
        label="工作室手术量"
        prop="operationVolume"
      >
        <el-input v-model="ruleForm.operationVolume"></el-input>
      </el-form-item>
      <el-form-item class="minclass" label="关联组长" prop="groupLeader">
        <el-select
          v-model="ruleForm.groupLeader"
          clearable
          filterable
          placeholder="关联工作室组长"
        >
          <el-option
            v-for="(item, index) in doctorList"
            :key="index"
            :disabled="item.status == 0"
            :label="item.name"
            :value="item.doctor_id"
          >
            <span>{{ item.name }}</span>
            <span v-if="item.status == 0">（已禁用） </span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-show="ruleForm.groupLeader"
        class="minclass"
        label="关联成员(可多选)"
        prop="teamMembers"
      >
        <el-select
          v-model="ruleForm.teamMembers"
          multiple
          placeholder="关联工作室组员"
        >
          <el-option
            v-for="(item, index) in teamMembersList"
            :key="index"
            :disabled="item.status == 0"
            :label="item.name"
            :value="item.doctor_id"
          >
            <span>{{ item.name }}</span>
            <span v-if="item.status == 0">（已禁用） </span></el-option
          >
        </el-select>
      </el-form-item>
      <el-form-item class="minclass" label="关联医助" prop="assistantId">
        <el-select
          v-model="ruleForm.assistantId"
          clearable
          filterable
          placeholder="关联医助"
        >
          <el-option
            v-for="(item, index) in allAssistantList"
            :key="index"
            :disabled="item.status == 0"
            :label="item.name"
            :value="item.assistant_id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="minclass" label="关联健康管理师" prop="customerId">
        <el-select
          v-model="ruleForm.customerId"
          clearable
          filterable
          placeholder="关联健康管理师"
        >
          <el-option
            v-for="item in healthManagerList"
            :key="item.customerServerId"
            :label="item.name"
            :value="item.customerServerId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="minclass" label="关联销售" prop="sellerId">
        <el-select
          v-model="ruleForm.sellerId"
          clearable
          filterable
          placeholder="关联销售"
        >
          <el-option
            v-for="(item, index) in allSellerList"
            :key="index"
            :disabled="item.status == 0"
            :label="item.seller_name"
            :value="item.seller_id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        class="minclass"
        label="关联运动康复师"
        prop="rehabilitationId"
      >
        <el-select
          v-model="ruleForm.rehabilitationId"
          clearable
          filterable
          placeholder="关联运动康复师"
        >
          <el-option
            v-for="item in therapistList"
            :key="item.rehabilitationId"
            :label="item.name"
            :value="item.rehabilitationId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-divider></el-divider>
      <el-form-item label="产品服务" prop="porductType">
        <el-checkbox-group
          v-model="ruleForm.porductType"
          class="products_choose"
        >
          <el-checkbox
            v-for="(item, i) in productList"
            :key="i"
            :disabled="item.status == 0"
            :label="item.product_id"
            name="porductType"
            >{{ item.product_name }}（{{ item.product_id }}）
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <el-divider></el-divider>
    <div class="advisory-fee-mag" v-if="dialogShowType == 1">
      <div class="title">顾问费发放信息</div>
      <div class="detail-sharing">
        <el-table :data="sharingList" style="width: 100%">
          <el-table-column prop="expertName" label="医生姓名">
            <template slot-scope="scope">
              <div class="doctorName">
                {{ scope.row.expertName }}
                <div v-if="scope.row.isLeader === 1" class="group">组长</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="percent" label="分成比例">
            <template slot-scope="scope"> {{ scope.row.percent }}% </template>
          </el-table-column>
          <el-table-column prop="expense" label="是否正常发放">
            <template slot-scope="scope">
              <div class="doctorName">
                {{
                  scope.row.status === 0
                    ? "正常"
                    : scope.row.status === 1
                    ? "延迟"
                    : "-"
                }}
                <div v-if="scope.row.effectStatus === 0" class="effect">
                  未生效
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="nextReleaseTime" label="下次发放日期">
            <template slot-scope="scope">
              {{
                scope.row.nextReleaseTime
                  ? scope.row.nextReleaseTime.slice(0, 10)
                  : "--"
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="operation"
            align="left"
            label="操作"
            width="90"
          >
            <template slot-scope="scope">
              <div v-if="scope.row.isLeader != -1" class="edit-btns">
                <el-button
                  v-if="scope.row.status === 0"
                  @click="delayedRelease(scope.row)"
                  type="text"
                  size="small"
                >
                  延迟发放
                </el-button>
                <el-button
                  v-else
                  @click="cancelRelease(scope.row)"
                  type="text"
                  size="small"
                >
                  取消延迟
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div v-if="dialogShowType == 1" class="sub-btn">
      <el-button size="mini" @click="resetForm('ruleForm')">关闭</el-button>
    </div>
    <div v-else class="sub-btn">
      <el-button size="mini" @click="resetForm('ruleForm')">取消</el-button>
      <el-button size="mini" type="primary" @click="submitForm('ruleForm')"
        >保存
      </el-button>
    </div>

    <!-- 延迟发放顾问费 -->
    <el-dialog
      :visible.sync="dialogVisible"
      width="560px"
      :modal="false"
      :show-close="false"
      class="releaseDialog"
    >
      <span>
        <div class="title">
          延迟顾问费发放
        </div>
        <div class="form-box">
          <el-form
            :model="ruleFormRelease"
            :rules="rulesRelease"
            ref="ruleRelease"
            label-width="200px"
          >
            <el-form-item label="所属工作室" prop="studio">
              <el-input
                placeholder="请填写所属工作室"
                v-model="ruleFormRelease.studio"
                disabled
                style="width: 240px;"
              ></el-input>
            </el-form-item>
            <el-form-item label="医生" prop="doctor">
              <el-input
                placeholder="请填写所属工作室"
                v-model="ruleFormRelease.doctor"
                disabled
                style="width: 240px;"
              ></el-input>
            </el-form-item>
            <el-form-item label="下次发放时间" prop="time">
              <el-date-picker
                type="month"
                placeholder="选择下次发放时间"
                v-model="ruleFormRelease.time"
                style="width: 240px;"
                :picker-options="pickerOptions"
                @change="changeTime"
                :clearable="false"
              ></el-date-picker>
            </el-form-item>
          </el-form>
          <div class="tips">{{ tips }}</div>
        </div>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="cancelFormRelease">取 消</el-button>
        <el-button
          size="mini"
          type="primary"
          @click="submitFormRelease('ruleRelease')"
          >提 交</el-button
        >
      </span>
    </el-dialog>

    <el-dialog
      :visible.sync="sureDialogVisible"
      width="424px"
      :modal="false"
      :show-close="false"
      class="releaseDialog"
      top="30vh"
    >
      <div class="content">
        <div class="one">
          <img src="@/assets/image/warning.png" alt="" class="image" />
          <div class="msg">
            <div>
              是否继续取消延迟操作？
            </div>
            <div>{{ cancelDelayTips }}</div>
          </div>
          <img
            src="@/assets/image/close-img.png"
            alt=""
            class="close-image"
            @click="sureDialogVisible = false"
          />
        </div>
        <span class="tip-footer">
          <el-button size="mini" @click="sureDialogVisible = false"
            >取 消</el-button
          >
          <el-button size="mini" type="primary" @click="submitRelease()"
            >确 定</el-button
          >
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { httpReq } from "@/http";
import Debounce from "../../../../../util/util";

export default {
  name: "",
  props: {
    id: { require: true },
    dialogShowType: { require: true },
    allHospitalList: { require: true },
    allDoctorList: { require: true },
    // allSellerList: { require: true },
    allAssistantList: { require: true },
    allProductList: { require: true },
    allCustomerList: { require: true }
  },
  data() {
    return {
      wardList: [],
      allSellerList: [],
      ruleForm: {
        areaId: "", //病区id
        hospitalId: "",
        name: "",
        studioType: "",
        remarks: "",
        groupLeader: "",
        teamMembers: [],
        sellerId: "",
        assistantId: "",
        porductType: [],
        realType: "",
        num: "",
        customerId: "",
        rehabilitationId: ""
      },
      rules: {
        hospitalId: [
          { required: true, message: "请输入所属医院", trigger: "blur" }
        ],
        areaId: [
          { required: true, message: "请选择工作室病区", trigger: "change" }
        ],
        name: [
          { required: true, message: "请输入工作室名称", trigger: "blur" }
        ],
        realType: [
          {
            required: true,
            message: "请选择工作室标签",
            trigger: "change"
          }
        ],
        studioType: [
          { required: true, message: "请选择工作室类型", trigger: "change" }
        ],
        remarks: [{ required: true, message: "请输入简介", trigger: "blur" }],
        operationVolume: [
          { required: true, message: "工作室手术量", trigger: "blur" }
        ],
        groupLeader: [
          { required: true, message: "请输入工作室组长", trigger: "blur" }
        ],
        teamMembers: [
          {
            type: "array",
            required: false,
            message: "请选择关联医生",
            trigger: "change"
          }
        ],
        assistantId: [
          { required: true, message: "请输入关联医助", trigger: "blur" }
        ],
        customerId: [
          { required: true, message: "请输入关联健康管理师", trigger: "blur" }
        ],
        rehabilitationId: [
          { required: true, message: "请输入关联运动康复师", trigger: "blur" }
        ],
        sellerId: [
          { required: false, message: "请输入关联销售", trigger: "blur" }
        ],
        porductType: [
          {
            type: "array",
            required: true,
            message: "请至少选择一个产品服务",
            trigger: "change"
          }
        ]
      },
      oldGroupLeaderId: "", //修改前当前工作室的组长id
      oldTeamMembers: [], //修改前当前工作室的成员
      healthManagerList: [], //健康管理师
      therapistList: [], //运动康复师
      sharingList: [],
      dialogVisible: false,
      ruleFormRelease: {
        studio: "",
        doctor: "",
        time: ""
      },
      rulesRelease: {
        studio: [
          { required: true, message: "请输入所属工作室", trigger: "blur" }
        ],
        doctor: [{ required: true, message: "请选择医生", trigger: "change" }],
        time: [
          {
            type: "date",
            required: true,
            message: "请选择日期",
            trigger: "change"
          }
        ]
      },
      pickerOptions: {
        disabledDate(time) {
          // 获取当前日期
          const currentDate = new Date();
          // 计算下下个月的日期
          const nextNextMonth = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth() + 2,
            1
          );
          // 如果选择的日期小于下下个月的日期，则禁用该日期
          return time.getTime() < nextNextMonth.getTime();
        }
      },
      tips: "",
      year: "",
      month: "",
      sureDialogVisible: false,
      expertId: "",
      currYear: "",
      currMonth: "",
      cancelDelayTips: "",
      day: 0
    };
  },
  created() {
    this.de = new Debounce(500);
  },
  computed: {
    // 可选的医生组长
    doctorList() {
      let canBindDoctor = [];
      let hospitalFilterBindDoctor = [];
      if (this.ruleForm.hospitalId) {
        hospitalFilterBindDoctor = this.showDoctorsList.filter(
          re => re.hospital_id == this.ruleForm.hospitalId
        );
      } else {
        hospitalFilterBindDoctor = this.showDoctorsList;
      }
      if (this.ruleForm.teamMembers.length > 0) {
        canBindDoctor = JSON.parse(
          JSON.stringify(hospitalFilterBindDoctor)
        ).filter(re => {
          let flag = this.ruleForm.teamMembers.some(r => {
            return re.doctor_id == r;
          });
          return !flag;
        });
      } else {
        canBindDoctor = JSON.parse(JSON.stringify(hospitalFilterBindDoctor));
      }
      return canBindDoctor;
    },
    // 可选的医院
    hospitalList() {
      let groupLeaderHospitalId = "";
      if (this.ruleForm.groupLeader) {
        let findHospital = this.showDoctorsList.find(
          re => re.doctor_id == this.ruleForm.groupLeader
        );
        groupLeaderHospitalId = findHospital ? findHospital.hospital_id : "";
      }
      return this.ruleForm.groupLeader
        ? this.allHospitalList.filter(
            re => re.hospitalId == groupLeaderHospitalId
          )
        : this.allHospitalList;
    },
    // 可选的组员
    teamMembersList() {
      let currLeaderHospitalId = "";
      if (this.ruleForm.groupLeader) {
        // 选择了关联组长后，需要留存当前关联组长的所在的医院ID，以便筛选同一个医院下的可选择医生。
        let findDoctor = this.showDoctorsList.find(
          re => re.doctor_id == this.ruleForm.groupLeader
        );
        currLeaderHospitalId = findDoctor ? findDoctor.hospital_id : "";
      }
      return this.ruleForm.groupLeader
        ? this.showDoctorsList.filter(
            re =>
              re.doctor_id !== this.ruleForm.groupLeader &&
              re.hospital_id == currLeaderHospitalId
          )
        : this.showDoctorsList;
    },
    //筛选出可显示的医生
    showDoctorsList() {
      let isBindShowArr = [];
      // 过滤出未绑定工作室且未被禁用的医生
      let allCanBindDoctor = JSON.parse(
        JSON.stringify(this.allDoctorList)
      ).filter(re => !re.group_id);
      // 不是新增时，数据回填需要将修改前已绑定过的医生信息加入到可选数据中
      if (this.oldTeamMembers.length > 0 && this.id) {
        isBindShowArr = this.allDoctorList.filter(re =>
          this.oldTeamMembers.some(r => r == re.doctor_id)
        );
      }

      if (this.oldGroupLeaderId && this.id) {
        let leaderInfo = this.allDoctorList.find(
          re => re.doctor_id == this.oldGroupLeaderId
        );
        if (leaderInfo) {
          isBindShowArr.push(leaderInfo);
        }
      }
      let arr = [...isBindShowArr, ...allCanBindDoctor];
      return arr;
    },
    // allAssistantList
    assistantList() {
      return this.allAssistantList.filter(re => re.status == 1);
    },
    // allAssistantList
    sellerList() {
      return this.allSellerList.filter(re => re.status == 1);
    },
    // allAssistantList
    productList() {
      let arr = [];
      let hash = {};
      arr = this.allProductList.reduce((item, next) => {
        if (!hash[next.product_name]) {
          hash[next.product_name] = next.product_id;
          item.push(next);
        } else if (
          this.ruleForm.porductType.some(re => re == next.product_id) &&
          hash[next.product_name] &&
          hash[next.product_name] != next.product_id
        ) {
          hash[next.product_name] = next.product_id;
          item.push(next);
        }
        // hash[next.product_name]?'':hash[next.product_name]=true&&item.push(next)
        return item;
      }, []);
      return arr;
    }
  },

  mounted() {
    this.getWardList();
    this.getHealthManagerList();
    this.getTherapistList();
    if (this.id) {
      this.getStudioInfo(this.id);
      if (this.dialogShowType == 1) this.getStudioProportion();
    }

    // 获取当前年月
    let now = new Date();
    this.currYear = now.getFullYear();
    this.currMonth = now.getMonth() + 1; //月份从0开始，所以需要加1
    this.day = now.getDate();
  },

  methods: {
    // 延迟发放
    delayedRelease(val) {
      this.expertId = val.expertId;
      this.ruleFormRelease.doctor = val.expertName;
      this.ruleFormRelease.studio = this.ruleForm.name;
      this.dialogVisible = true;
    },

    // 取消延迟确定
    submitRelease() {
      httpReq({
        url: "/group/fee/config/delay/cancel",
        method: "post",
        data: {
          businessId: this.expertId
        }
      }).then(res => {
        if (res.code == 1) {
          this.$message({
            message: `操作成功！`,
            type: "success"
          });
          this.sureDialogVisible = false;
          this.getStudioProportion();
        }
      });
    },

    // 取消延迟
    cancelRelease(val) {
      this.expertId = val.expertId;
      let year = val.startTime.slice(0, 4);
      let month = val.startTime.slice(5, 7);

      this.cancelDelayTips = `${year}年${Number(month)}月至${this.currYear}年${
        this.currMonth
      }月的顾问费将于${this.day > 10 ? "次月" : "当月"}发放`;
      this.sureDialogVisible = true;
    },

    // 获取顾问费发放信息
    getStudioProportion() {
      httpReq({
        url: "/group/fee/config/release/info",
        method: "post",
        data: {
          businessId: this.id
        }
      }).then(res => {
        this.sharingList = res.data.releaseList;
      });
    },

    // 提交
    submitFormRelease(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          httpReq({
            url: "/group/fee/config/delay/config",
            method: "post",
            data: {
              expertId: this.expertId,
              nextReleaseTime: `${this.year}-${this.month}-01`
            }
          }).then(res => {
            if (res.code == 1) {
              this.$message({
                message: `提交成功！顾问费将于${this.year}年${this.month}月发放。`,
                type: "success"
              });
              this.getStudioProportion();
              this.dialogVisible = false;
              this.$refs["ruleRelease"].resetFields();
            }
          });
        } else {
          return false;
        }
      });
    },
    // 取消
    cancelFormRelease() {
      if (this.ruleFormRelease.time) {
        this.$confirm(
          "您已对当前页面信息进行编辑，取消操作将对编辑内容不再保存, 是否继续?",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          }
        )
          .then(() => {
            this.dialogVisible = false;
            this.$refs["ruleRelease"].resetFields();
          })
          .catch(() => {});
      } else {
        this.dialogVisible = false;
      }
    },
    // 选择时间
    changeTime() {
      // 使用Date对象解析日期字符串
      const date = new Date(this.ruleFormRelease.time);

      // 提取年份和月份
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      this.year = year;
      this.month = month;

      this.tips = `${this.currYear}年${this.currMonth}月至${year}年${month -
        1}月的顾问费将于${year}年${month}月发放`;
    },

    //查询病区信息
    getWardList() {
      httpReq({
        url: "/doctors/getHospitalArea"
      }).then(res => {
        this.wardList = res.data;
      });
    },
    //查询关联销售
    searchList() {
      httpReq({
        url: "/doc/group/getSeller",
        method: "post",
        data: {
          hospitalId: this.ruleForm.hospitalId
        }
      }).then(res => {
        this.allSellerList = res.data;
      });
    },
    //查询关联健康管理师
    getHealthManagerList() {
      httpReq({
        url: "/doc/group/getCustomer",
        method: "get"
      }).then(res => {
        this.healthManagerList = res.data;
      });
    },
    //查询关联运动康复师
    getTherapistList() {
      httpReq({
        url: "/doc/group/rehabilitation",
        method: "get"
      }).then(res => {
        this.therapistList = res.data;
      });
    },
    //关联工作室组长
    querySearchGroupLeader(queryString, cb) {
      var restaurants = this.doctorList;
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      results.forEach(res => {
        res.value = res.name;
      });
      cb(results);
    },
    //关联医助
    querySearchAssistantId(queryString, cb) {
      var restaurants = this.allAssistantList;
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      results.forEach(res => {
        res.value = res.name;
      });

      cb(results);
    },
    //关联销售
    querySearchSellerId(queryString, cb) {
      var restaurants = this.allSellerList;
      var results = queryString
        ? restaurants.filter(this.createFilterSeller(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      results.forEach(res => {
        res.value = res.seller_name;
      });

      cb(results);
    },
    createFilter(queryString) {
      return restaurant => {
        return (
          restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) === 0
        );
      };
    },
    createFilterSeller(queryString) {
      return restaurant => {
        return (
          restaurant.seller_name
            .toLowerCase()
            .indexOf(queryString.toLowerCase()) === 0
        );
      };
    },
    querySearch(queryString, cb) {
      var restaurants = this.hospitalList;
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      results.forEach(res => {
        res.value = res.name;
      });
      cb(results);
    },
    async submitForm(formName) {
      await this.de.debounceEnd();
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.updateInfo(this.ruleForm);
        } else {
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.$parent.$parent.dialogFormVisibleEditBox = false;
    },
    //添加/修改信息
    updateInfo(info) {
      let doctors = info.teamMembers.map(v => {
        return {
          doctorId: v
        };
      });
      let products = info.porductType.map(v => {
        return {
          product_id: v
        };
      });
      httpReq({
        url: "/doc/group/addDocGroup",
        method: "post",
        data: {
          areaId: info.areaId, //病区id
          groupId: this.id ? this.id : "",
          groupName: info.name,
          hospitalId: info.hospitalId,
          type: info.studioType,
          doctorId: info.groupLeader,
          doctors, // [{"doctorId":"1000635"},{"doctorId":"1000641"}]
          assistantId: info.assistantId,
          sellerId: info.sellerId,
          customerId: info.customerId,
          rehabilitationId: info.rehabilitationId,
          realType: info.realType,
          products, // [{"product_id":"100410"}]
          groupDescription: info.remarks,
          operationVolume: info.operationVolume,
          alias: info.alias
        }
      })
        .then(res => {
          this.$message.success(`${this.id ? "修改" : "新增"}成功`);
          // 修改或新增后通知父组件更新数据
          this.$emit("updateList");
        })
        .catch(err => {
          this.$message.error(`失败：${err.msg}`);
        });
    },
    // 获取工作室信息
    getStudioInfo(groupId) {
      httpReq({
        url: "/doc/group/getDocGroupDetails",
        method: "post",
        data: {
          groupId
        }
      }).then(res => {
        this.ruleForm = {
          areaId: res.data.area_id, //这个是工作室的病区id
          hospitalId: res.data.hospital_id,
          operationVolume: res.data.operationVolume,
          name: res.data.group_name,
          rehabilitationId: res.data.rehabId,
          customerId: res.data.customerId,
          studioType: res.data.type,
          realType: res.data.realType,
          groupLeader: res.data.tdocName,
          teamMembers: res.data.docName
            ? JSON.parse(res.data.docName).map(v => {
                return Number(v.doctor_id);
              })
            : [],
          sellerId: res.data.seller_id,
          assistantId: res.data.assistant_id,
          porductType: res.data.productName
            ? JSON.parse(res.data.productName).map(v => {
                return Number(v.product_id);
              })
            : [],
          remarks: res.data.group_description,
          alias: res.data.alias
        };
        if (this.id) {
          this.oldGroupLeaderId = this.ruleForm.groupLeader
            ? JSON.parse(JSON.stringify(this.ruleForm.groupLeader))
            : "";
          this.oldTeamMembers = JSON.parse(
            JSON.stringify(this.ruleForm.teamMembers)
          );
        }
        this.searchList();
      });
    }
  }
};
</script>
<style lang="less" scoped>
.advisory-fee-mag {
  .title {
    font-weight: bold;
    font-size: 14px;
    color: #111111;
  }
  .detail-sharing {
    margin-top: 16px;
    .doctorName {
      display: flex;
      align-items: center;
      .group {
        width: 62px;
        height: 20px;
        background: rgba(64, 158, 255, 0.15);
        border-radius: 10px;
        color: #409eff;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 8px;
      }
      .effect {
        width: 56px;
        height: 20px;
        background: #fff9f0;
        border-radius: 2px;
        border: 1px solid #f17e2b;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 8px;
        font-size: 12px;
        color: #f17e2b;
      }
    }
    /deep/.el-table__header {
      .el-table__cell {
        background: #eef0f5;
        font-weight: bold;
        font-size: 14px;
        color: #3a4762;
      }
    }
  }
}
.edit-box {
  /deep/ .el-form {
    .minclass .el-form-item__content {
      width: 195px;
      margin-right: 10px;
    }

    .products_choose {
      max-height: 200px;
      overflow-y: scroll;
    }
  }

  .sub-btn {
    padding: 10px 0;
    text-align: center;
  }
}
/deep/.releaseDialog {
  .el-dialog__header,
  .el-dialog__body {
    padding: 0;
  }
  .content {
    padding: 24px;
    border-radius: 4px;
    .tip-footer {
      display: flex;
      justify-content: flex-end;
      margin-top: 45px;
    }
    .one {
      display: flex;
    }
    .image {
      width: 24px;
      height: 24px;
    }
    .msg {
      margin: 0 16px;
    }
    .close-image {
      width: 16px;
      height: 16px;
      cursor: pointer;
    }
  }
  .title {
    font-weight: bold;
    font-size: 16px;
    color: #111111;
    border-bottom: 1px solid #dfe4ec;
    padding: 16px 24px;
  }
  .form-box {
    margin-top: 24px;
  }
  .tips {
    margin-left: 200px;
    font-size: 12px;
    color: #999999;
    margin-top: -6px;
    margin-bottom: 24px;
  }
}
</style>
