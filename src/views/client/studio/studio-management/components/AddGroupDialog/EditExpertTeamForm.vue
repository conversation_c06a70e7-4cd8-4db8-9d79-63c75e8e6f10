<template>
<div class="editExpertTeamForm-wrapper">
  <div class="group-head">专家团队 <span>专家团队只能任命一个组长，可容纳多名组员</span></div>
  <div class="expertTeamForm-content">
    <div class="expertTeamForm-item" v-for="(item,index) in teamList" :key="index">
      <div class="form-item">
        <div class="form-label"><span>*</span>{{item.position}}</div>
        <div class="input-box">
          <el-select v-model="item.name" placeholder="请选择" class="select-box">
            <el-option label="测试工作室" value="1"></el-option>
          </el-select>
        </div>
      </div>
      <div class="form-item">
        <div class="form-label"><span>*</span>分配比例</div>
        <div class="icon-input-box">
          <el-input
            placeholder="请输入"
            v-model="item.percent"
            type="number"
          >
            <template slot="append">%</template>
          </el-input>
        </div>
      </div>
      <div class="delete-btn" v-if="item.position!=='组长'" @click="deleteMember(index)">移除</div>
    </div>
  </div>
  <div class="add-btn" @click="addTeamMember">
    <i class="el-icon-circle-plus-outline"></i>
    新增组员
  </div>
  <div class="sub-btn">
    <el-button size="mini" type="primary" @click="submitForm">保存</el-button>
    <el-button size="mini">取消</el-button>
  </div>
</div>
</template>

<script>
export default {
  name: "EditExpertTeamForm",
  data(){
    return{
      teamList:[
        {
          percent:null,
          position:'组长',
          name:''
        }
      ]
    }
  },
  computed:{
    allPercent(){
      const numbers = [];
      this.teamList.forEach(item=>{
        if (item.percent){
          numbers.push(parseInt(item.percent))
        }
      })
      return numbers.reduce((accumulator, currentValue) => {
        return accumulator + currentValue;
      }, 0)
    }
  },
  methods:{
    addTeamMember(){
      if (this.isFinishAllInfo()){
        let memberObj = {
          percent:null,
          position:'组员',
          name:''
        }
        this.teamList.push(memberObj)
        console.log(this.teamList,'-------')
      }else {
        this.$message.warning('请填写完所有内容!')
      }
    },
    isFinishAllInfo(){
      return this.teamList.every(item => item.name && item.percent !== null)
    },
    deleteMember(index){
      this.teamList.splice(index,1)
    },
    submitForm(){
      if (this.isFinishAllInfo()){
        if (this.allPercent===100){
          console.log('保存')
        }else {
          this.$message.warning('请重新分配比例至100%!')
        }
      }else {
        this.$message.warning('请填写完所有内容!')
      }
    }

  }
}
</script>

<style scoped lang="less">
.editExpertTeamForm-wrapper{
  margin-top: 24px;
  .group-head{
    font-weight: bold;
    font-size: 16px;
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    span{
      font-weight: 400;
      font-size: 14px;
      color: #939CAE;
      margin-left: 8px;
    }
  }
  .expertTeamForm-content{
    display: flex;
    flex-direction: column;
    /deep/.expertTeamForm-item{
      box-sizing: border-box;
      background: #F7F8FA;
      padding: 16px;
      display: flex;
      width: 100%;
      position: relative;
      margin-bottom: 8px;
      .form-item{
        margin-right: 40px;
        .form-label{
          font-weight: bold;
          font-size: 14px;
          color: #3A4762;
          margin-bottom: 8px;
          span{
            color: #F56C6C;
            margin-right: 4px;
          }
        }
      }
      .input-box{
        .el-input__inner {
          width: 220px;
          height: 32px;
          line-height: 32px;
          font-size: 14px;
          border-radius: 2px;
          &::placeholder {
            color: #909399;
          }
        }
        .select-box .el-icon-arrow-up:before {
          content: "";
        }
        .select-box .el-icon-arrow-down:before {
          content: "\e790";
        }
        .el-select .el-input .el-input__icon{
          line-height: 32px;
        }
      }
      .icon-input-box{
        .el-input-group{
          .el-input__inner{
            width: 76px;
            height: 32px;
            line-height: 32px;
            border-radius: 2px;
          }
          .el-input-group__append{
            width: 40px;
            padding: 0;
            border-radius: 2px;
            text-align: center;
          }
        }
        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
          -webkit-appearance: none;
        }
      }
      .delete-btn{
        font-weight: 400;
        font-size: 14px;
        color: #E63746;
        cursor: pointer;
        position: absolute;
        right: 16px;
        top: 16px;
      }
    }
  }
  .add-btn{
    font-size: 14px;
    display: flex;
    color: #2E6BE6;
    cursor: pointer;
    font-weight: normal;
    align-items: center;
    margin-right: 8px;
    margin-top: 12px;
  }
  .sub-btn {
    box-sizing: border-box;
    width: 100%;
    text-align: center;
    .el-button {
      width: 104px;
      height: 36px;
      font-size: 14px;
      color: #ffffff;
      background: #2E6BE6;
      margin-left: 20px;
      border-radius: 2px;
      &:last-of-type {
        color: #303133;
        background: #fff;
        border: 1px solid #dcdfe6;
      }
    }
  }
}
</style>
