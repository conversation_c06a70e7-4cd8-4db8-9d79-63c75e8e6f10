<template>
<div>
  <CommonDialog
    @cancel="cancel"
    @confirm="confirm"
    :visible.sync="visible"
    width="560px"
    :title="title">
    <div class="content-wrapper">
      <div class="edit-box">
        <el-form
          :model="ruleFormRelease"
          :rules="rulesRelease"
          ref="ruleRelease"
          class="self-rule-from"
        >
          <el-form-item label="所属工作室" prop="studio">
            <el-input
              placeholder="请填写所属工作室"
              v-model="ruleFormRelease.studio"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="医生" prop="doctor">
            <el-input
              placeholder="请填写所属工作室"
              v-model="ruleFormRelease.doctor"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="下次发放时间" prop="time">
            <el-date-picker
              type="month"
              placeholder="选择下次发放时间"
              v-model="ruleFormRelease.time"
              :picker-options="pickerOptions"
              @change="changeTime"
              :clearable="false"
            ></el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div class="tips">{{ tips }}</div>
    </div>

  </CommonDialog>
</div>
</template>

<script>
import CommonDialog from "../../../../../components/CommonDialog.vue";
import { httpReq } from "@/http";

export default {
  name: "DelayedConsultingFeeDialog",
  components:{CommonDialog},
  props: {
    visible: {
      require: true,
      type:Boolean
    },
    title: {
      require: true,
      type:String
    },
    expertId:{
      require: true,
      type:Number
    },
    ruleForm:{
      require: true,
      type:Object,
      default:()=>{}
    }
  },
  data(){
    return{
      ruleFormRelease: {
        studio: "",
        doctor: "",
        time: ""
      },
      rulesRelease: {
        studio: [
          { required: true, message: "请输入所属工作室", trigger: "blur" }
        ],
        doctor: [{ required: true, message: "请选择医生", trigger: "change" }],
        time: [
          {
            type: "date",
            required: true,
            message: "请选择日期",
            trigger: "change"
          }
        ]
      },
      pickerOptions: {
        disabledDate(time) {
          // 获取当前日期
          const currentDate = new Date();
          // 计算下下个月的日期
          const nextNextMonth = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth() + 2,
            1
          );
          // 如果选择的日期小于下下个月的日期，则禁用该日期
          return time.getTime() < nextNextMonth.getTime();
        }
      },
      tips: "",
      year: "",
      month: "",
      currYear: "",
      currMonth: "",
    }
  },
  created() {
    console.log('加载了')
    this.ruleFormRelease.doctor = this.ruleForm.doctor
    this.ruleFormRelease.studio = this.ruleForm.studio
    let now = new Date();
    this.currYear = now.getFullYear();
    this.currMonth = now.getMonth() + 1;  //月份从0开始，所以需要加1
  },
  methods:{
    cancel(){
      this.cancelFormRelease()
    },
    confirm(){
      this.submitFormRelease()
    },
    // 选择时间
    changeTime() {
      // 使用Date对象解析日期字符串
      const date = new Date(this.ruleFormRelease.time);

      // 提取年份和月份
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      this.year = year;
      this.month = month;

      this.tips = `${this.currYear}年${this.currMonth}月至${year}年${month -
      1}月的顾问费将于${year}年${month}月发放`;
    },
    // 提交
    submitFormRelease() {
      this.$refs['ruleRelease'].validate(valid => {
        if (valid) {
          httpReq({
            url: "/group/fee/config/delay/config",
            method: "post",
            data: {
              expertId: this.expertId,
              nextReleaseTime: `${this.year}-${this.month}-01`
            }
          }).then(res => {
            if (res.code == 1) {
              this.$message({
                message: `提交成功！顾问费将于${this.year}年${this.month}月发放。`,
                type: "success"
              });
              this.$refs["ruleRelease"].resetFields();
              this.$emit('confirm')
            }
          });
        } else {
          return false;
        }
      });
    },
    // 取消
    cancelFormRelease() {
      if (this.ruleFormRelease.time) {
        this.$confirm('<i class="el-icon-warning"></i>您已对当前页面信息进行编辑，取消操作将对编辑内容不再保存, 是否继续?', {
          title: '提示!',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          customClass: 'messageTips'
        }).then(() => {
          this.$refs["ruleRelease"].resetFields();
          this.$emit('update:visible', false)
        }).catch(() => {});
      } else {
        this.$emit('update:visible', false)
      }
    },
  }
}
</script>

<style scoped lang="less">
.content-wrapper{
  width: 100%;
  box-sizing: border-box;
  padding: 24px 68px;
  text-align: left;
  color: #15233F;
  .head-label{
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 8px;
    span{
      cursor: pointer;
      font-weight: normal;
      font-size: 14px;
      color: #2E6BE6;
    }
  }
}
.edit-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: auto;
  /deep/.el-form.self-rule-from {
    margin-bottom: 32px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .el-form-item{
      display: flex;
      align-items: center;
      .el-form-item__label {
        width: 130px;
        line-height: 40px;
        padding-right: 20px;
      }
      .el-form-item__content {
        width: 240px;
        line-height: 40px;
        border-radius: 4px;
        .el-input__inner {
          width: 240px;
          height: 40px;
          line-height: 40px;
          padding: 13px 15px;
          font-size: 14px;
          border-radius: 2px;
          &::placeholder {
            color: #909399;
          }
          // border-radius: 4px;
        }
        .el-date-editor{
          .el-input__inner{
            padding-left: 36px;
          }
        }
      }
    }
  }
  /deep/.el-textarea{
    .el-textarea__inner{
      font-family: PingFangSC-Regular, PingFang SC;
      min-height: 60px !important;
    }
  }
}
</style>
