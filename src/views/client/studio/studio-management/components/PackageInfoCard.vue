<template>
  <div class="content-wrapper">
    <div class="data-box box">
      <div class="title-box">
        <div class="group-name">产品服务</div>
      </div>
      <div class="table">
        <el-table
          :data="tableData"
          style="width: 100%"
          ref="table"
          :header-cell-style="{
              color: '#15233F',
              background: '#F7F8FA',
              'font-weight': 'bold',
              'font-size': '14px'
          }"
        >
          <el-table-column align="center" label="序号" width="60" fixed>
            <template slot-scope="scope">
              {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="productName" align="left" label="产品名称" width="250">
          </el-table-column>
          <el-table-column prop="productPrice" align="left" label="费用" width="150">
            <template slot-scope="scope">
              {{scope.row.productPrice}}/年
            </template>
          </el-table-column>
          <el-table-column prop="productStatus" align="left" label="服务包状态">
            <template slot-scope="scope">
              <span class="normal-icon" v-if="scope.row.productStatus===1"></span>
              <span class="danger-icon" v-if="scope.row.productStatus===2"></span>
              {{scope.row.productStatus===1? '启用' :'禁用'}}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import {getProductTableDataReq} from "../../../../../api/client";

export default {
  name: "PackageInfoCard",
  props: {
    groupId:{
      require: true,
    }
  },
  data(){
    return{
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 1,
    }
  },
  created() {
    this.getTableData()
  },
  methods:{
    getTableData(){
      getProductTableDataReq({groupId:this.groupId}).then(res=>{
        this.tableData = res.data
      })
    }
  }
}
</script>

<style scoped lang="less">
.content-wrapper{
  margin-bottom: 16px;
  box-sizing: border-box;
  padding: 24px;
  background: #FFFFFF;
  font-family: PingFangSC, PingFang SC;
  .data-box {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #FFFFFF;
    border-radius: 6px;
    .function-box {
      text-align: left;
      .title {
        font-weight: bold;
        font-size: 16px;
        color: #101B25;
      }
    }
    .table {
      flex: 1;
    }
    .el-pagination {
      text-align: right;
    }
    .title-box{
      margin-bottom: 12px;
      .group-name{
        font-weight: bold;
        font-size: 16px;
        color: #3A4762;
      }
    }
    .function-box{
      display: flex;
      margin: 12px 0;
      .add-btn{
        font-size: 14px;
        display: flex;
        color: #2E6BE6;
        cursor: pointer;
        font-weight: normal;
        align-items: center;
        margin-right: 8px;
      }

    }
  }
}
/deep/.el-pagination {
  margin-top: 16px;
  text-align: center !important;
}
.normal-icon{
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #2FB324;
  border-radius: 50%;
}
.danger-icon{
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #E63746;
  border-radius: 50%;
}
</style>
