<template>
<div class="content-wrapper">
  <div class="data-box box">
    <div class="title-box">
      <div class="group-name">院内团队</div>
    </div>
    <div class="function-box">
      <div class="add-btn" @click="visible=true">
        <i class="el-icon-circle-plus-outline"></i>
        调整分配比例
      </div>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        style="width: 100%"
        ref="table"
        max-height="550px"
        :header-cell-style="{
              color: '#15233F',
              background: '#F7F8FA',
              'font-weight': 'bold',
              'font-size': '14px'
          }"
      >
        <el-table-column align="center" label="序号" width="60" fixed>
          <template slot-scope="scope">
            {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" align="left" label="成员姓名" width="100">
        </el-table-column>
        <el-table-column prop="identity" align="left" label="成员身份" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.identity==='LEADER'">组长</span>
            <span v-if="scope.row.identity==='MEMBER'">组员</span>
          </template>
        </el-table-column>
        <el-table-column prop="position" align="left" label="院内职务" width="400">
          <template slot-scope="scope" v-if="scope.row.position">
            <span v-for="(item,index) in scope.row.position" :key="index">
              <span>
                {{item.deptName}}({{item.positionName}})
              </span>
              <span v-if="index+1!==scope.row.position.length && scope.row.position.length!==1">,</span>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="proportion" align="left" label="分成比例" width="100">
          <template slot-scope="scope">
            {{scope.row.proportion}}%
          </template>
        </el-table-column>
        <el-table-column prop="expertAccount" align="left" label="专家端账号" width="150">
        </el-table-column>
        <el-table-column prop="joinTime" align="left" label="加入时间">
        </el-table-column>
      </el-table>
    </div>
  </div>
  <AdjustPercentDialog
    :visible.sync="visible"
    title="调整分配比例"
    v-if="visible"
    :groupId="groupId"
    @submitSuccess="submitSuccess"
  >
  </AdjustPercentDialog>
</div>
</template>

<script>
import AdjustPercentDialog from "./AdjustPercentDialog.vue";
import {getHosTeamListReq} from "../../../../../api/client";
export default {
  name: "HospitalGroupInfo",
  components:{AdjustPercentDialog},
  props: {
    groupId:{
      require: true,
    }
  },
  data(){
    return{
      visible:false,
      tableData:[],
      currentPage:1,
      pageSize:10
    }
  },
  created() {
    this.getTableData()
  },
  methods:{
    submitSuccess() {
      this.$emit('submitSuccess')
    },
    getTableData(){
      getHosTeamListReq({groupId:this.groupId}).then(res=>{
      console.log('$debug: res', res);
        this.tableData = res.data
      })
    }
  }
}
</script>

<style scoped lang="less">
.content-wrapper{
  margin-bottom: 16px;
  box-sizing: border-box;
  padding: 24px;
  background: #FFFFFF;
  font-family: PingFangSC, PingFang SC;
  .data-box {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #FFFFFF;
    border-radius: 6px;
    .function-box {
      text-align: left;
      .title {
        font-weight: bold;
        font-size: 16px;
        color: #101B25;
      }
    }
    .table {
      flex: 1;
    }
    .el-pagination {
      text-align: right;
    }
    .title-box{
      .group-name{
        font-weight: bold;
        font-size: 16px;
        color: #3A4762;
      }
    }
    .function-box{
      display: flex;
      margin: 12px 0;
      .add-btn{
        font-size: 14px;
        display: flex;
        color: #2E6BE6;
        cursor: pointer;
        font-weight: normal;
        align-items: center;
        margin-right: 8px;
      }

    }
  }
}
/deep/.el-pagination {
  margin-top: 16px;
  text-align: center !important;
}
</style>
