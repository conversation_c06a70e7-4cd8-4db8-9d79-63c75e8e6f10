<template>
  <div class="content-wrapper">
    <div class="top-content">
      <div class="avatar-box">
        {{info.groupName? info.groupName[0] : ''}}
      </div>
      <div class="title-box">
        <div class="hos-name">
          {{info.groupName}}
          <span class="tag" v-if="info.groupType==='NORMAL'">普通工作室</span>
          <span class="tag" v-if="info.groupType==='RESEARCH'">科研工作室</span>
          <span class="edit-btn" @click="showAddGroupDialogVisible = true"><i class="el-icon-edit"></i>编辑工作室信息</span>
        </div>
        <div class="alias">别名：{{info.groupAlias || '--'}}</div>
      </div>
    </div>
    <div class="hos-info">
      <div class="info-item">
        <div class="label">所属医院：</div>
        <div class="info">{{info.hospitalName}}</div>
      </div>
      <div class="info-item">
        <div class="label">所属工作小组：</div>
        <div class="info">{{info.deptName}}</div>
      </div>
      <div class="info-item">
        <div class="label">预估年手术量：</div>
        <div class="info">{{info.yearOperation || '--'}}</div>
      </div>
      <div class="info-item">
        <div class="label">工作室简介：</div>
        <el-tooltip class="item" effect="dark" :content="info.groupIntro || null" placement="top">
          <div class="info">{{info.groupIntro || '--'}}</div>
        </el-tooltip>
      </div>
    </div>
    <div class="surgery-data">
      <div class="data-label">预计年手术量：</div>
      <div class="data-items">
        <template v-if="info.surgicalVolume">
          <div class="data-item" v-for="item in info.surgicalVolume" :key="item.month">
            <div class="month-num">{{item.month}}月</div>
            <div class="surgery-num">{{item.volume}}</div>
          </div>
        </template>
      </div>
    </div>
    <div class="count-data">
      <div class="count-item">
        <div class="label">累积转化：</div>
        <div class="count-num">{{numInfo.totalConvert}}</div>
      </div>
      <div class="count-item">
        <div class="label">转化率：</div>
        <div class="count-num">{{(numInfo.convertRate * 100).toFixed(2)}}%</div>
      </div>
    </div>
    <AddGroupDialog
      :visible.sync="showAddGroupDialogVisible"
      title="编辑工作室"
      v-if="showAddGroupDialogVisible"
      :groupId="groupId"
      @confirmSuccess="addGroupSucceed"
    >
    </AddGroupDialog>
  </div>
</template>

<script>
import AddGroupDialog from "./AddGroupDialog/AddGroupDialog.vue";
import {getBasicGroupDetailReq} from "../../../../../api/client";
export default {
  name: "StudioInfoCard",
  components:{AddGroupDialog},
  props: {
    groupId:{
      require: true,
    },
    numInfo:{
      require: true,
    }
  },
  data(){
    return{
      showAddGroupDialogVisible:false,
      info:{}
    }
  },
  created() {
    this.getDetails()
  },
  methods:{
    getDetails(){
      getBasicGroupDetailReq({groupId:this.groupId}).then(res=>{
        if (Array.isArray(res.data.surgicalVolume)){
          res.data['yearOperation'] = res.data.surgicalVolume.reduce((accumulator, currentValue) => accumulator + currentValue.volume, 0)
        }else {
          res.data['yearOperation'] = '--'
          res.data.surgicalVolume = []
        }
        this.info = res.data
        this.$emit('getGroupName',this.info.groupName)
      })
    },
    addGroupSucceed(){
      this.getDetails()
      this.$emit('confirmEditedGroup')
    }
  }
}
</script>

<style scoped lang="less">
.content-wrapper{
  margin-top: 16px;
  margin-bottom: 16px;
  box-sizing: border-box;
  padding: 24px;
  background: #FFFFFF;
  font-family: PingFangSC, PingFang SC;

.top-content{
  display: flex;
  align-items: center;
.avatar-box{
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #2E6BE6;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #FFFFFF;
  font-weight: bold;
  font-size: 30px;
img{
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}
}
.title-box{
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 26px;
  .hos-name{
    font-weight: bold;
    font-size: 20px;
    color: #3A4762;
    .tag{
      background: linear-gradient( 123deg, #C57C50 0%, #9F4834 100%);
      border-radius: 2px;
      box-sizing: border-box;
      padding: 2px 4px;
      font-size: 12px;
      color: #FFFFFF;
    }
  }
.alias{
  font-weight: 400;
  font-size: 16px;
  color: #3A4762;
}
}
}
.hos-info{
  box-sizing: border-box;
  padding: 24px 0;
  display: grid;
  grid-template-columns:  1fr 1fr 1fr 1fr;
  grid-row-gap: 8px;
  border-bottom: 1px solid #E9E8EB;
.info-item{
  display: flex;
  font-weight: 400;
  font-size: 14px;
  align-items: center;
  flex: 1;
  max-width: 400px;
.label{
  width: 100px;
  color: #939CAE;
  text-align: left;
}
.info{
  color: #3A4762;
  white-space: nowrap; /* 保证文本在一行内显示 */
  overflow: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis;
  flex-wrap: nowrap;
}
}
}
.surgery-data{
  box-sizing: border-box;
  padding-top: 24px;
  display: flex;
  flex-wrap: wrap;
  .data-label{
    color: #939CAE;
    text-align: left;
    font-size: 14px;
  }
  .data-items{
      display: flex;
      flex: 1;
      flex-wrap: wrap;
      .data-item{
        width: 100px;
        height: 80px;
        background: #F2F6FF;
        border-radius: 4px;
        margin-right: 8px;
        font-weight: 400;
        text-align: right;
        box-sizing: border-box;
        padding: 12px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .month-num{
          width: 100%;
          font-size: 12px;
          color: #7A8599;
        }
        .surgery-num{
          font-size: 14px;
          color: #3A4762;
        }
      }
    }
  }
}
.count-data{
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  margin-top: 24px;
  .count-item{
    display: flex;
    align-items: center;
    .label{
      font-weight: 400;
      font-size: 14px;
      color: #939CAE;
      margin-right: 24px;
    }
    .count-num{
      font-weight: bold;
      font-size: 20px;
      color: #15233F;
      width: 200px;
    }
  }
}
.edit-btn {
  margin-left: 8px;
  font-weight: 400;
  font-size: 14px;
  color: #2C89DC;
  cursor: pointer;
}
</style>
