<template>
  <div class="wrapper">
    <StudioInfoCard :groupId="groupId" @confirmEditedGroup="confirmEditedGroup"
                    @getGroupName="getGroupName" :numInfo="numInfo"></StudioInfoCard>
    <HrtGroupInfoCard :groupId="groupId" ref="hrtGroupInfoCardRef"></HrtGroupInfoCard>
    <HospitalGroupInfo :groupId="groupId" ref="hospitalGroupInfoRef" @submitSuccess="submitSuccess"></HospitalGroupInfo>
    <PackageInfoCard :groupId="groupId" ref="packageInfoCardRef"></PackageInfoCard>
    <ConsultingFeePaymentInfoCard :groupId="groupId" :groupName="groupName" ref="consultingFeePaymentInfoCardRef"></ConsultingFeePaymentInfoCard>
  </div>
</template>

<script>
import StudioInfoCard from "./components/StudioInfoCard.vue";
import HrtGroupInfoCard from "./components/HrtGroupInfoCard.vue";
import HospitalGroupInfo from "./components/HospitalGroupInfo.vue";
import PackageInfoCard from "./components/PackageInfoCard.vue";
import ConsultingFeePaymentInfoCard from "./components/ConsultingFeePaymentInfoCard.vue";
export default {
  name: "studio-details",
  components:{
    StudioInfoCard,
    HrtGroupInfoCard,
    HospitalGroupInfo,
    PackageInfoCard,
    ConsultingFeePaymentInfoCard
  },
  data(){
    return{
      groupId:null,
      groupName:'',
      numInfo:{
        //累计转化
        totalConvert:null,
        //工作室转化率
        convertRate:null
      }
    }
  },
  created() {
    this.groupId = parseInt(this.$route.query.groupId)
    this.numInfo.totalConvert = this.$route.query.totalConvert
    this.numInfo.convertRate = this.$route.query.convertRate
  },
  methods:{
    submitSuccess() {
      this.$refs["consultingFeePaymentInfoCardRef"].getStudioProportion()
    },
    confirmEditedGroup(){
      this.$refs["hrtGroupInfoCardRef"].getTableData()
      this.$refs["hospitalGroupInfoRef"].getTableData()
      this.$refs["packageInfoCardRef"].getTableData()
    },
    getGroupName(groupName){
      this.groupName = groupName
    }
  }
}
</script>

<style scoped lang="less">
.wrapper{
  width: 100%;
  height: 90%;
  padding: 0 16px;
  box-sizing: border-box;
  text-align: left;
  overflow-y: auto;
}
</style>
