<template>
  <div class="positionChangeInfoCard-wrapper">
    <div class="data-box box">
      <div class="title-box">
        <div class="group-name">任职记录</div>
      </div>
      <div class="table">
        <el-table
          :data="tableData"
          style="width: 100%"
          ref="table"
          :header-cell-style="{
              color: '#15233F',
              background: '#F7F8FA',
              'font-weight': 'bold',
              'font-size': '14px'
          }"
        >
          <el-table-column align="center" label="序号" width="60" fixed>
            <template slot-scope="scope">
              {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column prop="doctorName" align="left" label="人员">
          </el-table-column>
          <el-table-column prop="visitNum" align="left" label="当前职务拜访">
          </el-table-column>
          <el-table-column prop="officeTime" align="left" label="任职开始时间">
          </el-table-column>
          <el-table-column prop="resignationTime" align="left" label="任职结束时间">
          </el-table-column>
          <el-table-column prop="userName" align="left" label="调整人">
          </el-table-column>
          <el-table-column prop="modifyTime" align="left" label="调整时间">
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-sizes="[10, 20, 50]"
        :page-size="pageSize"
        layout="total,sizes, prev, pager, next"
        :total="total"
        size="mini"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import {getPositionInfoChangeRecordsReq} from "../../../../../api/client";

export default {
  name: "PositionChangeInfoCard",
  props: {
    doctorId:{
      require: true,
    }
  },
  data(){
    return{
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 1,
    }
  },
  created() {
    this.getTableData()
  },
  methods:{
    // 切换每页条数
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.pageSize = val;
      this.getTableData(1,this.pageSize)
    },
    // 切换页码
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.currentPage = val
      this.getTableData(val,this.pageSize)
    },
    getTableData(pageNumber,pageSize){
      getPositionInfoChangeRecordsReq({doctorId:this.doctorId,pageNumber,pageSize}).then(res=>{
        this.tableData = res.data.contents
        this.total =  res.data.total
      })
    }
  }
}
</script>

<style scoped lang="less">
.positionChangeInfoCard-wrapper{
  margin-bottom: 16px;
  box-sizing: border-box;
  padding: 24px;
  background: #FFFFFF;
  font-family: PingFangSC, PingFang SC;
  .data-box {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #FFFFFF;
    border-radius: 6px;
    .function-box {
      text-align: left;
      .title {
        font-weight: bold;
        font-size: 16px;
        color: #101B25;
      }
    }
    .table {
      flex: 1;
    }
    .el-pagination {
      text-align: center;
    }
    .title-box{
      margin-bottom: 12px;
      .group-name{
        font-weight: bold;
        font-size: 16px;
        color: #101B25;
      }
    }
    .function-box{
      display: flex;
      margin: 12px 0;
      .add-btn{
        font-size: 14px;
        display: flex;
        color: #2E6BE6;
        cursor: pointer;
        font-weight: normal;
        align-items: center;
        margin-right: 8px;
      }

    }
  }
}
/deep/.el-pagination {
  margin-top: 16px;
  text-align: center !important;
}
.normal-icon{
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #2FB324;
  border-radius: 50%;
}
.danger-icon{
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #E63746;
  border-radius: 50%;
}
</style>
