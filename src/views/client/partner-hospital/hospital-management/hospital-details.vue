<template>
<div class="wrapper">
  <HospitalInfoCard :hospitalId="hospitalId" ref="hospitalInfoCardRef" @hospitalInfoUpdate="hospitalInfoUpdate"></HospitalInfoCard>
  <StudioInfoCard :hospitalId="hospitalId" ref="studioInfoCardRef"></StudioInfoCard>
  <OkrInfoCard :hospitalId="hospitalId" @confirmSuccess="confirmSuccess"></OkrInfoCard>
</div>
</template>

<script>
import HospitalInfoCard from "./components/HospitalInfoCard.vue";
import StudioInfoCard from "./components/StudioInfoCard.vue";
import OkrInfoCard from "./components/OkrInfoCard.vue";
export default {
  name: "hospital-details",
  components:{HospitalInfoCard,StudioInfoCard,OkrInfoCard},
  data(){
    return{
      drawerVisible:false,
      hospitalId:"",
      hosInfo:{
        baseInfo:{},
        region:'',
        keys:[]
      },
      //是否完成架构
      isFinish:false,
      //是否建立工作室 大于0建立了
      isGroup:0,
      orgChartData:[
        {
          name: '',
          positionName:'',
          isFinish:4,
          child:[]
        }
      ],
      //医生个人详情
      doctorInfo:{},
      //医生子女信息
      doctorChildInfo:[],
      //进度记录
      taskFlowRecords:[],
      //操作日志
      operationLogs:[],
      logCurrentPage: 1,
      logTotal: 0,
      recordsCurrentPage:1,
      recordsTotal:0,

    }
  },
  created() {
    this.hospitalId = parseInt(this.$route.query.hospitalId)
  },
  mounted() {
  },
  methods:{
    confirmSuccess() {
      if (this.$refs['hospitalInfoCardRef'].getHospitalInfo) {
        this.$refs['hospitalInfoCardRef'].getHospitalInfo()
      }
    },
    hospitalInfoUpdate(){
      this.$refs['studioInfoCardRef'].getNum()
    }
  }
}
</script>

<style scoped lang="less">
.wrapper{
  width: 100%;
  height: 90%;
  padding: 0 16px;
  box-sizing: border-box;
  text-align: left;
  overflow-y: auto;
}
</style>

