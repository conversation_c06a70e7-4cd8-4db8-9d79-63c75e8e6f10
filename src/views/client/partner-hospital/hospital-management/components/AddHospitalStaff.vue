<template>
  <div>
    <CommonDialog
      @cancel="cancel"
      @confirm="confirm"
      :visible.sync="visible"
      :title="title"
      width="1200px"
      @open="openDialog"
    >
      <div class="content-wrapper">
        <el-form :model="staffForm" :rules="rules" ref="staffFormRef" class="rule-from-style">
          <div class="form-group">
            <div class="group-head">
              <span></span>
              <div>基本信息</div>
            </div>
            <div class="sub-form">
              <el-form-item label="姓名" prop="name">
                <el-input
                  v-model="staffForm.name"
                  :maxlength="10"
                  placeholder="请输入"
                  oninput="(value) = value.replace(/\s*/g,'')"
                ></el-input>
              </el-form-item>
              <el-form-item label="性别" prop="gender">
                <el-select v-model="staffForm.gender" placeholder="请选择" class="select-box">
                  <el-option
                    :label="item.name"
                    :value="item.value"
                    v-for="item in genderOptions"
                    :key="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="医院" prop="hospitalId" v-if="!hospitalId">
                <el-select
                  v-model="staffForm.hospitalId"
                  filterable
                  placeholder="请选择"
                  class="select-box"
                  @change="changeHospitalId"
                >
                  <el-option
                    :label="item.hospitalName"
                    :value="item.hospitalId"
                    v-for="item in hospitalOptions"
                    :key="item.hospitalId"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="职称" prop="jobTitle">
                <el-select
                  v-model="staffForm.jobTitle"
                  clearable
                  placeholder="请选择"
                  class="select-box"
                >
                  <el-option
                    :label="item.name"
                    :value="item.value"
                    v-for="item in jobTitleOption"
                    :key="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="毕业院校" prop="school">
                <el-input
                  v-model="staffForm.school"
                  placeholder="请输入"
                  :maxlength="20"
                  oninput="(value) = value.replace(/\s*/g,'')"
                ></el-input>
              </el-form-item>
              <el-form-item label="最高学历" prop="education">
                <el-select
                  v-model="staffForm.education"
                  placeholder="请选择"
                  clearable
                  class="select-box"
                >
                  <el-option
                    :label="item.name"
                    :value="item.value"
                    v-for="item in educationOptions"
                    :key="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="学术任职" prop="academicPost">
                <el-input
                  v-model="staffForm.academicPost"
                  placeholder="请输入"
                  :maxlength="100"
                  oninput="(value) = value.replace(/\s*/g,'')"
                ></el-input>
              </el-form-item>
              <el-form-item label="" prop=""> </el-form-item>
              <el-form-item label="" prop="" v-if="hospitalId"> </el-form-item>
              <el-form-item label="简介" prop="briefIntroduction" class="large-form-item">
                <el-input
                  v-model="staffForm.briefIntroduction"
                  placeholder="请输入"
                  class="large-size-input"
                  type="textarea"
                  :rows="3"
                  maxlength="500"
                  show-word-limit
                ></el-input>
              </el-form-item>
              <el-form-item label="专业擅长" prop="major" class="large-form-item">
                <el-input
                  v-model="staffForm.major"
                  placeholder="请输入"
                  class="large-size-input"
                  type="textarea"
                  :rows="3"
                  maxlength="500"
                  show-word-limit
                ></el-input>
              </el-form-item>
              <el-form-item label="简历" prop="curriculum">
                <div class="block">
                  <div
                    class="img-box"
                    v-for="(item, index) in staffForm.curriculum"
                    :key="item.url"
                  >
                    <el-image
                      v-if="acceptImageTypeArr.includes(item.fileType)"
                      style="width: 100px; height: 100px"
                      :src="item.url"
                      :preview-src-list="imgReviewList"
                      fit="cover"
                    >
                    </el-image>
                    <div
                      class="el-file"
                      v-if="acceptFileTypeArr.includes(item.fileType)"
                      @click="urlDownload(item.url)"
                    >
                      <div>{{ item.fileType }}</div>
                      <div style="font-size: 8px">{{ item.fileName }}</div>
                    </div>
                    <i class="el-icon-error" @click="deleteResume(index)"></i>
                  </div>
                  <div @click="uploadPic">
                    <el-avatar shape="square" :size="100" icon="el-icon-upload2" fit="cover">
                    </el-avatar>
                  </div>
                  <input
                    type="file"
                    ref="evfileHosStaff"
                    @change="zh_uploadFile_change"
                    style="display: none"
                  />
                </div>
              </el-form-item>
              <el-form-item label="头像" prop="profilePhoto">
                <div class="block">
                  <div class="img-box" v-if="staffForm.profilePhoto">
                    <el-image
                      style="width: 100px; height: 100px"
                      :src="staffForm.profilePhoto"
                      :preview-src-list="[staffForm.profilePhoto]"
                      fit="cover"
                    >
                    </el-image>
                    <i class="el-icon-error" @click="staffForm.profilePhoto = ''"></i>
                  </div>
                  <div @click="photo_uploadPic" v-if="!staffForm.profilePhoto" class="el-file">
                    <el-avatar shape="square" :size="100" icon="el-icon-upload2" fit="cover">
                    </el-avatar>
                    <span class="text-btn">点击上传，支持JPG、JPEG等格式</span>
                  </div>
                  <input
                    type="file"
                    ref="evfileAccount"
                    @change="photo_zh_uploadFile_change"
                    style="display: none"
                  />
                </div>
              </el-form-item>
            </div>
            <div class="group-head">
              <span></span>
              <div>职务信息</div>
            </div>
            <div class="sub-form">
              <div class="custom-form">
                <div
                  class="custom-form-item"
                  v-for="(positionItem, index) in positionForm"
                  :key="index"
                >
                  <div class="custom-item">
                    <div class="label">
                      <span>*</span>
                      部门
                    </div>
                    <el-select
                      v-model="positionItem.deptId"
                      placeholder="请选择"
                      class="select-box"
                      @change="(value) => handleChangeDep(value, index)"
                    >
                      <el-option
                        :label="item.deptName"
                        :value="item.deptId"
                        v-for="item in depOptions"
                        :key="item.deptId"
                      ></el-option>
                    </el-select>
                  </div>
                  <div class="custom-item">
                    <div class="label">
                      <span>*</span>
                      职务
                    </div>
                    <el-select
                      v-model="positionItem.position.positionId"
                      placeholder="请选择"
                      class="select-box"
                      @change="(value) => handleChangePosition(value, index, positionItem.deptType)"
                    >
                      <el-option
                        :label="item.positionName"
                        :value="item.positionId"
                        v-for="item in positionDepTypeMap[positionItem.deptType]"
                        :key="item.positionId"
                      ></el-option>
                    </el-select>
                    <span class="delete-btn" v-if="index !== 0">
                      <el-button type="text" size="mini" @click="deletePosition(index)"
                        >删除</el-button
                      >
                    </span>
                  </div>
                </div>
                <div class="add-btn" @click="addPosition">
                  <i class="el-icon-circle-plus-outline"></i>
                  添加职务
                </div>
              </div>
            </div>
            <div class="group-head">
              <span></span>
              <div>项目推动</div>
            </div>
            <div class="sub-form">
              <el-form-item label="是否关键决策人" prop="isKey">
                <el-radio-group v-model="staffForm.isKey" style="width: 240px">
                  <el-radio :label="item.value" v-for="item in isKeyOptions" :key="item.value">{{
                    item.name
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="推手类型" prop="pushType">
                <el-radio-group v-model="staffForm.pushType">
                  <el-radio
                    :label="item.value"
                    v-for="item in pushTypeOptions"
                    :key="item.value"
                    @click.native.prevent="customRadioPushTypeClickHandler(item)"
                    >{{ item.name }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="对付费认知" prop="payPerceive">
                <el-select
                  v-model="staffForm.payPerceive"
                  clearable
                  placeholder="请选择"
                  class="select-box"
                >
                  <el-option
                    :label="item.name"
                    :value="item.value"
                    v-for="item in payPerceiveOptions"
                    :key="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="对科研认知" prop="scientificPerceive">
                <el-select
                  v-model="staffForm.scientificPerceive"
                  clearable
                  placeholder="请选择"
                  class="select-box"
                >
                  <el-option
                    :label="item.name"
                    :value="item.value"
                    v-for="item in scientificPerceiveOptions"
                    :key="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="在院话语权" prop="rightSpeak">
                <el-select
                  v-model="staffForm.rightSpeak"
                  clearable
                  placeholder="请选择"
                  class="select-box"
                >
                  <el-option
                    :label="item.name"
                    :value="item.value"
                    v-for="item in speakerRightsOptions"
                    :key="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="讲者分类" prop="speakerType">
                <el-select
                  v-model="staffForm.speakerType"
                  clearable
                  placeholder="请选择"
                  class="select-box"
                >
                  <el-option
                    :label="item.name"
                    :value="item.value"
                    v-for="item in speakerTypeOptions"
                    :key="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="group-head">
              <span></span>
              <div>个人信息</div>
            </div>
            <div class="sub-form">
              <el-form-item label="微信号" prop="wxNo">
                <el-input
                  v-model="staffForm.wxNo"
                  placeholder="请输入"
                  oninput="(value) = value.replace(/[\u4e00-\u9fa5\s]/g, '')"
                ></el-input>
              </el-form-item>
              <el-form-item label="联系电话" prop="phone">
                <el-input
                  v-model="staffForm.phone"
                  placeholder="请输入"
                  oninput="(value) = value.replace(/\D/g, '')"
                ></el-input>
              </el-form-item>
              <el-form-item label="家庭地址" prop="location">
                <el-input
                  v-model="staffForm.location"
                  placeholder="请输入"
                  :maxlength="100"
                ></el-input>
              </el-form-item>
              <el-form-item label="兴趣爱好" prop="hobby">
                <el-input
                  v-model="staffForm.hobby"
                  placeholder="请输入"
                  :maxlength="100"
                ></el-input>
              </el-form-item>
            </div>
            <div class="group-head">
              <span></span>
              <div>身份信息</div>
            </div>
            <div class="sub-form">
              <el-form-item label="身份证号" prop="idCard">
                <el-input
                  v-model="staffForm.idCard"
                  placeholder="请输入"
                  :maxlength="20"
                  oninput="(value) = value.replace(/\D/g, '')"
                ></el-input>
              </el-form-item>
              <el-form-item label="出生年月" prop="birthday">
                <el-date-picker
                  v-model="staffForm.birthday"
                  type="date"
                  placeholder="选择出生年月日"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item></el-form-item>
              <el-form-item></el-form-item>
            </div>
            <div class="group-head">
              <span></span>
              <div>银行账号</div>
            </div>
            <div class="sub-form">
              <el-form-item label="开户行" prop="openingBank">
                <el-input
                  v-model="staffForm.openingBank"
                  placeholder="请输入"
                  :maxlength="20"
                  oninput="(value) = value.replace(/\s+/g, '')"
                ></el-input>
              </el-form-item>
              <el-form-item label="银行卡号" prop="bankNo">
                <el-input
                  v-model="staffForm.bankNo"
                  placeholder="请输入"
                  :maxlength="20"
                  oninput="(value) = value.replace(/\D/g, '')"
                ></el-input>
              </el-form-item>
              <el-form-item></el-form-item>
              <el-form-item></el-form-item>
            </div>
            <div class="group-head">
              <span></span>
              <div>配偶信息</div>
            </div>
            <div class="sub-form">
              <el-form-item label="配偶姓名" prop="nameSpouse">
                <el-input
                  v-model="staffForm.nameSpouse"
                  placeholder="请输入"
                  :maxlength="10"
                  oninput="(value) = value.replace(/\s*/g,'')"
                ></el-input>
              </el-form-item>
              <el-form-item label="配偶年龄" prop="ageSpouse">
                <el-input
                  v-model="staffForm.ageSpouse"
                  placeholder="请输入"
                  :maxlength="2"
                  oninput="(value) = value.replace(/\D/g, '')"
                ></el-input>
              </el-form-item>
              <el-form-item label="配偶工作单位" prop="unitSpouse">
                <el-input
                  v-model="staffForm.unitSpouse"
                  placeholder="请输入"
                  :maxlength="100"
                  oninput="(value) = value.replace(/\s+/g, '')"
                ></el-input>
              </el-form-item>
              <el-form-item label="配偶职业" prop="jobSpouse">
                <el-input
                  v-model="staffForm.jobSpouse"
                  placeholder="请输入"
                  :maxlength="10"
                  oninput="(value) = value.replace(/\s+/g, '')"
                ></el-input>
              </el-form-item>
              <el-form-item label="配偶兴趣爱好" prop="hobbySpouse">
                <el-input
                  v-model="staffForm.hobbySpouse"
                  placeholder="请输入"
                  :maxlength="100"
                ></el-input>
              </el-form-item>
            </div>
            <div class="group-head">
              <span></span>
              <div>子女信息</div>
            </div>
            <div class="sub-form">
              <el-form-item label="子女姓名" prop="nameChildren">
                <el-input
                  v-model="staffForm.nameChildren"
                  placeholder="请输入"
                  :maxlength="10"
                  oninput="(value) = value.replace(/\s*/g,'')"
                ></el-input>
              </el-form-item>
              <el-form-item label="子女年龄" prop="ageChildren">
                <el-input
                  v-model="staffForm.ageChildren"
                  placeholder="请输入"
                  :maxlength="2"
                  oninput="(value) = value.replace(/\D/g, '')"
                ></el-input>
              </el-form-item>
              <el-form-item label="子女兴趣爱好" prop="hobbyChildren">
                <el-input
                  v-model="staffForm.hobbyChildren"
                  placeholder="请输入"
                  :maxlength="100"
                ></el-input>
              </el-form-item>
              <el-form-item label="就读学校/工作单位" prop="schoolChildren">
                <el-input
                  v-model="staffForm.schoolChildren"
                  placeholder="请输入"
                  oninput="(value) = value.replace(/\s+/g, '')"
                  :maxlength="100"
                ></el-input>
              </el-form-item>
            </div>
            <div class="group-head">
              <span></span>
              <div>账号信息</div>
            </div>
            <div class="">
              <AccountInfoCard
                :doctorInfo="{ ...staffForm, doctorId: doctorId }"
                :showEdit="true"
                :accountList="tableData"
                @edit="editAccount"
                @status="updateAccount"
              ></AccountInfoCard>
            </div>
          </div>
        </el-form>
      </div>
    </CommonDialog>
    <EditAccountDialog
      @confirm="confirmEditDialog"
      v-if="editDialogVisible"
      :visible.sync="editDialogVisible"
      :accountInfo="rowAccount"
      @confirmAccountInfo="getAccountInfo"
      title="编辑账号-哈瑞特工作室"
    >
    </EditAccountDialog>
  </div>
</template>

<script>
import CommonDialog from '../../../../../components/CommonDialog.vue'
import EditAccountDialog from './EditAccountDialog.vue'

import AccountInfoCard from '../../hospital-client/components/AccountInfoCard.vue'
import { timeMode } from '@/util/util'
import { httpReq } from '@/http'
import * as qiniu from 'qiniu-js'
import {
  genderOptions,
  jobTitleOption,
  educationOptions,
  isKeyOptions,
  pushTypeOptions,
  payPerceiveOptions,
  scientificPerceiveOptions,
  speakerRightsOptions,
  speakerTypeOptions,
} from '../constant/hospitalEnum'
import { companyStatusMap } from '../../../../company/constant/okrEnum'
import {
  getAllHospitalOptionsReq,
  getAllDepOptionsReq,
  getAllDepPositionsOptionsReq,
  createHospitalStaffReq,
  updateDoctorAccountStatusReq,
  updateHospitalStaffReq,
  getHospitalPersonDetailReq,
} from '../../../../../api/client'
import Debounce from '../../../../../util/util'
export default {
  name: 'AddHospitalStaff',
  components: { CommonDialog, EditAccountDialog, AccountInfoCard },
  props: {
    visible: {
      require: true,
      type: Boolean,
    },
    title: {
      require: true,
      type: String,
    },
    hospitalId: {
      require: false,
    },
    doctorId: {
      require: false,
    },
  },
  data() {
    return {
      genderOptions,
      jobTitleOption,
      educationOptions,
      isKeyOptions,
      pushTypeOptions,
      payPerceiveOptions,
      scientificPerceiveOptions,
      companyStatusMap,
      speakerRightsOptions,
      speakerTypeOptions,
      staffForm: {
        hospitalId: null,
        name: '',
        gender: '',
        jobTitle: '',
        school: '',
        education: '',
        academicPost: '',
        briefIntroduction: '',
        major: '',
        curriculum: [],
        isKey: '',
        pushType: '',
        payPerceive: '',
        scientificPerceive: '',
        rightSpeak: '',
        speakerType: '',
        wxNo: '',
        phone: '',
        location: '',
        hobby: '',
        idCard: '',
        birthday: '',
        openingBank: '',
        bankNo: '',
        nameSpouse: '',
        ageSpouse: '',
        unitSpouse: '',
        jobSpouse: '',
        hobbySpouse: '',
        nameChildren: '',
        ageChildren: '',
        hobbyChildren: '',
        schoolChildren: '',
        profilePhoto: '',
        account: {
          // profilePhoto:'',
          account: '',
          phone: '',
          type: '',
          isLogin: '',
          status: '',
        },
      },
      rules: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
        hospitalId: [{ required: true, message: '请选择医院', trigger: 'change' }],
        isKey: [{ required: true, message: '请选择关键决策人', trigger: 'change' }],
        //职务为科主任时是必填
        phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
        //职务为科主任时是必填
        rightSpeak: [{ required: true, message: '请选择话语权', trigger: 'change' }],
        //职务类型有行政架构为必填
        idCard: [{ required: false, message: '请输入身份证号', trigger: 'blur' }],
        //职务类型有行政架构为必填
        openingBank: [{ required: false, message: '请输入开户行', trigger: 'blur' }],
        //职务类型有行政架构为必填
        bankNo: [{ required: false, message: '请输入银行账号', trigger: 'blur' }],
        //职务类型有行政架构为必填
        birthday: [{ required: false, message: '请输选择出生日期', trigger: 'change' }],
        //职务类型有行政架构为必填
        briefIntroduction: [{ required: false, message: '请输入简介', trigger: 'blur' }],
        profilePhoto: [{ required: true, message: '请上传头像', trigger: 'blur' }],
      },
      positionForm: [
        {
          deptId: '',
          deptType: '',
          position: {
            positionId: '',
            positionType: '',
          },
        },
      ],
      depOptions: [],
      positionsOptions: [],
      positionDepTypeMap: {},
      positionRules: {
        deptName: [{ required: true, message: '请选择职位', trigger: 'change' }],
        position: [{ required: true, message: '请选择部门', trigger: 'change' }],
      },
      qiNiuToken: '',
      acceptFileTypeArr: ['ppt', 'pptx', 'docx', 'doc', 'pdf'],
      acceptImageTypeArr: ['png', 'jpeg', 'jpg'],
      photoAcceptImageTypeArr: ['image/png', 'image/jpeg', 'image/jpg'],
      tableData: [
        {
          // profilePhoto:'',
          account: '',
          phone: '',
          type: '',
          isLogin: '',
          status: '',
          client: '哈瑞特工作室',
          systemType: 'MP',
          accountType: 'EXPERT',
          accountPermission: '默认权限',
        },
        {
          account: '',
          phone: '',
          type: '',
          isLogin: '',
          status: '',
          client: '科研数据统计平台',
          systemType: 'WEB',
          accountType: 'PI',
          accountPermission: '默认权限',
        },
      ],
      rowAccount: null,
      currentPage: 1,
      pageSize: 10,
      total: 1,
      editDialogVisible: false,
      positionIds: [],
      hospitalOptions: [],
    }
  },
  computed: {
    imgReviewList() {
      return this.staffForm.curriculum
        .filter((item) => this.acceptImageTypeArr.includes(item.fileType))
        .map((item) => item.url)
    },
    formatAcceptFileTypeArr() {
      return this.acceptFileTypeArr.map((item) => '.' + item)
    },
    formatAcceptImageTypeArr() {
      return this.acceptImageTypeArr.map((item) => '.' + item)
    },
  },
  watch: {
    positionForm: {
      handler: function (val) {
        //生成职务集合,修改必填非必填项
        this.changeRequiredField(val)
      },
      deep: true,
    },
    'staffForm.hospitalId': {
      handler: function (val) {
        if (val) {
          this.getDepOptionsAndPosition(val)
        }
      },
    },
  },
  created() {
    this.de = new Debounce(300)
    this.getUploadToken()
    this.initFormFnc()
  },
  methods: {
    openDialog() {},
    cancel() {
      this.$emit('update:visible', false)
      this.$emit('cancel')
    },
    confirm() {
      this.$refs['staffFormRef'].validate((valid) => {
        if (valid) {
          let finish = this.positionForm.every((item) => item.deptId && item.position.positionId)
          if (finish) {
            this.updateDoctor()
          } else {
            this.$message.warning(`请填写完整职务信息！`)
          }
        } else {
          this.$message.warning('请填写完整!')
        }
      })
    },
    changeHospitalId() {
      this.positionForm = [
        {
          deptId: '',
          deptType: '',
          position: {
            positionId: '',
            positionType: '',
          },
        },
      ]
    },
    photo_uploadPic() {
      console.log(this.$refs.evfile)
      this.$refs.evfileAccount.click()
    },
    photo_zh_uploadFile_change(evfile) {
      console.log('$debug: this.photoAcceptImageTypeArr', this.photoAcceptImageTypeArr)
      console.log('$debug: evfile', evfile.target.files[0].type)
      if (this.photoAcceptImageTypeArr.includes(evfile.target.files[0].type)) {
        var loading
        var uptoken = this.qiNiuToken
        var file = evfile.target.files[0] //Blob 对象，上传的文件
        var key = timeMode(new Date().getTime()).dateMin + file.name // 上传后文件资源名以设置的 key 为主，如果 key 为 null 或者 undefined，则文件资源名会以 hash 值作为资源名。
        let config = {
          useCdnDomain: true, //表示是否使用 cdn 加速域名，为布尔值，true 表示使用，默认为 false。
          region: qiniu.region.z2, // 根据具体提示修改上传地区,当为 null 或 undefined 时，自动分析上传域名区域
        }
        let putExtra = {
          fname: '', //文件原文件名
          params: {}, //用来放置自定义变量
          mimeType: null, //用来限制上传文件类型，为 null 时表示不对文件类型限制；限制类型放到数组里： ["image/png", "image/jpeg", "image/gif"]
        }
        var observable = qiniu.upload(file, key, uptoken, putExtra, config)
        observable.subscribe({
          next: (result) => {
            // 主要用来展示进度
            // console.log(result)
            loading = this.$loading({
              lock: true,
              text: '上传中',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)',
            })
          },
          error: (errResult) => {
            // 失败报错信息
            loading.close()
            this.$message.error('上传头像图片失败，请重新上传')
            console.log(errResult)
          },
          complete: (result) => {
            // 接收成功后返回的信息
            loading.close()
            this.staffForm.profilePhoto = 'http://image.scheartmed.com/' + result.key
          },
        })
      } else {
        this.$message.error('仅支持JPG、JPEG等格式')
      }
      evfile.target.value = ''
    },
    zh_uploadFile_change(evfile) {
      var file = evfile.target.files[0] //Blob 对象，上传的文件
      const fileType = file.name.slice(file.name.lastIndexOf('.') + 1, file.name.length)
      if (this.acceptFileTypeArr.includes(fileType) || this.acceptImageTypeArr.includes(fileType)) {
        var loading
        var uptoken = this.qiNiuToken
        var key = new Date().getTime() + file.name // 上传后文件资源名以设置的 key 为主，如果 key 为 null 或者 undefined，则文件资源名会以 hash 值作为资源名。
        let config = {
          useCdnDomain: true, //表示是否使用 cdn 加速域名，为布尔值，true 表示使用，默认为 false。
          region: qiniu.region.z2, // 根据具体提示修改上传地区,当为 null 或 undefined 时，自动分析上传域名区域
        }
        let putExtra = {
          fname: '', //文件原文件名
          params: {}, //用来放置自定义变量
          mimeType: null, //用来限制上传文件类型，为 null 时表示不对文件类型限制；限制类型放到数组里： ["image/png", "image/jpeg", "image/gif"]
        }
        var observable = qiniu.upload(file, key, uptoken, putExtra, config)
        observable.subscribe({
          next: (result) => {
            // 主要用来展示进度
            loading = this.$loading({
              lock: true,
              text: '上传中',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)',
            })
          },
          error: (errResult) => {
            // 失败报错信息
            loading.close()
            this.$message.error('文件上传失败，请重新上传')
          },
          complete: (result) => {
            // 接收成功后返回的信息
            loading.close()
            let fileInfo = {
              fileType: fileType,
              fileName: result.key,
              url: 'http://image.scheartmed.com/' + result.key,
            }
            this.staffForm.curriculum.push(fileInfo)
          },
        })
      } else {
        this.$message.error('仅支持doc、docx、ppt、pptx、jpeg、jpg、png格式!')
      }
      evfile.target.value = ''
    },
    urlDownload(url) {
      let eleLink = document.createElement('a')
      eleLink.style.display = 'none'
      eleLink.href = url
      document.body.appendChild(eleLink)
      eleLink.click()
      document.body.removeChild(eleLink)
    },
    //获取七牛云凭证
    getUploadToken() {
      httpReq({
        url: '/admins/getUploadToken',
      }).then((res) => {
        this.qiNiuToken = res.data
      })
    },
    uploadPic() {
      this.$refs.evfileHosStaff.click()
    },
    deleteResume(index) {
      this.staffForm.curriculum.splice(index, 1)
    },
    confirmEditDialog() {},
    addPosition() {
      let finish = this.positionForm.every((item) => item.deptId && item.position.positionId)
      if (finish) {
        let obj = {
          deptId: '',
          deptType: '',
          position: {
            positionId: '',
            positionType: '',
          },
        }
        this.positionForm.push(obj)
      } else {
        this.$message.warning(`请填写完整！`)
      }
    },
    deletePosition(index) {
      this.positionForm.splice(index, 1)
    },
    editAccount(row) {
      this.rowAccount = row
      if (row.accountType === 'EXPERT') {
        this.editDialogVisible = true
      } else if (row.accountType === 'PI') {
        this.editDialogVisible = true
      }
    },
    async updateAccount(row) {
      console.log(`output->row`, row)
      // 这里需要区分以前账号还是新增的pi账号
      if (row.accountType === 'EXPERT') {
        const status = row.status === 'ENABLE' ? 'DISABLE' : 'ENABLE'
        status === 'DISABLE' &&
          this.$confirm('<i class="el-icon-warning"></i>是否确认停用当前人员的账号?', {
            title: '账号停用后将无法继续使用对应系统',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            dangerouslyUseHTMLString: true,
            customClass: 'messageTips',
          })
            .then(() => {
              updateDoctorAccountStatusReq({
                doctorId: this.doctorId,
                status,
              })
                .then(() => {
                  this.$message.success(`账号禁用成功!`)
                  if (this.doctorId) {
                    this.getPersonDetail()
                  }
                })
                .catch((err) => {
                  this.$message.error(`账号禁用失败${err.msg}`)
                })
            })
            .catch(() => {})
        status === 'ENABLE' &&
          updateDoctorAccountStatusReq({
            doctorId: this.doctorId,
            status,
          })
            .then(() => {
              this.$message.success(`账号启用成功!`)
              if (this.doctorId) {
                this.getPersonDetail()
              }
            })
            .catch((err) => {
              this.$message.error(`账号启用失败${err.msg}`)
            })
      } else if (row.accountType === 'PI') {
        row.status === 'ENABLE' &&
          this.$confirm('<i class="el-icon-warning"></i>是否确认停用当前人员的账号?', {
            title: '账号停用后将无法继续使用对应系统',
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            dangerouslyUseHTMLString: true,
            customClass: 'messageTips',
          }).then(() => {
            httpReq({
              url: '/api/os/user/pi/status/modify',
              method: 'POST',
              data: {
                id: row.id,
                status: row.status === 'ENABLE' ? 0 : 1,
              },
            })
              .then((res) => {
                if (res.code === 1) {
                  this.$message.success(row.status === 'ENABLE' ? '账号禁用成功!' : '账号启用成功!')
                  if (this.doctorId) {
                    this.getPersonDetail()
                  }
                }
              })
              .catch((err) => {
                console.log(`output->err`, err)
              })
          })
        row.status === 'DISABLE' &&
          httpReq({
            url: '/api/os/user/pi/status/modify',
            method: 'POST',
            data: {
              id: row.id,
              status: row.status === 'ENABLE' ? 0 : 1,
            },
          })
            .then((res) => {
              if (res.code === 1) {
                this.$message.success(row.status === 'ENABLE' ? '账号禁用成功!' : '账号启用成功!')
                if (this.doctorId) {
                  this.getPersonDetail()
                }
              }
            })
            .catch((err) => {
              console.log(`output->err`, err)
            })
      }
    },
    groupByDeptType(data) {
      return data.reduce((result, item) => {
        // 如果 deptType 不在结果对象中，则创建一个空数组
        if (!result[item.deptType]) {
          result[item.deptType] = []
        }
        // 将当前项添加到对应 deptType 的数组中
        result[item.deptType].push(item)
        return result
      }, {})
    },
    //选择职务信息部门
    handleChangeDep(deptId, index) {
      let currentDep = this.depOptions.find((item) => item.deptId === deptId)
      this.positionForm[index].deptType = currentDep.deptType
      this.positionForm[index].position.positionId = ''
      this.positionForm[index].position.positionType = ''
    },
    //选择职务下职务信息
    handleChangePosition(positionId, index, deptType) {
      let currentPosition = this.positionDepTypeMap[deptType].find(
        (item) => item.positionId === positionId,
      )
      this.positionForm[index].position.positionType = currentPosition.positionType
    },
    //根据条件修改必填非必填项,
    changeRequiredField(val) {
      this.positionIds = val.map((item) => item.position.positionId)
      //如果所选职位有科室主任  20 科室主任
      this.rules.rightSpeak[0].required = this.positionIds.includes(20)
      this.rules.phone[0].required = this.positionIds.includes(20)
      let positionType = val.map((item) => item.position.positionType)
      //如果职务有行政架构职务 行政架构职务类型 ADMINISTRATIVE
      if (!this.doctorId) {
        this.staffForm.isKey = positionType.includes('ADMINISTRATIVE') ? 'IS_KEY_YES' : 'IS_KEY_NO'
      }
      this.rules.idCard[0].required = positionType.includes('ADMINISTRATIVE')
      this.rules.openingBank[0].required = positionType.includes('ADMINISTRATIVE')
      this.rules.bankNo[0].required = positionType.includes('ADMINISTRATIVE')
      this.rules.birthday[0].required = positionType.includes('ADMINISTRATIVE')
      this.rules.briefIntroduction[0].required = positionType.includes('ADMINISTRATIVE')
    },
    //编辑医生账号
    getAccountInfo(accountInfo) {
      const { profilePhoto, account, phone, type, isLogin, password } = accountInfo
      console.log(`output->accountInfo`, accountInfo)
      // this.staffForm.account = accountInfo;
      const accIndex = this.tableData.findIndex(
        (it) => it.accountType === this.rowAccount.accountType,
      )
      if (accIndex === 0) {
        this.staffForm.account = accountInfo
        this.staffForm.account.profilePhoto = this.staffForm.profilePhoto
      }
      console.log(`output->accIndex`, accIndex)
      this.tableData[accIndex].account = account
      this.tableData[accIndex].phone = phone
      this.tableData[accIndex].type = type
      this.tableData[accIndex].isLogin = isLogin
      this.tableData[accIndex].password = password
      this.tableData[accIndex].profilePhoto = profilePhoto
    },
    //获取所有可选医院
    getAllHospitalOptions() {
      return new Promise((resolve) => {
        getAllHospitalOptionsReq().then((res) => {
          this.hospitalOptions = res.data
          resolve('success')
        })
      })
    },
    //查询医院下所有部门以及部门可选职位 初始化职务相关数据
    getDepOptionsAndPosition(hospitalId) {
      return new Promise(async (resolve, reject) => {
        await this.getAllDepOptions(hospitalId)
        await this.getAllDepPositionsOptions(hospitalId)
        this.positionDepTypeMap = this.groupByDeptType(this.positionsOptions)
        resolve('success')
      })
    },
    //查询医院下所有部门
    getAllDepOptions(hospitalId) {
      return getAllDepOptionsReq({ hospitalId }).then((res) => {
        this.depOptions = res.data
      })
    },
    //查询医院下所有职位
    getAllDepPositionsOptions(hospitalId) {
      return getAllDepPositionsOptionsReq({ hospitalId }).then((res) => {
        this.positionsOptions = res.data
      })
    },
    //更新医院人员
    async updateDoctor() {
      const _this = this
      await this.de.debounceEnd()
      const loading = this.$loading({
        lock: true,
        text: '请求中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      const { curriculum, pushType, ...resParams } = this.staffForm
      let params = {
        ...resParams,
        pushType: [pushType],
        curriculum: this.staffForm.curriculum.map((item) => item.url),
        dept: this.getPositionParams(this.positionForm),
      }
      if (this.doctorId) {
        params['doctorId'] = this.doctorId
        updateHospitalStaffReq(params)
          .then(() => {
            return updatePiAccount()
          })
          .then((res) => {
            this.$message.success(`医院人员编辑成功!`)
            loading.close()
            this.$emit('confirmSuccess')
            this.$emit('update:visible', false)
          })
          .catch((err) => {
            this.$message.error(`医院人员编辑失败：${err.msg}`)
            console.log(err)
            loading.close()
          })
      } else {
        createHospitalStaffReq(params)
          .then(() => {
            this.$message.success(`医院人员新增成功!`)

            return updatePiAccount()
          })
          .then((res) => {
            loading.close()
            this.$emit('confirmSuccess')
            this.$emit('update:visible', false)
          })
          .catch((err) => {
            this.$message.error(`医院人员新增失败：${err.msg}`)
            loading.close()
          })
      }
      function updatePiAccount() {
        const piAccount = _this.tableData.find((it) => it.accountType === 'PI')
        console.log(`output->JSON.stringify(_this.rowAccount)`, JSON.stringify(_this.rowAccount))
        console.log(`output->JSON.stringify(piAccount)`, JSON.stringify(piAccount))
        if (
          !piAccount.account ||
          !_this.rowAccount ||
          _this.rowAccount.accountType !== 'PI' ||
          JSON.stringify(_this.rowAccount) === JSON.stringify(piAccount)
        )
          return
        const {
          name = _this.staffForm.name,
          userId = _this.doctorId,
          account,
          phone,
          password,
        } = piAccount
        return httpReq({
          url: '/api/os/user/pi/account/modify',
          method: 'POST',
          data: {
            name,
            account,
            phone,
            password,
            userId,
          },
        }).then((res) => {
          console.log(`output->res`, res)
        })
      }
    },
    getPositionParams(arr) {
      // 使用 reduce 来分组并合并 position
      return arr.reduce((acc, item) => {
        // 查找是否已经存在该 deptType
        const existingDept = acc.find((dept) => dept.deptType === item.deptType)

        if (existingDept) {
          // 如果存在该 deptType，则将新的 position 添加到对应的 position 数组中
          existingDept.position.push(item.position)
        } else {
          // 如果不存在该 deptType，创建新的对象并加入 position 数组
          acc.push({
            deptId: item.deptId,
            deptType: item.deptType,
            position: [item.position],
          })
        }
        return acc
      }, [])
    },
    customRadioPushTypeClickHandler(item) {
      this.staffForm.pushType = item.value === this.staffForm.pushType ? '' : item.value
    },
    getPersonDetail() {
      getHospitalPersonDetailReq({ doctorId: this.doctorId }).then((res) => {
        const {
          hospitalId,
          name,
          gender,
          jobTitle,
          school,
          education,
          academicPost,
          briefIntroduction,
          major,
          isKey,
          pushType,
          payPerceive,
          scientificPerceive,
          rightSpeak,
          speakerType,
          wxNo,
          phone,
          location,
          hobby,
          idCard,
          birthday,
          openingBank,
          bankNo,
          nameSpouse,
          ageSpouse,
          unitSpouse,
          jobSpouse,
          hobbySpouse,
          nameChildren,
          ageChildren,
          hobbyChildren,
          schoolChildren,
          account,
          dept,
          curriculum,
          profilePhoto,
        } = res.data
        this.staffForm.hospitalId = hospitalId
        this.staffForm.name = name
        this.staffForm.gender = gender
        this.staffForm.jobTitle = jobTitle
        this.staffForm.school = school
        this.staffForm.education = education
        this.staffForm.academicPost = academicPost
        this.staffForm.briefIntroduction = briefIntroduction
        this.staffForm.major = major
        this.staffForm.isKey = isKey
        this.staffForm.pushType = pushType[0]
        this.staffForm.payPerceive = payPerceive
        this.staffForm.scientificPerceive = scientificPerceive
        this.staffForm.rightSpeak = rightSpeak
        this.staffForm.speakerType = speakerType
        this.staffForm.wxNo = wxNo
        this.staffForm.phone = phone
        this.staffForm.location = location
        this.staffForm.hobby = hobby
        this.staffForm.idCard = idCard
        this.staffForm.birthday = birthday
        this.staffForm.openingBank = openingBank
        this.staffForm.bankNo = bankNo
        this.staffForm.nameSpouse = nameSpouse
        this.staffForm.ageSpouse = ageSpouse
        this.staffForm.unitSpouse = unitSpouse
        this.staffForm.jobSpouse = jobSpouse
        this.staffForm.hobbySpouse = hobbySpouse
        this.staffForm.nameChildren = nameChildren
        this.staffForm.ageChildren = ageChildren
        this.staffForm.hobbyChildren = hobbyChildren
        this.staffForm.schoolChildren = schoolChildren
        this.staffForm.profilePhoto = profilePhoto
        //处理账号信息回显
        if (account) {
          this.tableData[0].account = account.account
          this.tableData[0].phone = account.phone
          this.tableData[0].type = account.type
          this.tableData[0].isLogin = account.isLogin
          // this.tableData[0].profilePhoto = account.profilePhoto
          this.tableData[0].status = account.status
          this.staffForm.account = account
        }
        //处理简历回显
        if (Array.isArray(curriculum)) {
          let res = curriculum.map((item) => {
            return {
              fileType: item.slice(item.lastIndexOf('.') + 1, item.length),
              fileName: item.replace('http://image.scheartmed.com/', ''),
              url: item,
            }
          })
          this.staffForm.curriculum = res
        }
        //处理职务信息回显
        if (Array.isArray(dept)) {
          if (dept.length) {
            let res = []
            dept.forEach((dept) => {
              if (Array.isArray(dept.position)) {
                dept.position.forEach((position) => {
                  res.push({
                    deptId: dept.deptId,
                    deptType: dept.deptType,
                    position: {
                      positionId: position.positionId,
                      positionType: position.positionType,
                    },
                  })
                })
              }
            })
            this.positionForm = res
          } else {
            let obj = {
              deptId: '',
              deptType: '',
              position: {
                positionId: '',
                positionType: '',
              },
            }
            this.positionForm.push(obj)
          }
        }
      })
    },
    async initFormFnc() {
      if (this.hospitalId) {
        await this.getDepOptionsAndPosition(this.hospitalId)
        this.staffForm.hospitalId = this.hospitalId
      } else {
        await this.getAllHospitalOptions()
      }
      if (this.doctorId) {
        this.getPersonDetail()
      }
    },
  },
}
</script>

<style scoped lang="less">
.content-wrapper {
  width: 100%;
  box-sizing: border-box;
  padding: 24px;
  text-align: left;
  color: #15233f;
  overflow-y: auto;
}
/deep/.el-form.rule-from-style {
  .form-group {
    .group-head {
      font-weight: bold;
      font-size: 16px;
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      span {
        display: block;
        width: 4px;
        height: 16px;
        background: #2e6be6;
        border-radius: 1px;
        margin-right: 8px;
      }
    }
    .sub-form {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 24px;
      .el-form-item {
        flex: 1;
        margin-bottom: 8px;
        .el-form-item__label {
          color: #3a4762;
          padding: 0;
          line-height: 32px;
          font-weight: bold;
        }
        .el-form-item__content {
          width: 240px;
          line-height: 32px;
          border-radius: 4px;
          .el-input__inner {
            width: 240px;
            height: 32px;
            line-height: 32px;
            font-size: 14px;
            border-radius: 2px;
            &::placeholder {
              color: #909399;
            }
          }
          .large-size-input {
            .el-input__inner {
              width: 480px;
              height: 32px;
              line-height: 32px;
              font-size: 14px;
              border-radius: 2px;
              &::placeholder {
                color: #909399;
              }
            }
          }
          .select-box .el-icon-arrow-up:before {
            content: '';
          }
          .select-box .el-icon-arrow-down:before {
            content: '\e790';
          }
          .el-select .el-input .el-input__icon {
            line-height: 32px;
          }
          .el-radio-group {
            .el-radio__input.is-checked .el-radio__inner {
              border-color: #2e6be6;
              background: #2e6be6;
            }
            .el-radio__input.is-checked + .el-radio__label {
              color: #2e6be6;
            }
            .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
              box-shadow: none !important;
            }
          }
          .el-checkbox-group {
            width: 240px;
            display: flex;
            flex-wrap: wrap;
            .el-checkbox {
              flex: 1;
              margin-right: 0;
            }
          }
          .el-input__icon {
            line-height: 32px;
          }
          .el-checkbox__input.is-checked .el-checkbox__inner,
          .el-checkbox__input.is-indeterminate .el-checkbox__inner {
            background-color: #2e6be6;
            border-color: #2e6be6;
          }
          .el-checkbox .is-checked {
            .el-checkbox__inner {
              background-color: #2e6be6;
              border-color: #2e6be6;
            }
          }
          .el-checkbox__input.is-checked + .el-checkbox__label {
            color: #2e6be6;
          }
        }
      }
      .large-form-item {
        .el-form-item__content {
          width: 480px;
        }
      }
      .text-btn {
        color: #969799;
      }
      .custom-form {
        .custom-form-item {
          display: flex;
          margin-bottom: 16px;
          .custom-item {
            margin-right: 40px;
            .label {
              color: #3a4762;
              font-weight: bold;
              margin-bottom: 8px;
              span {
                color: #f56c6c;
              }
            }
            .delete-btn {
              margin-left: 8px;
              .el-button {
                color: #e63746;
              }
            }
            .el-input__inner {
              width: 240px;
              height: 32px;
              line-height: 32px;
              font-size: 14px;
              border-radius: 2px;
              &::placeholder {
                color: #909399;
              }
            }
            .select-box .el-icon-arrow-up:before {
              content: '';
            }
            .select-box .el-icon-arrow-down:before {
              content: '\e790';
            }
            .el-select .el-input .el-input__icon {
              line-height: 32px;
            }
          }
        }
      }
    }
    .add-btn {
      font-size: 14px;
      display: flex;
      color: #2e6be6;
      cursor: pointer;
      font-weight: normal;
      align-items: center;
      margin-right: 8px;
    }
  }
}
.logo-box {
  text-align: left;
}
.block {
  display: flex;
  flex-wrap: wrap;
  width: 500px;
  .el-avatar {
    display: block;
    background-color: #ffffff;
    border: 1px dashed #dcdfe6;
    font-size: 30px;
    color: #d9d9d9;
  }
  .img-box {
    margin-right: 8px;
    width: 100px;
    height: 100px;
    position: relative;
    .el-image {
      border-radius: 4px;
    }
    .el-file {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      border-radius: 4px;
      border: 1px solid #c0ccda;
      font-size: 24px;
      font-weight: 800;
      color: #606266;
      text-align: center;
      line-height: normal;
      div {
        word-wrap: break-word;
        white-space: normal;
        word-break: break-all;
      }
    }
    .el-icon-error {
      color: red;
      font-size: 18px;
      position: absolute;
      right: -6px;
      top: -6px;
      cursor: pointer;
      background-color: #ffffff;
      border-radius: 50%;
    }
  }
  .upload-btn {
    bottom: 30px;
    left: 24px;
    position: absolute;
    color: #d9d9d9;
    height: calc(100% - 30px);
    display: flex;
    align-items: flex-end;
  }
  .delete-mask {
    top: 0;
    left: 0;
    width: 100px;
    height: 100px;
    position: absolute;
    color: #ffffff;
    font-size: 24px;
    background-color: #333333;
    text-align: center;
    line-height: 100px;
    opacity: 0;
    cursor: pointer;
  }
  .delete-mask:hover {
    transition: all 0.3s;
    opacity: 0.3;
  }
}
.el-pagination {
  margin-top: 16px;
  text-align: center;
}
.warning-btn {
  font-size: 14px;
  color: #e63746;
}
.primary-btn {
  font-size: 14px;
  color: #2e6be6;
}
.normal-icon {
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #2fb324;
  border-radius: 50%;
}
.danger-icon {
  display: inline-block;
  width: 8px;
  height: 8px;
  background: #e63746;
  border-radius: 50%;
}
</style>
