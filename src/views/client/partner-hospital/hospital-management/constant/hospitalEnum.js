import {getMapData} from "../../../../../util/util";
//医院类型
export const hosTypes = [
  { name: "总部",id:'TOTAL_HOSPITAL' }, //1
  { name: "分部",id:'BRANCH_HOSPITAL' }, //2
]
//性别Options
export const genderOptions = [
  {
    name:'男',
    value:'MALE'
  },
  {
    name:'女',
    value:'FEMALE'
  },
]
export const hosTypesMap = {
  TOTAL_HOSPITAL:'总部',
  BRANCH_HOSPITAL:'分部'
}

//医院等级
export const hosLevelList = [
  { name: "甲级",id:'LEVEL_A' },
  { name: "乙级",id:'LEVEL_B' },
  { name: "丙级",id:'LEVEL_C' },
]

export const hosLevelMap = {
  LEVEL_A:'甲级',
  LEVEL_B:'乙级',
  LEVEL_C:'丙级',
}

//医院开发状态
export const developStatusOptions = [
  { name: "待开发",value:'DEVELOP_PENDING'},
  { name: "访前准备",value:'DEVELOP_PREPARATION'},
  { name: "正式拜访",value:'DEVELOP_VISIT'},
  { name: "部分交接",value:'DEVELOP_PART_HANDOVER'},
  { name: "交接销售",value:'DEVELOP_SELLER'},
  { name: "开发完成",value:'DEVELOP_COMPLETE'},
  { name: "暂停开发",value:'DEVELOP_PAUSE'},
  { name: "市场暂停",value:'DEVELOP_MARKET_PAUSE'},
]

export const developStatusMap = {
  DEVELOP_PENDING:'待开发',
  DEVELOP_PREPARATION:'访前准备',
  DEVELOP_VISIT:'正式拜访',
  DEVELOP_PART_HANDOVER:'部分交接',
  DEVELOP_SELLER:'交接销售',
  DEVELOP_COMPLETE:'开发完成',
  DEVELOP_PAUSE:'暂停开发',
  DEVELOP_MARKET_PAUSE:'市场暂停',
}

export const depOptions = [
  { name: "院办",value:'HOSPITAL_OFFICE'},
  { name: "医务科",value:'MEDICAL_SECTION'},
  { name: "护理部",value:'NURSING_SECTION'},
  { name: "信息科",value:'INFORMATION_SECTION'},
  { name: "临床科室",value:'CLINICAL_SECTION'},
  { name: "病区",value:'HOSPITAL_WARD'},
  { name: "工作小组",value:'WORK_GROUP'},
]

export const depTypeMap = getMapData(depOptions)


//职务类型
export const positionTypeOption = [
  { name: "临床架构",value:'CLINICAL'},
  { name: "行政架构",value:'ADMINISTRATIVE'},
  { name: "其他",value:'OTHER'},
]
export const positionTypeMap = getMapData(positionTypeOption)
//职称选项
export const jobTitleOption = [
  { name: "主任医师",value:'CHIEF_PHYSICIAN'},
  { name: "副主任医师",value:'ASSOCIATE_CHIEF_PHYSICIAN'},
  { name: "主治医师",value:'ATTENDING'},
  { name: "住院医师",value:'RESIDENT_DOCTOR'},
  { name: "主任护士",value:'CHIEF_NURSE'},
  { name: "副主任护士",value:'DEPUTY_CHIEF_NURSE'},
  { name: "主管护士",value:'REGULAR_NURSE'},
  { name: "护师",value:'SENIOR_NURSE'},
  { name: "护士",value:'NURSE'},
]
export const jobTitleMap = getMapData(jobTitleOption)
//职务选项
export const positionOptions = [
  { name: "书记",value:'SECRETARY'},
  { name: "副书记",value:'SECRETARY_SECOND'},
  { name: "院长",value:'ATTENDING'},
  { name: "心血管方向副院长",value:'CARDIOVASCULAR_DIRECTOR_DEAN'},
  { name: "副院长",value:'DIRECTOR_DEAN'},
  { name: "院长助理",value:'DEAN_ASSISTANT'},
  { name: "医务处处长",value:'CHIEF_MEDICAL_SERVICE'},
  { name: "医务处副处长",value:'DIRECTOR_MEDICAL_SERVICE'},
]
//学历
export const educationOptions = [
  { name: "导师",value:'MENTOR'},
  { name: "博士",value:'DOCTOR'},
  { name: "硕士",value:'MASTER'},
  { name: "本科",value:'BACHELOR'},
  { name: "专科",value:'COLLEGE'},
  { name: "其他",value:'OTHER'},
]
export const educationMap = getMapData(educationOptions)
//关键决策人
export const isKeyOptions = [
  { name: "是",value:'IS_KEY_YES'},
  { name: "否",value:'IS_KEY_NO'},
]
export const isKeyMap = getMapData(isKeyOptions)
//推手类型
export const pushTypeOptions = [
  { name: "行政推手",value:'ADMINISTRATIVE_PUSHER'},
  { name: "临床推手",value:'CLINICAL_PUSHER'},
]
export  const pushTypeMap = getMapData(pushTypeOptions)
//付费认知类型
export const payPerceiveOptions = [
  { name: "拒绝",value:'REFUSE'},
  { name: "暂停",value:'PAUSE'},
  { name: "不了解",value:'UNDERSTAND'},
  { name: "了解",value:'KNOW'},
  { name: "试用",value:'TRY'},
  { name: "使用",value:'USE'},
  { name: "推荐",value:'RECOMMEND'},
  { name: "倡导",value:'ADVOCATE'},
]
//科研认知类型
export const scientificPerceiveOptions = [
  { name: "拒绝",value:'REFUSE'},
  { name: "暂停",value:'PAUSE'},
  { name: "不了解",value:'UNDERSTAND'},
  { name: "了解",value:'KNOW'},
  { name: "试用",value:'TRY'},
  { name: "使用",value:'USE'},
  { name: "推荐",value:'RECOMMEND'},
  { name: "倡导",value:'ADVOCATE'},
]
export const scientificPerceiveMap = getMapData(scientificPerceiveOptions)



//讲者分类类型
export const speakerTypeOptions = [
  { name: "全国级讲者",value:'NATIONAL_LEVEL'},
  { name: "区域级讲者",value:'REGIONAL_LEVEL'},
  { name: "城市级讲者",value:'CITY_LEVEL'},
  { name: "科会级讲者",value:'KON_LEVEL'},
  { name: "行政级讲者",value:'ADMINISTRATIVE_LEVEL'},
  { name: "其他类讲者",value:'OTHER_LEVEL'},
]

export const speakerTypeMap = getMapData(speakerTypeOptions)
//话语权分类
export const speakerRightsOptions = [
  { name: "非常有话语权",value:'HAS_VERY_RIGHT'},
  { name: "有一定话语权",value:'HAS_SOME_RIGHT'},
  { name: "无话语权",value:'NO_RIGHT'},
]
export const speakerRightsMap = getMapData(speakerRightsOptions)

//工作室选项
export const groupTypeOptions = [
  { name: "普通工作室",value:'NORMAL'},
  { name: "科研工作室",value:'RESEARCH'},
]
export const groupTypeMap = getMapData(groupTypeOptions)

//院内角色
export const companyRoleOptions = [
  { name: "医生",value:'ASSISTANT'},
  { name: "健康管理师",value:'CUSTOMER_SERVER'},
  { name: "运动康复师",value:'REHAB'},
  { name: "健康顾问",value:'SELLER'},
]
export const companyRoleMap = getMapData(companyRoleOptions)




