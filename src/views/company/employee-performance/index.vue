<script>
import { fetchPerformanceList } from '@/api'
import ExcelUpload from '@/components/ExcelUpload.vue'
import { IMPORT_TYPE } from '@/constant'
import { formatDateRange } from '@/util/util'

export default {
  name: 'CompanyEmployeePerformance',
  components: {
    ExcelUpload,
  },
  data() {
    return {
      searchFrom: {
        date: [],
      },
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      pageSizes: [10, 20, 50],
      importType: IMPORT_TYPE,
    }
  },
  methods: {
    async getList(page, pageSize, info) {
      const res = await fetchPerformanceList({
        pageNumber: page,
        pageSize,
        ...formatDateRange(info.date),
      })
      if (res.code === 1) {
        this.tableData = res.data.contents
        this.total = res.data.total
      }
    },
    onSearch() {
      this.currentPage = 1
      this.getList(1, this.pageSize, this.searchFrom)
    },
    resetForm() {
      this.searchFrom = {
        date: [],
      }
      this.currentPage = 1
      this.getList(this.currentPage, this.pageSize, this.searchFrom)
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getList(this.currentPage, val, this.searchFrom)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getList(val, this.pageSize, this.searchFrom)
    },
    monthFormat(month) {
      if (month) {
        const monthStr = `${month}`
        const year = `${monthStr}`.substring(0, 4)
        return `${year}年${monthStr.substring(4, 6)}月`
      } else {
        return '--'
      }
    },
    handleUploadSuccess() {
      this.getList(this.currentPage, this.pageSize, this.searchFrom)
    },
    handleUploadError() {},
  },
  created() {
    this.getList(this.currentPage, this.pageSize, this.searchFrom)
  },
}
</script>

<template>
  <div class="page-wrapper flex flex-col">
    <div class="search-box box">
      <el-form ref="searchFrom" size="mini" :inline="true" :model="searchFrom" label-width="90px">
        <div class="flex gap-4">
          <el-form-item label="绩效所属月">
            <el-date-picker
              v-model="searchFrom.date"
              type="monthrange"
              size="small"
              range-separator="——"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="datepicker"
              value-format="yyyy-MM"
              placeholder="选择月"
            >
            </el-date-picker>
          </el-form-item>
          <div class="btn">
            <el-button type="primary" @click="onSearch" class="search-btn" size="small"
              >查询
            </el-button>
            <el-button size="small" @click="resetForm" class="search-btn">重置 </el-button>
          </div>
        </div>
      </el-form>
    </div>
    <div class="add-wrapper">
      <ExcelUpload
        :import-type="importType.PERFORMANCE_DATA"
        @success="handleUploadSuccess"
        @error="handleUploadError"
      />
    </div>
    <div class="table-wrapper flex-1 flex flex-col">
      <el-table :data="tableData" style="width: 100%" class="flex-1 overflow-y-auto">
        <el-table-column type="index" width="50" label="序号" align="center">
          <template slot-scope="scope">
            {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="month" label="绩效所属月" width="100">
          <template slot-scope="scope">
            <span>{{ monthFormat(scope.row.month) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="department" label="所属部门" width="120" />
        <el-table-column prop="employeeName" label="员工姓名"></el-table-column>
        <el-table-column prop="score" label="绩效得分"></el-table-column>
        <el-table-column prop="coefficient" label="对应系数"></el-table-column>
        <el-table-column prop="uploaderName" label="上传人"></el-table-column>
        <el-table-column prop="uploadTime" label="上传时间"></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          :page-sizes="pageSizes"
          :page-size="pageSize"
          layout="total,sizes, prev, pager, next, jumper"
          :total="total"
          size="mini"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.page-wrapper {
  width: 100%;
  text-align: left;
  padding: 0 16px;
  box-sizing: border-box;
  overflow: hidden;
  margin-bottom: 16px;
}

.table-wrapper {
  box-sizing: border-box;
  padding: 0 16px 16px;
  background-color: white;
  overflow-y: auto;
}

.box {
  background: #fff;
  padding: 20px;
  border-radius: 5px;
}

.add-wrapper {
  margin: 16px 0;
}

.pagination {
  margin-top: 24px;
  text-align: right;
}

/deep/ .el-button--primary {
  background-color: #0c88be;
  border-color: #0c88be;
}

.search-box {
  box-sizing: border-box;
  text-align: left;

  /deep/ .el-form {
    .el-form-item {
      margin-bottom: 0;
    }
  }
}
</style>
