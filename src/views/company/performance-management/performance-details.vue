<template>
<div class="wrapper">
  <div class="header">
    <div class="top">
      <div class="left">
        <span>{{baseInfo.name}}提交的绩效</span>
        <ProgressTag :status="performanceDetail.status" :flowStatus="performanceDetail.flowStatus"/>
      </div>
      <div class="right">绩效单号：{{performanceDetail.performanceNo}}</div>
    </div>
    <div class="bottom">
      <div class="item">
        <span>绩效人</span>
        <span>{{baseInfo.name}}</span>
      </div>
      <div class="item">
        <span>手机号码</span>
        <span>{{baseInfo.phone}}</span>
      </div>
      <div class="item">
        <span>所属部门</span>
        <span>{{baseInfo.deptName}}</span>
      </div>
      <div class="item">
        <span>职位名称</span>
        <span>{{positionName}}</span>
      </div>
      <div class="item">
        <span>直属上级</span>
        <span>{{baseInfo.superintendName}}</span>
      </div>
      <div class="item">
        <span>考核周期</span>
        <span>{{performanceDetail.year}}-{{performanceDetail.month}}</span>
      </div>
    </div>
  </div>
  <div class="progress">
    <MySteps :stepInfo="stepInfo" v-if="stepInfo"/>
    <div class="record">
      <div class="title">审核动态</div>
      <div class="content">
        <div class="item" v-for="item in operationRecordsList" :key="item.operationId">
          <div class="left">
            <div class="first">
              <span :class="{deniedIcon:item.status===2,passedIcon:item.status===1}"></span>
              <span>{{item.operationName}}</span>
              <span :class="{deniedStatusText:item.status===2,passedStatusText:item.status===1}" style="margin: 0 8px">{{ item.status===2? '驳回' : '通过' }}</span>
              <span>绩效</span>
            </div>
            <div class="second">
              <span>{{item.describe}}</span>
            </div>
          </div>
          <div class="right">{{isNaN(item.createTime)? item.createTime : timeMode(item.createTime).dateMin}}</div>
        </div>
      </div>
    </div>
  </div>
  <div class="details-box">
    <div class="title">绩效详情</div>
<!--    销售业绩指标-->
    <div class="defalut-content" v-if="paramsInfo.type==='1'">
      <div class="label">个人业绩指标</div>
      <div class="details-itemList">
        <div class="detail-item" v-for="(item,index) in sellerPersonalPerformance" :key="index">
          <div class="top">
            <div class="left">
              {{item.assessmentName}}
            </div>
            <div class="right">
              <span>权重：{{item.weight===''? '0' : item.weight}}%</span>
              <span>自评：{{item.personalScore}}</span>
              <span>终评：{{item.examineScore}}</span>
              <span>{{superiorName}}</span>
            </div>
          </div>
          <div class="des" v-if="item.assessmentDesc!==''">
            {{item.assessmentDesc}}
          </div>
        </div>
      </div>
    </div>
<!--    市场业绩指标-->
    <div class="defalut-content" v-if="paramsInfo.type==='2'">
      <div class="label">个人业绩指标</div>
      <div class="details-itemList">
        <div class="hospital-box" v-for="(hos,index) in marketerPersonalPerformance" :key="index">
          <div class="hos-name">{{hos.hospitalName}}（当月指标：{{hos.quota}}）</div>
          <div class="cooperation-progress">
            <div class="label">
              <div class="cooperation-title">
                <span></span>
                <span>合作进度</span>
              </div>
              <div class="index">
                <span>已分解指标：{{hos.total}}</span>
              </div>
            </div>
            <div class="area-content">
              <div class="area" v-for="area in hos.collaborateProgress" :key="area.areaId">
                <div class="area-name">{{area.area}}（指标{{area.quota}}）</div>
                <div class="area-item" v-for="doctor in  area.doctors" :key="doctor.doctorId">
                  <span>{{doctor.doctorName}}</span>
                  <span>{{doctor.quota}}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="sign-progress">
            <div class="label">
              <div class="sign-title">
                <span></span>
                <span>签约进度</span>
              </div>
              <div :class="{signstatus:hos.signProgress.signStatus!==4,finshed:hos.signProgress.signStatus===4}">
                <span>{{
                    hos.signProgress.signStatus === 0
                      ? '明确医院组织架构'
                      : hos.signProgress.signStatus === 1
                        ? '拜访关键决策人'
                        : hos.signProgress.signStatus === 2
                          ? '组织院办会'
                          : '已签约'
                  }}</span>
              </div>
            </div>
            <div class="sign-content">
              <div class="area">
                <div class="sign-item" v-for="keyMan in hos.signProgress.keyMans" :key="keyMan.keyManId">
                  <span>{{keyMan.keyManName}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>
        <div class="bottom-content">
          <div class="_area _botomarea">
            <span>自评：{{marketerPerformanceScore.length? marketerPerformanceScore[0].personalScore : ''}}</span>
            <span class="superior">
                  <span>终评：{{marketerPerformanceScore.length? marketerPerformanceScore[0].examineScore : ''}}</span>
                  <span class="name">{{superiorName}}</span>
                </span>
          </div>
        </div>
    </div>
<!--    个人素养-->
    <div class="defalut-content">
      <div class="label">职业素养</div>
      <div class="details-itemList">
        <div class="detail-item" v-for="(item,index) in professionalCompetence" :key="index">
          <div class="top">
            <div class="left">
              {{item.assessmentName}}
            </div>
            <div class="right">
              <span>权重：{{item.weight===''? '0' : item.weight}}%</span>
              <span>自评：{{item.personalScore}}</span>
              <span>终评：{{item.examineScore}}</span>
              <span>{{superiorName}}</span>
            </div>
          </div>
          <div class="des" v-if="item.assessmentDesc!==''">
            {{item.assessmentDesc}}
          </div>
        </div>
      </div>
    </div>
<!--    价值观践行-->
    <div class="defalut-content">
      <div class="label">价值观践行</div>
      <div class="details-itemList">
        <div class="detail-item" v-for="(item,index) in personalValues" :key="index">
          <div class="top">
            <div class="left">
              {{item.assessmentName}}
            </div>
            <div class="right">
              <span>权重：无</span>
              <span>自评：{{item.personalScore ? item.personalScore :'无'}}</span>
              <span>终评：{{item.examineScore ? item.examineScore :'无'}}</span>
              <span>{{superiorName}}</span>
            </div>
          </div>
          <div class="des" v-if="item.assessmentDesc!==''">
            {{item.assessmentDesc}}
          </div>
        </div>
      </div>
    </div>
    <div class="singRow">
      <div class="left">
        <span>自评</span>
        <span>（{{stepInfo.create.name}}）</span>
      </div>
      <div class="right">
        <span class="des" v-if="stepInfo.create.status">{{evaluationList.length? evaluationList[0].assessmentDesc : ""}}</span>
        <span class="waiting" v-if="!stepInfo.create.status">待提交</span>
      </div>
    </div>
    <div class="singRow" v-if="stepInfo.superior.status">
      <div class="left">
        <span>终评</span>
        <span>（{{stepInfo.superior.name}}）</span>
      </div>
      <div class="right">
        <span class="des" v-if="stepInfo.superior.status===1">{{evaluationList.length? evaluationList[1].assessmentDesc : ''}}</span>
        <span class="waiting" v-if="stepInfo.superior.status===3">等待审核</span>
      </div>
    </div>
    <div class="singRow" v-if="stepInfo.director.status">
      <div class="left">
        <span>特殊审核</span>
        <span>
          <span v-if="stepInfo.director.name">（{{stepInfo.director.name}}）</span>
        </span>
      </div>
      <div class="right">
        <span class="des" v-if="stepInfo.director.status===1">{{evaluationList.length? evaluationList[2].assessmentDesc : ''}}</span>
        <span class="passed" v-if="stepInfo.director.status===4">无需审核</span>
        <span class="waiting" v-if="stepInfo.director.status===3">等待审核</span>
      </div>
    </div>
    <div class="singRow" v-if="performanceDetail.status===2">
      <div class="left">
        <span>驳回理由</span>
        <span></span>
      </div>
      <div class="right">
        <span class="des" style="color: #F56C6C">{{refuseRecord}}</span>
      </div>
    </div>
    <div class="singRow" v-if="stepInfo.hr.status!==2 && stepInfo.hr.status!==null">
      <div class="left">
        <span>人事审核</span>
        <span></span>
      </div>
      <div class="right">
        <span class="passed" v-if="stepInfo.hr.status===1">已通过</span>
        <span class="waiting" v-if="stepInfo.hr.status===3&& hrPermission===0">等待审核</span>
        <span v-if="stepInfo.hr.status===3 && hrPermission===1">
          <span class="passbtn" @click="showPassedDialog">通过</span>
          <span class="denybtn" @click="showRefuseDialog=!showRefuseDialog">
            <span >
              驳回
            <i class="el-icon-caret-top"></i>
            </span>
            <div class="confirm-dialog" v-if="showRefuseDialog">
              <div v-for="item in refuseData" :key="item.value" @click="chooseReason(item)">
                <span v-if="item.isShow===0">{{item.label}}</span>
              </div>
            </div>
          </span>
        </span>
      </div>
    </div>
    <div class="singRow" v-if="stepInfo.confirm.status">
      <div class="left">
        <span>绩效人确认</span>
        <span></span>
      </div>
      <div class="right">
        <span class="passed" v-if="stepInfo.confirm.status===1">已通过</span>
        <span class="waiting" v-if="stepInfo.confirm.status===3">等待确认</span>
      </div>
    </div>
  </div>
  <div class="backbtn" @click="$router.back()">返回列表</div>
  <el-dialog
    :visible.sync="visibleDialog"
    class="download-dialog"
    :show-close="false"
    :modal="false"
    @close="remarks=''"
    :close-on-click-modal="false"
  >
      <span slot="title" class="show-title">
        <span>驳回理由</span>
      </span>
    <div class="event-content">
      <el-input
        type="textarea"
        :rows="2"
        placeholder="请输驳回理由"
        v-model.trim="remarks">
      </el-input>
      <div class="btnBox">
        <span @click="refusePerformance(2)">驳回</span>
        <span @click="visibleDialog = false,showRefuseDialog=false">取消</span>
      </div>
    </div>
  </el-dialog>
  <el-dialog
    :visible.sync="passedVisibleDialog"
    :show-close="false"
    :modal="false"
    class="passed-dialog"
    :close-on-click-modal="false"
  >
    <span>是否确认通过？</span>
    <div class="btnBox">
      <span @click="refusePerformance(1)">通过</span>
      <span @click="passedVisibleDialog=false">取消</span>
    </div>
  </el-dialog>
</div>
</template>

<script>
import ProgressTag from "./components/ProgressTag";
import MySteps from "./components/MySteps";
import { httpReq } from "@/http";
import {timeMode} from "../../../util/util";
export default {
  name: "performance-details",
  components:{
    ProgressTag,
    MySteps
  },
  data(){
    return{
      timeMode,
      refuseData:[],
      showRefuseDialog:false,
      visibleDialog:false,
      passedVisibleDialog:false,
      paramsInfo:{
        performanceId:'',
        type:''
      },
      baseInfo:{},
      performanceDetail:{},
      //销售业绩
      sellerPersonalPerformance:[],
      //市场业绩表现
      marketerPersonalPerformance:[],
      //市场业绩评分
      marketerPerformanceScore:[],
      //个人素养
      professionalCompetence:[],
      //价值观践行
      personalValues:[],
      //评价列表
      evaluationList:[],
      stepInfo:{
        confirm: {
          date: "",
          name: "",
          status: null
        },
        superior: {
          date: "",
          name: "",
          status: null
        },
        director: {
          date: "",
          name: "",
          status: null
        },
        create: {
          date: "",
          name: "",
          status: null
        },
        hr: {
          date: "",
          name: "",
          status: null
        },
        flowStatus: "",
        status: "",
        //选择驳回到哪个节点
      },
      remarks:"",
      refuseNode:'',
      operationRecordsList:[],
      hrPermission:''
    }
  },
  computed:{
    //直属上级
    superiorName(){
      if (this.stepInfo.superior.name){
        return `（${this.stepInfo.superior.name}）`
      }else {
        return ''
      }
    },
    //获取人事驳回记录
    refuseRecord(){
      let res = this.operationRecordsList.filter(item=>item.status===2)
      if (res.length){
        return res[0].remarks
      }else {
        return  ""
      }
    },
    //获取是否展示驳回到特殊审核
    isAbledToRefuseDirector(){
      if (this.operationRecordsList.length){
        if (this.operationRecordsList[0].flowStatus===2){
          return 0
        }else {
          return  1
        }
      }else {
        return 1
      }
    },
    //获取职位名称
    positionName(){
      if (Object.keys(this.baseInfo).length){
        if (this.baseInfo.type===2){
          if (this.baseInfo.roleType===2)return  "市场经理"
          if (this.baseInfo.roleType===1)return  "市场总监"
        }
        if (this.baseInfo.type===1){
          if (this.baseInfo.roleType===1)return  "健康顾问"
          if (this.baseInfo.roleType===2)return  "销售总监"
          if (this.baseInfo.roleType===3)return  "区域经理"
          if (this.baseInfo.roleType===4)return  "总经理"
        }
      }else {
        return ''
      }
    }
  },
  created() {
    console.log(this.$route.query)
    this.paramsInfo.performanceId=this.$route.query.performanceId
    this.paramsInfo.type=this.$route.query.type
    console.log(this.paramsInfo)
    this.getPerformanceDetails()
    this.getProcess()
    this.getOperationsRecords()
    this.hrPermission = parseInt(sessionStorage.getItem('hrPermission'))
  },
  methods:{
    chooseReason(item){
      this.refuseNode = item.value
      this.showRefuseDialog = false
      this.visibleDialog = true
    },
    //获取操作记录
    getOperationsRecords(){
      httpReq({
        url: "/performanceReview/getPerformanceOperation",
        method: "post",
        data: this.paramsInfo
      }).then(res=>{
        console.log(res)
        this.operationRecordsList = res.data
        this.getRefuseDataList()

      })
    },
    //获取绩效详情
    getPerformanceDetails(){
      httpReq({
        url: "/performanceReview/getDetail",
        method: "post",
        data: this.paramsInfo
      }).then(res => {
        console.log('进入了------------------------------------')
        console.log(res)
        if (res.data.tasks){
          res.data.tasks.forEach(hos => {
            hos['total'] = 0;
            hos.collaborateProgress.forEach(area => {
              if (area.quota) {
                hos.total += area.quota;
              }
            });
          });
          this.marketerPersonalPerformance = res.data.tasks
          console.log(this.marketerPersonalPerformance,'===========')
        }
        this.baseInfo=res.data.baseInfo
        console.log(this.baseInfo,'=====-------------------------------------------------------=====')
        res.data.performanceDetail.additionalAssessmentInfo=res.data.performanceDetail.additionalAssessmentInfo ==="" ? [] : JSON.parse(res.data.performanceDetail.additionalAssessmentInfo)
        res.data.performanceDetail.defaultAssessmentInfo=res.data.performanceDetail.defaultAssessmentInfo ==="" ? [] : JSON.parse(res.data.performanceDetail.defaultAssessmentInfo)
        this.performanceDetail=res.data.performanceDetail
        this.addData()
      });
    },
    getProcess(){
      httpReq({
        url: "/performanceReview/getProcess",
        method: "post",
        data: this.paramsInfo
      }).then(res=>{
        console.log(res)
        this.stepInfo = res.data
      })
    },
    showPassedDialog(){
      this.passedVisibleDialog = true
      //4为通过
      this.refuseNode = 4
      this.remarks = ''
    },
    //添加考核项数据
    addData(){
      this.performanceDetail.defaultAssessmentInfo.forEach(item=>{
        if (this.paramsInfo.type==='1'){
          if (item.modelType===0){
            this.sellerPersonalPerformance.push(item)
          }
        }else {
          if (item.modelType===0){
            this.marketerPerformanceScore.push(item)
          }
        }
        if (item.modelType===2){
          this.professionalCompetence.push(item)
        }
        if (item.modelType===4){
          this.personalValues.push(item)
        }
        if (item.modelType===6){
          this.evaluationList.push(item)
        }
      })
      if (Array.isArray(this.performanceDetail.additionalAssessmentInfo)){
        this.performanceDetail.additionalAssessmentInfo.forEach(item=>{
          if (this.paramsInfo.type==='1'){
            if (item.modelType===1){
              this.sellerPersonalPerformance.push(item)
            }
          }
          if (item.modelType===3){
            this.professionalCompetence.push(item)
          }
          if (item.modelType===5){
            this.personalValues.push(item)
          }
        })
      }
    },
    //驳回绩效
    refusePerformance(status){
      if (status===2){
        if (this.remarks===''){
          return this.$message.warning('请填写驳回理由！')
        }else {
          this.refuseReq(status)
        }
      }else {
        this.refuseReq(status)
      }
    },
    //驳回当前绩效请求
    refuseReq(status){
      let params = {
        ...this.paramsInfo,
        flowStatus:this.refuseNode,
        status,
        remarks:this.remarks
      }
      httpReq({
        url: "/performanceReview/updateStatus",
        method: "post",
        data: params
      }).then(res=>{
        console.log(res)
        if (status===1){
          this.$message.success('已通过该绩效!')
          this.passedVisibleDialog = false
        }else {
          this.$message.success(`已驳回至${this.refuseNode===0? "绩效人" : this.refuseNode===1? "上级审批" : "特级审批"}`)
          this.visibleDialog = false
          this.showRefuseDialog = false
          this.getOperationsRecords()
        }
        this.getProcess()
      }).catch(err=>{
        if (status===1){
          this.$message.error(`通过失败:${err.msg}`)
        }else {
          this.$message.error(`驳回失败:${err.msg}`)
        }
      })
    },
    getRefuseDataList(){
      this.refuseData =[
        {
          value:0,
          label:"驳回【绩效人】",
          isShow:0
        },
        {
          value:1,
          label:"驳回【上级审批】",
          isShow:0

        },
        {
          value:2,
          label:"驳回【特级审批】",
          isShow:this.isAbledToRefuseDirector

        },
      ]
      console.log(this.refuseData,'=====================')
    }
  }
}
</script>

<style scoped lang="less">
.wrapper{
  box-sizing: border-box !important;
  padding: 0 16px;
  text-align: left;
  font-family: PingFangSC-Semibold, PingFang SC;
  .header{
    box-sizing: border-box;
    background-color: #FFFFFF;
    padding: 16px;
    .top{
      border-bottom: 1px solid  #D8D8D8;
      padding-bottom: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left{
        span:first-child{
          font-size: 20px;
          font-weight: 600;
          color: #111111;
          margin-right: 16px;
        }
      }
      .right{
        font-size: 12px;
        font-weight: 400;
        color: #666666;
      }
    }
    .bottom{
      padding-top: 24px;
      display: flex;
      justify-content: space-between;
      .item{
        font-size: 14px;
        font-weight: 400;
        span:first-child{
          color: #999999;
          margin-right: 24px;
        }
        span:last-child{
          color: #333333;
        }
      }
    }
  }
  .progress{
    margin-top: 16px;
    background-color: #FFFFFF;
    box-sizing: border-box;
    padding: 24px;
    .record{
      margin-top: 16px;
      box-sizing: border-box;
      padding: 16px;
      background-color: #F7FCFF;
      font-family: PingFangSC-Semibold, PingFang SC;
      .title{
        font-size: 14px;
        font-weight: 600;
        color: #333333;
      }
      .content{
        margin-top: 16px;
        .item{
          display: flex;
          justify-content: space-between;
          line-height: 20px;
          .left{
            font-size: 12px;
            font-weight: 400;
            color: #333333;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 400px;
            .first{
              display: flex;
              align-items: center;
              .deniedIcon{
                display: inline-block;
                width: 6px;
                height: 6px;
                background-color: #F56C6C;
                border-radius: 50%;
                margin-right: 6px;
              }
              .passedIcon{
                display: inline-block;
                width: 6px;
                height: 6px;
                background-color: #1CC37C;
                border-radius: 50%;
                margin-right: 6px;

              }
              .deniedStatusText{
                color: #F56C6C;
              }
              .passedStatusText{
                color: #1CC37C;
              }
            }
            .second{
              width: 200px;
              .passedText{
                color: #999999;
              }
              .deniedText{
              color: #F56C6C;
            }
              .passedText{
                color: #1CC37C;
              }
            }
          }
          .right{
            font-size: 12px;
            font-weight: 400;
            color: #999999;
          }
        }
      }
    }
  }
  .details-box{
    box-sizing: border-box;
    padding: 16px;
    font-family: PingFangSC-Semibold, PingFang SC;
    background-color: #FFFFFF;
    margin-top: 16px;
    .title{
      border-left: 4px solid #0C88BE;
      font-size: 16px;
      font-weight: 600;
      color: #0C88BE;
      padding-left: 10px;
    }
    .details-content{
      margin-top: 24px;
      border-bottom: 1px solid #D8D8D8;
      padding-bottom: 12px;
      .label{
        font-size: 14px;
        font-weight: bold;
        color: #333333;
      }
      .details-itemList{
        margin-top: 16px;
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        .detail-item{
          display: flex;
          align-items: center;
          line-height: 44px;
          .left{
            width: 100px;
            margin-right: 180px;
          }
          .right{
            span{
              margin-right: 68px;
            }
            span:nth-child(3){
              margin-right: 16px;
            }
            span:last-child{
              color: #999999;
            }
          }
        }
      }
    }
    .defalut-content{
      margin-top: 24px;
      border-bottom: 1px solid #D8D8D8;
      padding-bottom: 24px;
      .label{
        font-size: 14px;
        font-weight: bold;
        color: #333333;
      }
      .details-itemList{
        margin-top: 16px;
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        .detail-item{
          .top{
            display: flex;
            align-items: center;
            line-height: 28px;
            .left{
              width: 100px;
              margin-right: 180px;
            }
            .right{
              span{
                margin-right: 68px;
                width: 64px;
                display: inline-block;
              }
              span:nth-child(3){
                width: auto;
                margin-right: 16px;
              }
              span:last-child{
                color: #999999;
                width: auto;
              }
            }
          }
          .des{
            box-sizing: border-box;
            padding: 16px;
            background-color: #FAFAFA;
            font-size: 12px;
            font-weight: 400;
            color: #999999;
            line-height: 17px;
            margin-top: 4px;
          }
        }
        .hospital-box{
          font-family: PingFangSC-Semibold, PingFang SC;
          .hos-name{
            font-size: 14px;
            font-weight: 600;
            color: #333333;
            margin-bottom: 12px;
          }
          .cooperation-progress{
            .label{
              display: flex;
              justify-content: space-between;
              align-items: center;
              .cooperation-title{
                font-size: 14px;
                font-weight: bold;
                color: #333333;
                display: flex;
                align-items: center;
                span:first-child{
                  display: inline-block;
                  width: 8px;
                  height: 8px;
                  background-color:#5ACA6D;
                  border-radius: 50%;
                  margin-right: 10px;
                }
              }
              .index{
                font-size: 14px;
                font-weight: 400;
                color: #333333;
              }
            }
            .area-content{
              box-sizing: border-box;
              padding: 16px 20px;
              .area{
                box-sizing: border-box;
                padding: 16px;
                background-color: #FAFAFA;
                margin-bottom: 12px;
                .area-name{
                  font-size: 14px;
                  font-weight: bold;
                  color: #333333;
                  margin-bottom: 8px;
                }
                .area-item{
                  font-size: 12px;
                  font-weight: 400;
                  color: #111111;
                  line-height: 22px;
                  span:first-child{
                    width: 150px;
                    margin-right: 118px;
                  }
                }
              }
            }
          }
          .sign-progress{
            .label{
              display: flex;
              justify-content: space-between;
              align-items: center;
              .sign-title{
                font-size: 14px;
                font-weight: bold;
                color: #333333;
                display: flex;
                align-items: center;
                span:first-child{
                  display: inline-block;
                  width: 8px;
                  height: 8px;
                  background-color:#F56C6C;
                  border-radius: 50%;
                  margin-right: 10px;
                }
              }
              .signstatus{
                font-size: 14px;
                font-weight: 400;
                color: #0C88BE;
              }
              .finshed{
                font-size: 14px;
                font-weight: 400;
                color: #00b389;
              }
            }
            .sign-content{
              box-sizing: border-box;
              padding: 16px 20px 0 20px;
              .botomarea{
                display: flex;
                justify-content: center;
                span{
                  font-size: 14px;
                  font-weight: bold;
                  color: #333333;
                }
                .superior{
                  margin-left: 58px;
                }
                .name{
                  font-size: 14px;
                  font-weight: bold;
                  color: #999999;
                }
              }
              .area{
                box-sizing: border-box;
                padding: 16px;
                background-color: #FAFAFA;
                margin-bottom: 12px;
                .area-name{
                  font-size: 14px;
                  font-weight: bold;
                  color: #333333;
                  margin-bottom: 8px;
                }
                .sign-item{
                  font-size: 12px;
                  font-weight: 400;
                  color: #111111;
                  line-height: 22px;
                }
              }
            }
          }
        }
      }
    }
    .singRow{
      display: flex;
      align-items: center;
      padding: 24px 0;
      border-bottom: 1px solid #D8D8D8;
      .left{
        width: 150px;
        margin-right: 130px;
        span:first-child{
          font-size: 14px;
          font-weight: bold;
          color: #333333;
        }
        span:last-child{
          font-size: 12px;
          font-weight: 400;
          color: #999999;
        }
      }
      .right{
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #303133;
        .waiting{
            color: #FE8D3C;
        }
        .passed{
          color: #5ACA6D;
        }
        .passbtn{
          font-size: 14px;
          font-weight: 400;
          color: #FFFFFF;
          padding: 6px 34px ;
          background-color: #5ACA6D;
          border-radius: 4px;
          cursor: pointer;
          margin-right: 32px;
        }
        .denybtn{
          font-size: 14px;
          font-weight: 400;
          color: #111111;
          width: 90px;
          height: 32px;
          border-radius: 4px;
          border: 1px solid #EBEEF5;
          cursor: pointer;
          display: inline-block;
          text-align: center;
          line-height: 32px;
          position: relative;
          .confirm-dialog{
            position: absolute;
            font-size: 14px;
            font-weight: 400;
            color: #323233;
            box-sizing: border-box;
            bottom: 0;
            right: -150px;
            background-color: #FFFFFF;
            box-shadow:
              2.8px 0.2px 2.2px rgba(0, 0, 0, 0.07),
              6.7px 0.5px 5.3px rgba(0, 0, 0, 0.059),
              12.5px 0.9px 10px rgba(0, 0, 0, 0.047),
              22.3px 1.6px 17.9px rgba(0, 0, 0, 0.035),
              41.8px 2.9px 33.4px rgba(0, 0, 0, 0.022),
              100px 7px 80px rgba(0, 0, 0, 0.01)
          ;
            div{
              padding: 3px 12px;
            }
            div:hover{
              background-color: #E8EFFA;
            }
          }
        }
      }
    }
  }
  .backbtn{
    margin: 16px 0;
    width: 120px;
    height: 40px;
    background: #0C88BE;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 400;
    color: #FFFFFF;
  }
}
/deep/.download-dialog {
  .el-dialog {
    width: 568px;
    background: #ffffff;
    box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    border: 1px solid #e4e7ed;
    box-sizing: border-box;
    margin-top: 20vh !important;
    .el-dialog__header {
      font-size: 18px;
      text-align: left;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #303133;
      padding: 14px 20px ;
      border-bottom: 1px solid #ebeef5;
    }
    .el-dialog__body {
      padding: 16px 20px;
      .event-content{
        .el-textarea__inner{
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #909399;
        }
      }
    }
  }
}
/deep/.passed-dialog{
  .el-dialog {
    width: 360px;
    background: #ffffff;
    box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    border: 1px solid #e4e7ed;
    box-sizing: border-box;
    margin-top: 20vh !important;
    .el-dialog__header {
      display: none;
    }
    .el-dialog__body {
      padding: 16px 20px;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }
}
.show-title{
  font-family: PingFangSC-Regular, PingFang SC;
  font-size: 18px;
}
.btnBox {
  margin: 16px auto;
  display: flex;
  span {
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    width: 76px;
    height: 32px;
    border-radius: 2px;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    cursor: pointer;
  }
  span:first-child {
    background: #2c89dc;
    color: #ffffff;
    margin-right: 16px;
  }
  span:last-child {
    color: #8193a3;
    background: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
}
._botomarea{
  display: flex;
  justify-content: center;
  span{
    font-size: 14px;
    font-weight: bold;
    color: #333333;
  }
  .superior{
    margin-left: 58px;
  }
  .name{
    font-size: 14px;
    font-weight: bold;
    color: #999999;
  }
}
._area{
  box-sizing: border-box;
  padding: 16px;
  background-color: #FAFAFA;
  .area-name{
    font-size: 14px;
    font-weight: bold;
    color: #333333;
    margin-bottom: 8px;
  }
  .sign-item{
    font-size: 12px;
    font-weight: 400;
    color: #111111;
    line-height: 22px;
  }
}
.bottom-content{
  padding: 0 20px;
  box-sizing: border-box;
}
</style>
