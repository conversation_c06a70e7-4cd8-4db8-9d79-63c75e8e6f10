<script>
import { fetchPerformanceList, deletePerformanceById } from '@/api'
import ExcelUpload from '@/components/ExcelUpload.vue'
import { IMPORT_TYPE } from '@/constant'
import { formatDateRange } from '@/util/util'

export default {
  name: 'CompanyEmployeePerformance',
  components: {
    ExcelUpload,
  },
  data() {
    return {
      searchFrom: {
        date: [],
      },
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      pageSizes: [10, 20, 50, 100],
      importType: IMPORT_TYPE,
      multipleSelection: [],
    }
  },
  computed: {
    showDeleteBtn() {
      return this.multipleSelection && this.multipleSelection.length > 0
    },
  },
  methods: {
    async getList(page, pageSize, info) {
      const res = await fetchPerformanceList({
        pageNumber: page,
        pageSize,
        ...formatDateRange(info.date),
      })
      if (res.code === 1) {
        this.tableData = res.data.contents
        this.total = res.data.total
      }
    },
    onSearch() {
      this.currentPage = 1
      this.getList(this.currentPage, this.pageSize, this.searchFrom)
    },
    resetForm() {
      this.currentPage = 1
      this.searchFrom = {
        date: [],
      }
      this.getList(this.currentPage, this.pageSize, this.searchFrom)
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getList(this.currentPage, val, this.searchFrom)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getList(val, this.pageSize, this.searchFrom)
    },
    monthFormat(month) {
      if (month) {
        const monthStr = `${month}`
        const year = `${monthStr}`.substring(0, 4)
        return `${year}年${monthStr.substring(4, 6)}月`
      } else {
        return '--'
      }
    },
    handleUploadSuccess() {
      this.getList(this.currentPage, this.pageSize, this.searchFrom)
    },
    handleUploadError() {},
    deletePerformance(idList) {
      deletePerformanceById(idList)
        .then((res) => {
          if (res.code === 1) {
            this.$message.success('删除成功')
            // 当前处于最后一页并且还有数据，删除后回到上一页
            if (
              this.total === this.pageSize * (this.currentPage - 1) + idList.length &&
              this.currentPage > 1
            ) {
              this.currentPage = this.currentPage - 1
              this.getList(this.currentPage, this.pageSize, this.searchFrom)
            } else {
              this.getList(this.currentPage, this.pageSize, this.searchFrom)
            }
          }
        })
        .catch((err) => {
          this.$message.error(err.msg)
        })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteConfirm(ids) {
      this.$confirm('确认要删除所选数据吗?', {
        title: '提示',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      })
        .then(() => {
          this.deletePerformance(ids)
        })
        .catch(() => {})
    },
    batchDelete() {
      const ids = this.multipleSelection.map((item) => item.id)
      this.deleteConfirm(ids)
    },
    singleDelete(id) {
      this.deleteConfirm([id])
    },
  },
  created() {
    this.getList(this.currentPage, this.pageSize, this.searchFrom)
  },
}
</script>

<template>
  <div class="page-wrapper flex flex-col">
    <div class="search-box box">
      <el-form ref="searchFrom" size="mini" :inline="true" :model="searchFrom" label-width="90px">
        <div class="flex gap-4">
          <el-form-item label="工资所属月">
            <el-date-picker
              v-model="searchFrom.date"
              type="monthrange"
              size="small"
              range-separator="——"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="datepicker"
              value-format="yyyy-MM"
              placeholder="选择月"
            >
            </el-date-picker>
          </el-form-item>
          <div class="btn">
            <el-button type="primary" @click="onSearch" class="search-btn" size="small"
              >查询
            </el-button>
            <el-button size="small" @click="resetForm" class="search-btn">重置 </el-button>
          </div>
        </div>
      </el-form>
    </div>
    <div class="add-wrapper">
      <ExcelUpload
        :import-type="importType.PERFORMANCE_DATA"
        @success="handleUploadSuccess"
        @error="handleUploadError"
      />
      <el-button v-if="showDeleteBtn" type="primary" size="medium" @click="batchDelete">
        删除所选
      </el-button>
    </div>
    <div class="table-wrapper flex-1 flex flex-col">
      <el-table
        :data="tableData"
        style="width: 100%"
        class="flex-1 overflow-y-auto"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column type="index" width="50" label="序号">
          <template slot-scope="scope">
            {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="month" label="工资所属月" width="180">
          <template slot-scope="scope">
            <span>{{ monthFormat(scope.row.month) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="employeeName" label="员工姓名"></el-table-column>
        <el-table-column prop="uploaderName" label="上传人"></el-table-column>
        <el-table-column prop="uploadTime" label="上传时间"></el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button @click="singleDelete(scope.row.id)" type="text" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          :page-sizes="[10, 20, 50]"
          :page-size="pageSize"
          layout="total,sizes, prev, pager, next, jumper"
          :total="total"
          size="mini"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.page-wrapper {
  width: 100%;
  text-align: left;
  padding: 0 16px;
  box-sizing: border-box;
  overflow: hidden;
  margin-bottom: 16px;
}

.table-wrapper {
  box-sizing: border-box;
  padding: 0 16px 16px;
  background-color: white;
  overflow-y: auto;
}

.box {
  background: #fff;
  padding: 20px;
  border-radius: 5px;
}

.add-wrapper {
  margin: 16px 0;
  display: flex;
  gap: 16px;
  align-items: center;
}

.pagination {
  margin-top: 24px;
  text-align: right;
}

/deep/ .el-button--primary {
  background-color: #0c88be;
  border-color: #0c88be;
}

.search-box {
  box-sizing: border-box;
  text-align: left;

  /deep/ .el-form {
    .el-form-item {
      margin-bottom: 0;
    }
  }
}
</style>
