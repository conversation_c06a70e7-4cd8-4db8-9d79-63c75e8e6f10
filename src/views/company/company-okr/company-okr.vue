<!-- 公司架构页面 -->
<template>
  <div class="content-wrapper">
    <div class="search-box box">
      <el-form ref="searchFrom" size="mini" :inline="true" :model="searchFrom">
        <el-form-item label="公司名称" class="minclass" prop="companyName">
          <el-input
            v-model.trim="searchFrom.companyName"
            placeholder="请输入公司名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态" class="minclass" prop="status">
          <el-select
            v-model="searchFrom.status"
            class="minclass"
            placeholder="请选择计划类型"
            filterable
          >
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="(item, index) in companyStatusOptions"
              :key="index"
              :label="item.name"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" class="date-mini-class" prop="dateRange">
          <el-date-picker
            v-model="searchFrom.dateRange"
            type="daterange"
            align="right"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
        </el-form-item>
        <span class="btn-group">
          <el-button type="primary" @click="onSearch" class="search-btn">查询</el-button>
          <el-button @click="resetForm" class="reset-btn">重置</el-button>
        </span>
      </el-form>
    </div>
    <div class="data-box-wrapper">
      <div class="data-box box">
        <div class="table">
          <el-table
            :data="hosList"
            style="width: 100%"
            ref="table"
            height="98%"
            :header-cell-style="{
              color: '#15233F',
              background: '#F7F8FA',
              'font-weight': 'bold',
              'font-size': '14px'
          }"
          >
            <el-table-column align="center" label="序号" width="60" fixed>
              <template slot-scope="scope">
                {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="companyName" align="left" label="公司名称" width="150" fixed>
            </el-table-column>
            <el-table-column prop="departmentNum" align="left" label="部门数量" width="100">
            </el-table-column>
            <el-table-column prop="staffNum" align="left" label="员工数量" width="100">
            </el-table-column>
            <el-table-column prop="status" align="left" label="状态" width="100">
              <template slot-scope="scope">
                {{companyStatusMap[scope.row.status]}}
              </template>
            </el-table-column>
            <el-table-column prop="createUser" align="left" label="创建人" width="100">
              <template slot-scope="scope">
                {{scope.row.createUser || '--'}}
              </template>
            </el-table-column>
            <el-table-column prop="createTime" align="left" label="创建时间" width="200">
              <template slot-scope="scope">
                {{scope.row.createTime || '--'}}
              </template>
            </el-table-column>
            <el-table-column prop="operation" align="left" label="操作"  fixed="right">
              <template slot-scope="scope">
                <el-button
                  @click="toLookDetails(scope.row.companyId)"
                  type="text"
                  size="small"
                  class="normal-text-btn"
                >公司详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          :page-sizes="[10, 20, 50]"
          :page-size="pageSize"
          layout="total,sizes, prev, pager, next"
          :total="total"
          size="mini"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { timeMode } from "@/util/util";
import {companyStatusOptions} from "../constant/okrEnum";
import {getCompanyList} from "../../../api/company";
import {companyStatusMap} from "../constant/okrEnum";
import parameterCacheMixin from "../../../mixins/parameterCacheMixin";
export default {
  mixins:[parameterCacheMixin],
  data() {
    return {
      companyStatusOptions,
      companyStatusMap,
      timeMode,
      searchFrom: {
        companyName:'',
        status:'',
        dateRange:[]
      },
      hosList:[],
      currentPage: 1,
      pageSize: 10,
      total: 1,
      pickerOptions: {
        onPick: this.getPickDate,
        disabledDate: this.disabledDate
      },
      pickDate: {},
    };
  },
  computed: {
  },
  watch: {},
  created() {
  },
  mounted() {
    this.getCompanyList(this.currentPage, this.pageSize, this.searchFrom);
  },
  methods: {
    //搜索
    onSearch() {
      this.getCompanyList(1, this.pageSize, this.searchFrom);
    },
    //重置
    resetForm() {
      this.$refs['searchFrom'].resetFields();
      this.getCompanyList(this.currentPage, this.pageSize, this.searchFrom);
    },
    toLookDetails(companyId) {
      this.$router.push({
        path: "/index/company-okr-detail",
        query:{
          companyId,
        }
      });
    },
    //请求列表
    getCompanyList(pageNumber, pageSize, info) {
      const  {dateRange,...resParams } = info
      let params = {
        startTime:dateRange.length? dateRange[0] : null,
        endTime:dateRange.length? dateRange[1] : null,
        ...resParams,
        pageNumber,
        pageSize
      }
      console.log(params)
      getCompanyList(params).then(res=>{
        this.hosList = res.data.contents
        this.total = res.data.total;
      })
    },
    // 切换每页条数
    handleSizeChange(val) {
      this.pageSize = val;
      this.getCompanyList(1, this.pageSize, this.searchFrom);
    },
    // 切换页码
    handleCurrentChange(val) {
      this.getCompanyList(val, this.pageSize, this.searchFrom);
    },
    getPickDate(pick) {
      this.pickDate = pick
    },
    disabledDate(date){
      const { minDate, maxDate } = this.pickDate
      if (minDate && !maxDate) {
        const diff = Math.abs(minDate.valueOf() - date.valueOf())
        if (diff > 1000 * 3600 * 24 * 365) {
          return true
        }
      }
    }
  }
};
</script>
<style lang="less" scoped>
.content-wrapper {
  display: flex;
  flex-direction: column;
  /deep/.el-dialog__header {
    padding: 0;
  }
  /deep/.el-dialog--center {
    margin-top: 10vh !important;
  }
  /deep/.el-dialog__wrapper.self-dialog {
    .el-dialog {
      width: 800px;
      max-height: 95vh !important;
      background: #ffffff;
      box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      border: 1px solid #e4e7ed;
      box-sizing: border-box;
      margin-top: 2vh !important;
      .el-dialog__header {
        font-size: 18px;
        text-align: left;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #303133;
        padding: 20px 20px 16px;
        border-bottom: 1px solid #ebeef5;
      }
      .el-dialog__body {
        padding: 16px  0;
      }
    }
  }
  /deep/.el-dialog__wrapper.delete-dialog {
    .el-dialog {
      width: 400px;
      background: #ffffff;
      box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      border: 1px solid #e4e7ed;
      box-sizing: border-box;
      margin-top: 30vh !important;
      .el-dialog__header {
        font-size: 18px;
        text-align: left;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #303133;
        padding: 20px 20px 16px;
        border-bottom: 1px solid #ebeef5;
      }
      .el-dialog__body {
        padding: 16px 20px;
      }
    }
  }
  .logo-box{
    text-align: left;
    .block{
      .el-avatar{
        background-color: #FFFFFF;
        border: 1px dashed #DCDFE6;
        font-size: 30px;
        color: #D9D9D9;
        position: relative;
        overflow: hidden;
      }
      .upload-btn{
        bottom: 30px;
        left: 24px;
        position: absolute;
        color: #D9D9D9;
        height: calc(100% - 30px);
        display: flex;
        align-items: flex-end;
      }
      .delete-mask{
        top: 0;
        width: 100px;
        height: 100px;
        position: absolute;
        color: #FFFFFF;
        font-size: 24px;
        background-color: #333333;
        text-align: center;
        line-height: 100px;
        opacity: 0;
        cursor: pointer;
      }
      .delete-mask:hover  {
        transition: all .3s;
        opacity: 0.3;
      }
    }
  }
  .delete-text{
    width: 100%;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
  }
  .search-box {
    box-sizing: border-box;
    text-align: left;
    display: flex;
    align-items: center;
    /deep/.el-form {
      .el-form-item {
        margin: 0 20px 8px 0;
      }
      .minclass .el-form-item__content {
        .el-date-editor.el-input__inner {
          width: 360px;
        }
        .el-input__inner {
          border-radius: 2px;
          height: 32px;
          font-size: 14px;
          width: 220px;
        }
        .el-cascader{
          .el-input__inner{
            border-radius: 2px;
            height: 32px;
            font-size: 14px;
            width: 220px;
          }
        }
      }
      .minclass .el-form-item__label {
        line-height: 32px;
        font-size: 14px;
        color: #000000;
      }
      .minclass .el-icon-arrow-up:before {
        content: "";
      }
      .date-mini-class {
        .el-input__inner {
          width: 360px;
          border-radius: 2px;
          height: 32px;
          .el-range-separator{
            line-height: 24px;
          }
        }
        .el-range-input{
          font-size: 14px !important;
        }
      }
      .date-mini-class .el-form-item__label {
        line-height: 32px;
        font-size: 14px;
        color: #000000;
      }
    }
  }
  .data-box-wrapper{
    box-sizing: border-box;
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .data-box {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #FFFFFF;
    border-radius: 6px;
    padding: 24px;
    .function-box {
      height: 40px;
      text-align: left;
      padding-bottom: 24px;
      .operation-btn {
        background-color: #2E6BE6;
      }
    }
    .table {
      flex: 1;
    }
    .el-pagination {
      text-align: center;
    }
  }
  .box {
    padding: 24px;
  }
}
.dialog-footer {
  display: flex;
  justify-content: center;
}
.select-box{
  position: relative;
  .add-text{
    position: absolute;
    right: -40px;
    color: #1890FF;
    cursor: pointer;
  }
}
/deep/.select-box .el-icon-arrow-up:before {
  content: "";
}
/deep/.select-box .el-icon-arrow-down:before {
  content: "\e790";
}

.edit-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  /deep/.el-form.self-rule-from {
    margin-bottom: 32px;
    display: flex;
    flex-direction: column;
    .el-form-item__label {
      width: 86px !important;
      line-height: 40px;
      padding-right: 20px;
    }
    .el-form-item__content {
      width: 442px;
      line-height: 40px;
      border-radius: 4px;
      margin-left: 86px !important;
      .el-input__inner {
        width: 442px;
        height: 40px;
        line-height: 40px;
        padding: 13px 15px;
        font-size: 14px;
        border-radius: 2px;
        &::placeholder {
          color: #909399;
        }
        // border-radius: 4px;
      }
    }
    .el-form-item{
      .el-select{
        width: 100%;
      }
    }
  }
  /deep/.el-textarea{
    .el-textarea__inner{
      font-family: PingFangSC-Regular, PingFang SC;
      min-height: 60px !important;
    }
  }
}
.sub-btn {
  box-sizing: border-box;
  width: 100%;
  text-align: right;
  padding: 16px;
  border-top: 1px solid #E9E8EB;
  .el-button {
    width: 104px;
    height: 36px;
    font-size: 14px;
    color: #ffffff;
    background: #2E6BE6;
    margin-left: 20px;
    border-radius: 2px;
    &:last-of-type {
      color: #303133;
      background: #fff;
      border: 1px solid #dcdfe6;
    }
  }
}
.delete-text{
  width: 100%;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  margin: 0 auto;
}
.show-title{
  font-weight: bold;
}
.btn-group{
  line-height: 32px;
  .search-btn {
    width: 88px;
    height: 32px;
    color: #ffffff;
    font-size: 12px;
    background: #2E6BE6;
    padding: 0;
    &:first-of-type {
      margin: 0 16px 0 20px;
    }
  }
  .reset-btn{
    width: 88px;
    height: 32px;
    font-size: 12px;
    padding: 0;
  }
}
.el-button{
  border-radius: 2px;
}
</style>
