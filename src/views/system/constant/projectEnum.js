import { getMapData } from "../../../util/util";

export const projectStatusOptions = [
  {
    name: "初始化",
    value: "INITIALIZED"
  },
  {
    name: "立项申报",
    value: "PROPOSED"
  },
  {
    name: "研究暂停",
    value: "SUSPENDED"
  },
  {
    name: "受试者招募",
    value: "RECRUITED"
  },
  {
    name: "研究实施",
    value: "RESEARCH_IMPLEMENTED"
  },
  {
    name: "评估总结",
    value: "EVALUATION_COMPLETED"
  },
  {
    name: "结束",
    value: "FINISHED"
  }
];

export const projectStatusMap = getMapData(projectStatusOptions);

export const projectSourceOptions = [
  {
    name: "政府项目",
    value: "GOVERNMENT"
  },
  {
    name: "企业项目",
    value: "ENTERPRISE"
  },
  {
    name: "高校院所",
    value: "COLLEGE"
  },
  {
    name: "其他",
    value: "OTHER"
  }
];
export const projectSourceMap = getMapData(projectSourceOptions);

export const governmentLevelOptions = [
  {
    name: "国家级",
    value: "COUNTRY"
  },
  {
    name: "省部级",
    value: "PROVINCE"
  },
  {
    name: "市厅级",
    value: "CITY"
  },
  {
    name: "高校级",
    value: "COLLEGE"
  }
];
export const governmentLevelOptionsMap = getMapData(governmentLevelOptions);

export const projectTypeOptions = [
  {
    name: "企业自研",
    value: "ISR"
  },
  {
    name: "委托研发",
    value: "CRD"
  }
];

export const projectTypeMap = getMapData(projectTypeOptions);

export const projectResearchTypeOptions = [
  {
    name: "随机对照研究/RCT",
    value: "RCT"
  },
  {
    name: "非随机对照研究",
    value: "NRCT"
  },
  {
    name: "描述性研究",
    value: "DS"
  },
  {
    name: "分析性研究",
    value: "AS"
  }
];
export const projectResearchTypeMap = getMapData(projectResearchTypeOptions);

// 伦理
export const ethicsStatusOptions = [
  {
    name: "未提交",
    value: "UN_COMMITTED"
  },
  {
    name: "审核中",
    value: "PENDING"
  },
  {
    name: "已通过",
    value: "APPROVED"
  },
  {
    name: "拒绝参与",
    value: "REJECTED"
  },
  {
    name: "无需提交",
    value: "NO_NEEDED"
  }
];
export const ethicsStatusMap = getMapData(ethicsStatusOptions);

// 对照方式
export const compareMethodTypeOptions = [
  {
    name: "平行对照",
    value: "PC"
  },
  {
    name: "交叉对照",
    value: "CC"
  }
];

export const compareMethodTypeOptionsMap = getMapData(compareMethodTypeOptions);

// 盲法
export const blindMethodTypeOptions = [
  {
    name: "开放",
    value: "OPEN_LABEL"
  },
  {
    name: "单盲",
    value: "SINGLE_BLIND"
  },
  {
    name: "双盲",
    value: "DOUBLE_BLIND"
  }
];
export const blindMethodTypeOptionsMap = getMapData(blindMethodTypeOptions);

export const enrollmentMethodOptions = [
  { name: "竞争入组", value: "CE" },
  {
    name: "分配指标入组",
    value: "AIE"
  }
];
export const enrollmentMethodOptionsMap = getMapData(enrollmentMethodOptions);

export const enrollmentMomentTypeOptions = [
  {
    name: "直接入组",
    value: "DE"
  },
  {
    name: "审核后入组",
    value: "AE"
  }
];

export const enrollmentMomentTypeOptionsMap = getMapData(
  enrollmentMomentTypeOptions
);

// 受试者来源
export const participantSourceOptions = [
  {
    name: "科研入组",
    value: "RE"
  },
  {
    name: "业务入组",
    value: "BE"
  },
  {
    name: "其他科研入组",
    value: "ORE"
  }
];

export const participantSourceOptionsMap = getMapData(participantSourceOptions);

// 受试者具体来源

export const participantOreProjectTypeOptions = [
  {
    name: "当前项目",
    value: "CURRENT_PROJECT"
  },
  {
    name: "历史项目",
    value: "ALL_PROJECT"
  },
  {
    name: "其它项目",
    value: "OTHER_PROJECT"
  }
];
export const participantOreProjectTypeOptionsMap = getMapData(
  participantOreProjectTypeOptions
);

// 补偿方式
export const compensationMethodTypesOptions = [
  {
    name: "数字医疗补偿",
    value: "DMC"
  },
  {
    name: "硬件补偿",
    value: "HC"
  },
  {
    name: "货币补偿",
    value: "MC"
  },
  {
    name: "其他补偿",
    value: "OC"
  },
  {
    name: "无",
    value: "NC"
  }
];
export const compensationMethodTypesOptionsMap = getMapData(
  compensationMethodTypesOptions
);

// 硬件补偿方式
export const CompensationHardwareMethodOptions = [
  {
    name: "血压计",
    value: "BP"
  },
  {
    name: "智能手表",
    value: "SW"
  },
  {
    name: "体重秤",
    value: "WS"
  }
];
export const CompensationHardwareMethodOptionsMap = getMapData(
  CompensationHardwareMethodOptions
);

export const groupTypeOptions = [
  {
    name: "干预组",
    value: "INTERVENTION_GROUP"
  },
  {
    name: "对照组",
    value: "CONTROL_GROUP"
  }
];
export const groupTypeOptionsMap = getMapData(groupTypeOptions);

export const subCenterStatusOptions = [
  {
    name: "尚未开始",
    value: "CREATED"
  },
  {
    name: "正式启动",
    value: "STARTED"
  },
  {
    name: "正在进行",
    value: "ONGOING"
  },
  {
    name: "暂停",
    value: "PAUSED"
  },
  {
    name: "结束",
    value: "FINISHED"
  }
];
export const subCenterStatusOptionsMap = getMapData(subCenterStatusOptions);

export const CRFRecordTypeOptions = [
  {
    name: "基线记录",
    value: "BASELINE"
  },
  {
    name: "访视记录",
    value: "VISIT"
  },
  {
    name: "终点记录",
    value: "ENDPOINT"
  }
];
export const CRFRecordTypeOptionsMap = getMapData(CRFRecordTypeOptions);

export const CRFTimeLineTypeOptions = [
  {
    name: "入组时间",
    value: "ENROLLMENT_TIME"
  },
  {
    name: "出院时间",
    value: "DISCHARGE_TIME"
  }
];

export const CRFTimeLineTypeOptionsMap = getMapData(CRFTimeLineTypeOptions);

export const CRFUnitTypeOptions = [
  {
    name: "知情同意书",
    value: "AGREE_FILE"
  },
  {
    name: "入院记录",
    value: "ADMISSION_REPORT"
  },
  {
    name: "手术记录",
    value: "SURGERY"
  },
  {
    name: "用药记录",
    value: "DRUG_INFO"
  },
  {
    name: "出院记录",
    value: "DISCHARGE_REPORT"
  },
  {
    name: "失访事件",
    value: "LTFU_EVENT"
  },
  {
    name: "终点事件",
    value: "ENDPOINT_EVENT"
  },
  {
    name: "退出事件",
    value: "WITHDRAWAL_EVENT"
  },
  {
    name: "临床事件",
    value: "CLINICAL_EVENT"
  },
  {
    name: "医疗随访量表",
    value: "SYMPTOM_FOLLOW_UP"
  },
  {
    name: "一般情况调查量表",
    value: "LIFESTYLE_NORMAL"
  },
  {
    name: "焦虑评估调查问卷",
    value: "LIFESTYLE_GAD_7"
  },
  {
    name: "抑郁评估调查问卷",
    value: "LIFESTYLE_DEPRESSED"
  },
  {
    name: "睡眠质量调查问卷",
    value: "LIFESTYLE_SLEEP"
  },
  {
    name: "生活质量调查问卷",
    value: "LIFESTYLE_LIFE"
  },
  {
    name: "焦虑抑郁合并筛查量表",
    value: "LIFESTYLE_ANXIETY_DEPRESSED"
  },
  {
    name: "堪萨斯城心肌病问卷-KCCQ",
    value: "LIFESTYLE_CARDIOMYOPATHY"
  },
  {
    name: "血压",
    value: "BLOOD_PRESSURE"
  },
  {
    name: "血糖",
    value: "BLOOD_SUGAR"
  },
  {
    name: "心率",
    value: "HEART_RATE"
  },
  {
    name: "出入量",
    value: "INTAKE_OUTPUT"
  },
  {
    name: "体重",
    value: "WEIGHT"
  },
  {
    name: "肝功能",
    value: "LIVER_FUNCTION_TEST"
  },
  {
    name: "电解质",
    value: "ELECTROLYTES"
  },
  {
    name: "甲功五项",
    value: "THYROID_FUNCTION_PANEL"
  },
  {
    name: "肾功能",
    value: "RENAL_FUNCTION_TEST"
  },
  {
    name: "血脂全套",
    value: "COMPLETE_LIPID_PROFILE"
  },
  {
    name: "OGTT",
    value: "OGTT"
  },
  {
    name: "全血细胞分析",
    value: "COMPLETE_BLOOD_COUNT_CBC"
  },
  {
    name: "尿常规+尿沉渣镜检",
    value: "URINALYSIS_WITH_MICROSCOPIC_EXAMINATION"
  },
  {
    name: "快速血气",
    value: "RAPID_BLOOD_GAS_ANALYSIS"
  },
  {
    name: "炎症指标",
    value: "INFLAMMATORY_MARKERS"
  },
  {
    name: "铁五项",
    value: "IRON_PANEL"
  },
  {
    name: "尿微量蛋白",
    value: "MICROALBUMINURIA_TEST"
  },
  {
    name: "白介素",
    value: "INTERLEUKINS"
  },
  {
    name: "骨代谢",
    value: "BONE_METABOLISM_TEST"
  },
  {
    name: "凝血检查",
    value: "COAGULATION_TEST"
  },
  {
    name: "粪便常规",
    value: "STOOL_ROUTINE_EXAMINATION"
  },
  {
    name: "尿蛋白定量",
    value: "QUANTITATIVE_URINE_PROTEIN_TEST"
  },
  {
    name: "血栓五项",
    value: "COAGULATION_PANEL"
  },
  {
    name: "尿乳糜试验",
    value: "URINARY_LIPID_TEST"
  },
  {
    name: "尿蛋白-尿微量白蛋白24h",
    value: "PROTEINURIA_MICROALBUMINURIA_24_HOUR_TEST"
  },
  {
    name: "肿瘤相关抗原",
    value: "TUMOR_MARKERS"
  },
  {
    name: "心肌酶谱",
    value: "CARDIAC_ENZYME_PROFILE"
  },
  {
    name: "12导联心电图",
    value: "ECG_12"
  },
  {
    name: "心脏彩超",
    value: "ECHOCARDIOGRAM"
  },
  {
    name: "动态心电图",
    value: "ECG_DYNAMIC"
  }
];
export const CRFUnitTypeOptionsMap = getMapData(CRFUnitTypeOptions);

export const CRFUnitTypeSecondOptionsMap = {
  ADMISSION_RECORD: ["知情同意书", "入院记录", "手术记录", "出院记录"],
  EXAMINATION_REPORT: ["沿用现有复查检查项"],
  SYMPTOM_FOLLOW_UP: [
    "一般情况调查量表",
    "焦虑抑郁合并筛查量表",
    "匹兹堡睡眠质量指数-PSQI",
    "生活质量问卷SF36",
    "冠心病症状随访问卷",
    "心衰症状随访问卷"
  ],
  DRUG_INFO: ["用药记录"]
};

export const randomMethodTypeOptions = [
  {
    name: "不随机",
    value: "NR"
  },
  {
    name: "分层随机",
    value: "SR"
  },
  {
    name: "区组随机",
    value: "BR"
  },
  {
    name: "分层区组随机",
    value: "SBR"
  },
  {
    name: "动态随机/适应性随机",
    value: "DR"
  }
];

export const randomMethodTypeOptionsMap = getMapData(randomMethodTypeOptions);

// 分层因素枚举
export const randomLevelFactorOptions = [
  {
    name: "分中心",
    value: "PROJECT_BRANCH_CENTER_ALL"
  },
  {
    name: "性别",
    value: "PATIENT_GENDER"
  },
  {
    name: "年龄",
    value: "PATIENT_AGE"
  },
  {
    name: "SCAI分期",
    value: "PATIENT_SCAI"
  }
];

export const randomLevelInfo = [
  {
    name: "项目全部分中心",
    value: "PROJECT_BRANCH_CENTER_ALL"
  },
  {
    name: "男;女",
    value: "PATIENT_GENDER"
  },
  {
    name: "SCAI A;SCAI B;SCAI C",
    value: "PATIENT_SCAI"
  }
];
export const randomLevelFactorOptionsMap = getMapData(randomLevelFactorOptions);
export const randomLevelInfoOptionsMap = getMapData(randomLevelInfo);
// 服务包枚举
export const groupProductionOptions = [
  {
    name: "心力衰竭院外管理科研包",
    value: "HEART_FAILURE"
  },
  {
    name: "冠心病院外管理科研包",
    value: "CORONARY_HEART"
  },
  {
    name: "心血管病院外管理科研包",
    value: "CARDIOVASCULAR"
  },
  {
    name: "肺血管病院外管理科研包",
    value: "PULMONARY_ARTERY"
  }
];
export const groupProductionOptionsMap = getMapData(groupProductionOptions);
