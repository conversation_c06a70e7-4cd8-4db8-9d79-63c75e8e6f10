import { getMapData } from "../../../util/util";
export const applicablePortOptions = [
  {
    name: '系统后台',
    value: "SYSTEM_BACKEND"
  },
  {
    name: '数据终端',
    value: "DATA_TERMINAL"
  },
  {
    name: '医生工作台',
    value: "DOCTOR_WORK_WORKBENCH"
  },
  {
    name: '员工工作台',
    value: "EMPLOYEE_WORK_WORKBENCH"
  },
  {
    name: '哈瑞特工作室',
    value: "HRT_WORK_GROUP"
  },
  {
    name: '数据统计平台',
    value: "DATA_STATISTICS_PLATFORM"
  }
]

export const applicablePortMap = getMapData(applicablePortOptions)

export const roleTypeOptions = [
  {
    name: '自定义',
    value: "CUSTOMIZE"
  },
  {
    name: '系统',
    value: "SYSTEM"
  }
]

export const roleTypeMap = getMapData(roleTypeOptions)

