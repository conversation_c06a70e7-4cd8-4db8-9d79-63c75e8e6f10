<template>
  <CommonDialog
    @cancel="cancel"
    @confirm="confirm"
    :visible.sync="visible"
    :title="title"
    width="1200px"
  >
    <div class="add-role-wrapper">
      <el-form :model="staffForm" :rules="rules" ref="staffFormRef" class="rule-from-style">
        <div class="form-group">
          <div class="group-head">
            <div>基本信息</div>
          </div>
          <div class="sub-form">
            <el-form-item label="角色类型" prop="type">
              <el-select v-model="staffForm.type" placeholder="请选择" class="select-box">
                <el-option
                  :label="item.name"
                  :value="item.value"
                  v-for="item in roleTypeOptions"
                  :key="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="角色名称" prop="name">
              <el-input v-model="staffForm.name" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="适用端口" prop="applicablePort">
              <el-select v-model="staffForm.applicablePort" placeholder="请选择" class="select-box">
                <el-option
                  :label="item.name"
                  :value="item.value"
                  v-for="item in applicablePortOptions"
                  :key="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="备注" prop="remarks" class="large-form-item">
              <el-input
                v-model="staffForm.remarks"
                placeholder="请输入"
                class="large-size-input"
                type="textarea"
                :rows="3"
                maxlength="500"
                show-word-limit
              ></el-input>
            </el-form-item>
          </div>
        </div>
      </el-form>
      <div class="permission-content">
        <div class="group-head">
          <div>角色权限</div>
        </div>
        <treeTable :menu="menu" :disabled="disabled" />
      </div>
    </div>
  </CommonDialog>
</template>

<script>
import CommonDialog from '../../../../components/CommonDialog.vue'
import treeTable from './treeTable.vue'
import { applicablePortOptions, roleTypeOptions } from '../../constant/rolesEnum'
import {
  createRole,
  getPermissionList,
  getCurrentRolePermissionDetails,
  updateRole,
} from '../../../../api/system'
export default {
  name: 'AddRoleDialog',
  components: { CommonDialog, treeTable },
  props: {
    visible: {
      require: true,
      type: Boolean,
    },
    title: {
      require: true,
      type: String,
    },
    roleId: {
      require: true,
    },
  },
  data() {
    return {
      applicablePortOptions: applicablePortOptions.filter((v) => v.value === 'SYSTEM_BACKEND'),
      roleTypeOptions: roleTypeOptions.filter((v) => v.value === 'CUSTOMIZE'),
      staffForm: {
        type: 'CUSTOMIZE',
        name: '',
        applicablePort: 'SYSTEM_BACKEND',
        remarks: '',
        permissionIds: [],
      },
      rules: {
        type: [{ required: true, message: '请选择角色类型', trigger: 'change' }],
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        applicablePort: [{ required: true, message: '请选择端口', trigger: 'change' }],
        remarks: [{ required: false, message: '请输入备注', trigger: 'blur' }],
      },
      menu: {
        name: '',
        id: 0,
        showRow: true,
        indeterminate: false,
        checked: false,
        isRoot: true,
        children: [],
        disabled: false,
      },
      disabled: false,
      selectedItems: [],
    }
  },
  created() {
    if (this.roleId) {
      this.getCurrentRolePermission(this.roleId)
    } else {
      this.getDepPermissionList()
    }
  },
  methods: {
    cancel() {
      this.$emit('update:visible', false)
    },
    confirm() {
      this.$refs['staffFormRef'].validate((valid) => {
        if (valid) {
          this.confirmCurrentRoles()
        } else {
          this.$message.warning('请填写完整!')
        }
      })
    },
    //获取权限列表
    getDepPermissionList() {
      getPermissionList({ status: 0 })
        .then((res) => {
          if (res.data.length === 0) {
            this.$message.warning(`该部门暂无权限列表划分!`)
            this.menu.children = []
          } else {
            this.menu.children = res.data[0].children.map((item) => {
              console.log(item)
              // 屏蔽员工绩效
              const list = (item.children || []).filter((route) => route.name !== '员工绩效')
              return {
                ...item,
                children: list,
              }
            })
            this.menu.name = res.data[0].name
            this.menu.id = res.data[0].id
            this.addCheckedForNode(this.menu.children)
          }
          if (this.roleId) {
            this.addCheckedForNode(this.menu.children)
          }
        })
        .catch((err) => {
          this.$message.error(`获取权限列表失败!`)
        })
    },
    //遍历数据根据选中id作为回显
    addCheckedForNode(list) {
      list.forEach((item) => {
        if (item.children && item.children.length) {
          this.addCheckedForNode(item.children)
        } else {
          if (this.isCheckedById(item.id)) {
            this.$set(item, 'checked', true)
          }
        }
      })
    },
    //判断是否该项是否为选中项+标识符作为回显
    isCheckedById(id) {
      let ids = this.selectedItems.map((item) => item)
      return ids.includes(id)
    },
    //处理选中项
    getSelectedItems(list) {
      list.forEach((item) => {
        if (item.children && item.children.length) {
          this.getSelectedItems(item.children)
        } else {
          if (item.checked) {
            this.selectedItems.push(item.id)
          }
        }
      })
    },
    confirmCurrentRoles() {
      this.selectedItems = []
      this.getSelectedItems(this.menu.children)
      this.staffForm.permissionIds = this.selectedItems
      if (this.roleId) {
        //新增
        this.changeRoleRequest()
      } else {
        // 编辑
        this.addRoleRequest()
      }
    },
    //新增角色
    addRoleRequest() {
      createRole(this.staffForm)
        .then((res) => {
          if (res.code === 1) {
            this.$message.success(`角色创建成功!`)
            this.$emit('confirmSuccess')
            this.$emit('update:visible', false)
          } else {
            this.$message.error(`角色添加失败!:${res.msg}`)
          }
        })
        .catch((err) => {
          this.$message.error(`角色添加失败!:${err.msg}`)
        })
    },
    //编辑角色
    changeRoleRequest() {
      let params = {
        ...this.staffForm,
        roleId: this.roleId,
      }
      updateRole(params)
        .then((res) => {
          if (res.code === 1) {
            this.$message.success(`角色修改成功!`)
            this.$emit('confirmSuccess')
            this.$emit('update:visible', false)
          } else {
            this.$message.error(`角色修改失败!:${res.msg}`)
          }
        })
        .catch((err) => {
          this.$message.error(`角色修改失败!:${err.msg}`)
        })
    },
    //获取当前角色权限详情
    getCurrentRolePermission(roleId) {
      getCurrentRolePermissionDetails({ roleId })
        .then((res) => {
          this.staffForm.type = res.data.roleResponseDTO.type
          this.staffForm.applicablePort = res.data.roleResponseDTO.applicablePort
          this.staffForm.remarks = res.data.roleResponseDTO.remarks
          this.staffForm.name = res.data.roleResponseDTO.name
          this.selectedItems = res.data.permissionIds
          this.getDepPermissionList(res.data.status)
        })
        .catch((err) => {
          this.$message.error(`当前角色权限详情获取错误!:${err.msg}`)
        })
    },
  },
}
</script>

<style scoped lang="less">
.add-role-wrapper {
  width: 100%;
  box-sizing: border-box;
  padding: 24px 84px;
  text-align: left;
  color: #15233f;
  overflow-y: auto;
}
/deep/.el-form.rule-from-style {
  .form-group {
    .sub-form {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 24px;
      .el-form-item {
        flex: 1;
        margin-bottom: 8px;
        .el-form-item__label {
          color: #3a4762;
          padding: 0;
          line-height: 32px;
          font-weight: bold;
        }
        .el-form-item__content {
          width: 240px;
          line-height: 32px;
          border-radius: 4px;
          .el-input__inner {
            width: 240px;
            height: 32px;
            line-height: 32px;
            font-size: 14px;
            border-radius: 2px;
            &::placeholder {
              color: #909399;
            }
          }
          .custom-content {
            width: 240px;
            height: 32px;
            line-height: 32px;
            font-size: 14px;
            display: inline-block;
            font-weight: bold;
          }
          .large-size-input {
            .el-input__inner {
              width: 480px;
              height: 32px;
              line-height: 32px;
              font-size: 14px;
              border-radius: 2px;
              &::placeholder {
                color: #909399;
              }
            }
          }
          .select-box .el-icon-arrow-up:before {
            content: '';
          }
          .select-box .el-icon-arrow-down:before {
            content: '\e790';
          }
          .el-select .el-input .el-input__icon {
            line-height: 32px;
          }
          .el-radio-group {
            .el-radio__input.is-checked .el-radio__inner {
              border-color: #2e6be6;
              background: #2e6be6;
            }
            .el-radio__input.is-checked + .el-radio__label {
              color: #2e6be6;
            }
          }
          .el-checkbox-group {
            width: 240px;
            display: flex;
            flex-wrap: wrap;
            .el-checkbox {
              flex: 1;
              margin-right: 0;
            }
          }
        }
      }
      .large-form-item {
        .el-form-item__content {
          width: 480px;
        }
        .el-textarea__inner {
          border-radius: 2px;
          font-family:
            PingFangSC-Regular,
            PingFang SC;
        }
      }
    }
  }
}
.group-head {
  font-weight: bold;
  font-size: 16px;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  span {
    display: block;
    width: 4px;
    height: 16px;
    background: #2e6be6;
    border-radius: 1px;
    margin-right: 8px;
  }
}
</style>
