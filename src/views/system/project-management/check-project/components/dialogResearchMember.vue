<template>
  <el-dialog
    :visible.sync="visible"
    @close="close"
    width="666px"
    :before-close="beforeClose"
  >
    <div class="header" slot="title">
      项目信息-{{ isEdit ? "编辑" : "查看" }}
      <span class="finished" v-if="queryData.projectStatus === 'FINISHED'"
        >项目处于结束状态，不可编辑</span
      >
      <el-button
        v-if="!isEdit"
        type="primary"
        @click="changeEdit"
        :disabled="queryData.projectStatus === 'FINISHED'"
      >
        编辑
      </el-button>
    </div>
    <!-- <h3>基础信息</h3> -->
    <memberConfigCompsVue
      :resolvedFormData="queryData"
      :memberEditDisabled="!isEdit"
      ref="memberConfigRef"
    />
    <h3>分中心研究人员</h3>
    <el-table :data="subCenterList" max-height="240" min-height="200" border>
      <el-table-column label="分中心名称" prop="branchId" min-width="180">
        <template #default="{ row }">
          {{ options.hospitalIdDict[row.branchHospitalId] }}
        </template>
      </el-table-column>
      <el-table-column label="CRC" prop="crcIds" min-width="150">
        <template #default="{ row }">
          {{ getTypedIds("CRC", row.crcIds) }}
        </template>
      </el-table-column>
      <el-table-column label="CRA" prop="craIds" min-width="150">
        <template #default="{ row }">
          {{ getTypedIds("CRA", row.craIds) }}
        </template>
      </el-table-column>
      <el-table-column label="DM" prop="dmIds" min-width="150">
        <template #default="{ row }">
          {{ getTypedIds("DM", row.dmIds) }}
        </template>
      </el-table-column>
    </el-table>
    <paginationComps
      :currentPage.sync="currentPage"
      :total.sync="total"
      :pageSize.sync="pageSize"
      align="center"
      @currentChange="getSubCenterList"
      @sizeChange="getSubCenterList"
    />
    <div slot="footer" v-if="isEdit">
      <el-button @click="beforeClose">取消</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
  </el-dialog>
</template>
<script>
import paginationComps from "../../components/paginationComps.vue";
import memberConfigCompsVue from "../../add-new-project/components/memberConfigComps.vue";
import { getSubCenterList } from "@/api/system";
export default {
  name: "",
  components: {
    memberConfigCompsVue,
    paginationComps
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    queryData: {
      type: Object,
      default: () => ({})
    },
    getRoleInfo: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      isEdit: false,
      timer: null,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      subCenterList: [],
      options: {
        hospitalIdDict: sessionStorage.getItem("dictData-hospital-list")
          ? JSON.parse(sessionStorage.getItem("dictData-hospital-list"))
          : {}
      }
    };
  },
  computed: {
    getTypedIds() {
      return (type, ids) =>
        (ids || []).map(it => this.getRoleInfo(type, it).memberName).join("、");
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getSubCenterList();
      }
    }
  },
  inject: ["initData"],
  mounted() {},
  methods: {
    async submit() {
      if (!this.$refs.memberConfigRef) return;
      await this.$refs.memberConfigRef.submitForm();
      this.initData();
      this.close();
    },
    async getSubCenterList() {
      try {
        const res = await getSubCenterList({
          projectId: this.queryData.projectId,
          pageNum: this.pageNum,
          pageSize: this.pageSize
        });
        if (res.code === 1) {
          this.subCenterList = res.data.contents;
          this.total = res.data.total;
          console.log(`output->this.subCenterList`, this.subCenterList);
        } else {
          throw new Error(res);
        }
      } catch (error) {
        this.$message.error("获取分中心研究人员列表失败：" + error.msg);
        console.log(error);
      }
    },
    changeEdit() {
      // 模拟查看/编辑弹窗切换
      this.close();
      this.timer = setTimeout(() => {
        this.isEdit = !this.isEdit;
        this.$emit("update:visible", true);
      }, 200);
    },
    beforeClose(done) {
      if (this.isEdit) {
        this.$confirm("当前编辑内容未保存，是否退出？")
          .then(() => {
            this.isEdit = false;
            this.close();
          })
          .catch(_ => {});
      } else {
        this.close();
      }
    },
    close() {
      this.$emit("update:visible", false);
    }
  },
  beforeDestroy() {
    if (this.timer) clearTimeout(this.timer);
  }
};
</script>
<style lang="less" scoped>
@import "../../common.less";

.header {
  display: flex;
  justify-content: space-between;

  .el-button {
    margin-right: 40px;
  }
}
</style>
