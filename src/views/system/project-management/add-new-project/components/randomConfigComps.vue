<template>
  <div class="random-config-container">
    <el-form
      ref="randomRef"
      :model="projectRandomConfig"
      :rules="rules"
      inline
      label-position="left"
      label-width="140px"
    >
      <el-form-item label="随机化方法：" prop="randomMethodType">
        <el-select
          v-model="projectRandomConfig.randomMethodType"
          placeholder="请选择"
          @change="handleChange"
        >
          <el-option
            v-for="(item, index) in options.randomMethodTypeOptions"
            :key="index"
            :label="item.name"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="
          projectRandomConfig.randomMethodType === 'BR' ||
            projectRandomConfig.randomMethodType === 'SBR'
        "
        label="区组长度系数："
        prop="randomSetting.blockSetting.blockFactors"
      >
        <el-select
          v-model="projectRandomConfig.randomSetting.blockSetting.blockFactors"
          multiple
        >
          <el-option
            v-for="(item, index) in options.blockCapacityOptions"
            :key="index"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 系统根据区组长度系数和分组数自动计算 -->
      <el-form-item
        v-if="
          projectRandomConfig.randomMethodType === 'BR' ||
            projectRandomConfig.randomMethodType === 'SBR'
        "
        label="区组长度："
      >
        <el-input
          v-model="projectRandomConfig.randomSetting.blockSetting.blockLengths"
          disabled
          placeholder="系统根据区组长度系数和分组数自动计算"
        ></el-input>
      </el-form-item>
    </el-form>
    <el-table
      v-if="
        projectRandomConfig.randomMethodType === 'SR' ||
          projectRandomConfig.randomMethodType === 'SBR' ||
          projectRandomConfig.randomMethodType === 'DR'
      "
      :data="projectRandomConfig.stratificationComponents"
      border
    >
      <el-table-column label="分层因素" min-width="120px">
        <template #default="scope">
          <el-select
            v-model="projectRandomConfig.stratificationComponents[scope.$index]"
          >
            <el-option
              v-for="(item, index) in options.randomLevelFactorOptions"
              :label="item.name"
              :value="item.value"
              :key="index"
              :disabled="
                projectRandomConfig.stratificationComponents.includes(
                  item.value
                )
              "
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="亚组(多个亚组请用英文;分割)" min-width="420px">
        <template #default="{ row }">
          <div tabindex="0">
            {{ options.randomLevelInfoOptionsMap[row] }}
            <template v-if="row === 'PATIENT_AGE'">
              <div>
                <span
                  v-for="(item, index) in agesToAgeGroupsShow(
                    projectRandomConfig.ageGroups
                  )"
                  :key="index"
                >
                  <span v-if="index !== 0"> {{ item.minAge }}&lt; </span>
                  年龄≤{{ item.maxAge }};
                  <span
                    v-if="
                      index !== 0 &&
                        index ===
                          agesToAgeGroupsShow(projectRandomConfig.ageGroups)
                            .length -
                            1
                    "
                  >
                    年龄>{{ item.maxAge }}
                  </span>
                </span>
              </div>
              <el-input
                v-model="projectRandomConfig.ageGroups"
                placeholder="示例：25;50（亚组1：:≤25岁；亚组2：26~50岁；亚组3：＞50岁）"
                style="margin-top: 12px"
              ></el-input>
            </template>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button type="text" @click="handleDel(scope.$index)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="action text-left" style="margin-top: 20px">
      <el-button
        v-if="
          projectRandomConfig.randomMethodType === 'SR' ||
            projectRandomConfig.randomMethodType === 'SBR' ||
            projectRandomConfig.randomMethodType === 'DR'
        "
        type="text"
        icon="el-icon-plus"
        @click="handleAdd"
        >添加分层</el-button
      >
      <el-button type="primary" icon="el-icon-check" @click="handleSave"
        >保存</el-button
      >
    </div>
  </div>
</template>
<script>
import {
  randomMethodTypeOptions,
  randomLevelFactorOptions,
  randomLevelInfoOptionsMap
} from "../../../constant/projectEnum";

import { resolvedFormDataProp } from "../../mixins";

import { createOrEditRandomConfig } from "@/api/system";

export default {
  name: "RandomConfigComps",
  components: {},
  mixins: [resolvedFormDataProp],
  props: {},
  data() {
    return {
      projectRandomConfig: {
        randomMethodType: "",
        randomSetting: {
          blockSetting: {
            blockFactors: [], // 区组长度
            blockLengths: [] // 区组长度系数
          }
        },
        stratificationComponents: [], // 分层因素
        ageGroups: ""
      },
      options: {
        randomMethodTypeOptions,
        randomLevelFactorOptions,
        randomLevelInfoOptionsMap,
        blockCapacityOptions: [
          {
            value: 1
          },
          {
            value: 2
          },
          {
            value: 3
          },
          {
            value: 4
          }
        ]
      },
      rules: {
        randomMethodType: [
          { required: true, trigger: "blur", message: "请选择随机化方法" }
        ],
        "randomSetting.blockSetting.blockFactors": [
          { required: true, trigger: "blur", message: "请选择区组长度系数" }
        ]
      }
    };
  },
  computed: {},
  watch: {
    resolvedData() {
      this.updateData();
    },
    "projectRandomConfig.randomSetting.blockSetting.blockFactors": {
      handler: function(val) {
        if (val) this.blockCapacityChange(val);
      },
      immediate: true
    }
  },
  mounted() {
    this.updateData();
  },
  methods: {
    async handleSave() {
      console.log(`output->randomConfig`, this.projectRandomConfig);
      await this.$refs.randomRef.validate();
      try {
        // 校验
        if (
          "SR,SBR,DR"
            .split(",")
            .includes(this.projectRandomConfig.randomMethodType)
        ) {
          // 是否添加分层因素
          if (this.projectRandomConfig.stratificationComponents.length === 0)
            return this.$message.warning("请添加分层因素");
          // 年龄是否输入
          if (
            this.projectRandomConfig.stratificationComponents.includes(
              "PATIENT_AGE"
            ) &&
            !this.projectRandomConfig.ageGroups
          )
            return this.$message.warning("请输入年龄亚组，并以英文 ; 分隔");
        }
        console.log(
          `output->this.projectRandomConfig.ageGroups`,
          this.projectRandomConfig.ageGroups
        );
        const ageGroups = this.projectRandomConfig.ageGroups
          ? this.agesToAgeGroups(this.projectRandomConfig.ageGroups)
          : null;
        const res = await createOrEditRandomConfig({
          projectId: this.resolvedData.projectId,
          ...this.projectRandomConfig,
          ageGroups: this.projectRandomConfig.stratificationComponents.includes(
            "PATIENT_AGE"
          )
            ? ageGroups
            : []
        });
        if (res.code === 1) {
          this.$message.success("随机配置保存成功");
          this.updateResolvedFormData();
        } else {
          throw new Error(res);
        }
      } catch (error) {
        console.log(`output->error`, error);
        this.$message.error(`保存失败：` + error.msg);
      }
    },
    agesToAgeGroups(ages) {
      const t = ages.split(";").filter(it => it);
      if (!t.includes(110)) {
        t.push(110);
      }
      return t
        .sort((a, b) => a - b)
        .reduce((pre, cur, idx) => {
          pre.push({
            groupName: `亚组${idx}`,
            minAge: pre.slice(-1).length ? pre.slice(-1)[0].maxAge : 0,
            maxAge: Number(cur)
          });
          return pre;
        }, []);
    },
    agesToAgeGroupsShow(ages) {
      const t = ages.split(";").filter(it => it);
      if (t.includes(110)) {
        t.splice(t.indexOf(110), 1);
      }
      return t
        .sort((a, b) => a - b)
        .reduce((pre, cur, idx) => {
          pre.push({
            groupName: `亚组${idx}`,
            minAge: pre.slice(-1).length ? pre.slice(-1)[0].maxAge : 0,
            maxAge: Number(cur)
          });
          return pre;
        }, []);
    },
    handleAdd() {
      if (
        this.projectRandomConfig.stratificationComponents.length >=
        this.options.randomLevelFactorOptions.length
      )
        return;
      this.projectRandomConfig.stratificationComponents = [
        ...this.projectRandomConfig.stratificationComponents,
        null
      ];
    },
    handleDel(index) {
      if (this.projectRandomConfig.stratificationComponents.length === 0)
        return;
      this.projectRandomConfig.stratificationComponents.splice(index, 1);
    },
    blockCapacityChange(val) {
      if (!this.resolvedData.projectGroups) return;
      const proportions = this.resolvedData.projectGroups.map(
        it => it.groupProportion
      );
      const gcd = this.calculateGCD(proportions);
      const num = proportions
        .map(it => Math.floor(it / gcd))
        .reduce((pre, cur) => (pre += cur));
      this.projectRandomConfig.randomSetting.blockSetting.blockLengths = val.map(
        it => it * num
      );
    },
    calculateGCD(numbers) {
      const gcdOfTwo = (a, b) => {
        while (b) {
          const temp = b;
          b = a % b;
          a = temp;
        }
        return a;
      };

      // Calculate GCD of all numbers in array
      return numbers.reduce((gcd, num) => gcdOfTwo(gcd, num));
    },
    handleChange() {
      this.$emit("update:random", this.random);
    },
    updateData() {
      if (this.resolvedData.projectRandomConfig) {
        this.projectRandomConfig = JSON.parse(
          JSON.stringify(this.resolvedData.projectRandomConfig)
        );
        let ageGroups = this.projectRandomConfig.stratificationFactors.filter(
          it => it.key === "PATIENT_AGE"
        );
        ageGroups = ageGroups.length ? ageGroups.slice(-1)[0].ageGroups : [];
        console.log(`output->ageGroups`, ageGroups);
        this.projectRandomConfig = {
          ...this.projectRandomConfig,
          stratificationComponents: (
            this.projectRandomConfig.stratificationFactors || []
          ).map(it => it.value),
          ageGroups: ageGroups
            .map(it => it.maxAge)
            .filter(it => it != 110)
            .join(";")
        };
      }
    }
  }
};
</script>
<style lang="less" scoped>
@import "../../common.less";

.random-config-container {
  padding: 24px 12px;
}

.el-form {
  text-align: left;

  &-item {
    margin-right: 120px;

    .el-select {
      width: 240px;
    }

    .el-input {
      width: 240px;
    }

    &:first-child {
      display: block;
    }
  }
}
</style>
