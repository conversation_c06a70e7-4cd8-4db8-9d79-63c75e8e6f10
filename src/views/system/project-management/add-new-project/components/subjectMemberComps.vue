<template>
  <div class="subject-member-container">
    <div class="form-item">
      <span>知情同意书：</span>
      <UploadComps :fileUrl.sync="recruitForm.informedConsents" />
    </div>
    <div class="form-item">
      <span>开放患者自行入组：</span>
      <el-switch v-model="recruitForm.selfEnroll"></el-switch>
      <p v-if="recruitForm.selfEnroll" @click="() => (visible = true)">
        编辑招募信息
      </p>
    </div>
    <div class="text-left mb-16">
      <el-button type="primary" icon="el-icon-check" @click="submit"
        >保存病历筛选配置</el-button
      >
    </div>
    <div class="table-container">
      <div class="table-form">
        <div class="header">
          <div class="title">入组标准</div>
          <div class="opration">操作</div>
        </div>
        <div class="content">
          <div
            class="items"
            v-for="(item, index) in tableForms.SSC_IC"
            :key="index"
          >
            <el-input
              v-model="item.dictName"
              style="width: 612px"
              maxlength="100"
            ></el-input>
            <span class="del-btn" @click="handleDel('SSC_IC', index)"
              >删除</span
            >
          </div>
        </div>
        <div class="action-btn">
          <el-button
            type="text"
            icon="el-icon-circle-plus-outline"
            @click="handleAdd('SSC_IC')"
            >添加标准</el-button
          >
          <el-button
            type="text"
            icon="el-icon-circle-check"
            @click="handleSave('SSC_IC')"
            >保存标准</el-button
          >
        </div>
      </div>
      <div class="table-form">
        <div class="header">
          <div class="title">排除标准</div>
          <div class="opration">操作</div>
        </div>
        <div class="content">
          <div
            class="items"
            v-for="(item, index) in tableForms.SSC_EC"
            :key="index"
          >
            <el-input
              v-model="item.dictName"
              style="width: 612px"
              maxlength="100"
            ></el-input>
            <span class="del-btn" @click="handleDel('SSC_EC', index)"
              >删除</span
            >
          </div>
        </div>
        <div class="action-btn">
          <el-button
            type="text"
            icon="el-icon-circle-plus-outline"
            @click="handleAdd('SSC_EC')"
            >添加标准</el-button
          >
          <el-button
            type="text"
            icon="el-icon-circle-check"
            @click="handleSave('SSC_EC')"
            >保存标准</el-button
          >
        </div>
      </div>

      <div class="table-form">
        <div class="header">
          <div class="title">退出标准</div>
          <div class="exit-standard">退出类型</div>
          <div class="opration">操作</div>
        </div>
        <div class="content">
          <div
            class="items"
            v-for="(item, index) in tableForms.SSC_WC"
            :key="index"
          >
            <div class="sp">
              <el-input
                v-model="item.dictName"
                style="width: 612px"
                maxlength="100"
              ></el-input>
            </div>
            <el-select v-model="item.dictKey" style="margin-right: 18px">
              <el-option label="退出" value="SSC_WC_EXIT"></el-option>
              <el-option label="剔除" value="SSC_WC_REJECT"></el-option>
            </el-select>
            <span class="del-btn" @click="handleDel('SSC_WC', index)"
              >删除</span
            >
          </div>
        </div>
        <div class="action-btn">
          <el-button
            type="text"
            icon="el-icon-circle-plus-outline"
            @click="handleAdd('SSC_WC')"
            >添加标准</el-button
          >
          <el-button
            type="text"
            icon="el-icon-circle-check"
            @click="handleSave('SSC_WC')"
            >保存标准</el-button
          >
        </div>
      </div>

      <div class="table-form">
        <div class="header">
          <div class="title">失访标准</div>
          <div class="opration">操作</div>
        </div>
        <div class="content">
          <div
            class="items"
            v-for="(item, index) in tableForms.SSC_LFC"
            :key="index"
          >
            <el-input
              v-model="item.dictName"
              style="width: 612px"
              maxlength="100"
            ></el-input>
            <span class="del-btn" @click="handleDel('SSC_LFC', index)"
              >删除</span
            >
          </div>
        </div>
        <div class="action-btn">
          <el-button
            type="text"
            icon="el-icon-circle-plus-outline"
            @click="handleAdd('SSC_LFC')"
            >添加标准</el-button
          >
          <el-button
            type="text"
            icon="el-icon-circle-check"
            @click="handleSave('SSC_LFC')"
            >保存标准</el-button
          >
        </div>
      </div>

      <div class="table-form">
        <div class="header">
          <div class="title">达到终点标准</div>
          <div class="opration">操作</div>
        </div>
        <div class="content">
          <div
            class="items"
            v-for="(item, index) in tableForms.SSC_EAC"
            :key="index"
          >
            <el-input
              v-model="item.dictName"
              style="width: 612px"
              maxlength="100"
            ></el-input>
            <span class="del-btn" @click="handleDel('SSC_EAC', index)"
              >删除</span
            >
          </div>
        </div>
        <div class="action-btn">
          <el-button
            type="text"
            icon="el-icon-circle-plus-outline"
            @click="handleAdd('SSC_EAC')"
            >添加标准</el-button
          >
          <el-button
            type="text"
            icon="el-icon-circle-check"
            @click="handleSave('SSC_EAC', index)"
            >保存标准</el-button
          >
        </div>
      </div>
    </div>
    <EditRecruitComps
      :visible.sync="visible"
      title="编辑招募信息"
      :content.sync="recruitForm.caseEnrollDesc"
      @submit="handleSubmit"
    />
  </div>
</template>
<script>
import UploadComps from "../../components/uploadComps.vue";
import EditRecruitComps from "../../components/editRecruitComps.vue";
import { resolvedFormDataProp } from "../../mixins";
import { editProjectCaseFilterConfig } from "@/api/system";

import {
  createProjectDict,
  editProjectDict,
  deleteProjectDict
} from "@/api/system";

export default {
  name: "SubjectMemberComps",
  components: {
    UploadComps,
    EditRecruitComps
  },
  props: {},
  mixins: [resolvedFormDataProp],
  data() {
    return {
      recruitForm: {
        selfEnroll: false,
        caseEnrollDesc: "",
        informedConsents: []
      },
      visible: false,
      tableForms: {
        SSC_IC: [],
        SSC_EC: [],
        SSC_WC: [],
        SSC_LFC: [],
        SSC_EAC: []
      }
    };
  },
  computed: {},
  watch: {
    resolvedData() {
      this.tableForms = {
        SSC_IC: [],
        SSC_EC: [],
        SSC_WC: [],
        SSC_LFC: [],
        SSC_EAC: []
      };
      this.updateData();
    }
  },
  mounted() {
    this.updateData();
  },
  methods: {
    handleSubmit(html) {
      this.recruitForm.caseEnrollDesc = html;
    },
    async submit() {
      try {
        const requestBody = {
          ...this.recruitForm,
          projectId: this.resolvedData.projectId
        };
        const res = await editProjectCaseFilterConfig(requestBody);
        if (res.code === 1) {
          this.$message.success("保存成功");
        } else {
          throw new Error(res);
        }
      } catch (error) {
        console.log(error);
        this.$message.error(error.msg);
      } finally {
        this.updateResolvedFormData();
      }
    },
    updateData() {
      if (this.resolvedData && this.resolvedData.projectDictConfigs) {
        this.resolvedData.projectDictConfigs.forEach(item => {
          if (this.tableForms[item.dictType]) {
            this.tableForms[item.dictType].push({
              ...item
            });
          }
        });
      }
      const {
        selfEnroll,
        caseEnrollDesc,
        informedConsents
      } = this.resolvedData;
      this.recruitForm = {
        ...this.recruitForm,
        selfEnroll: !!selfEnroll,
        caseEnrollDesc,
        informedConsents
      };
    },
    handleSave(type) {
      const typedForm = this.tableForms[type];
      console.log(`output->typedForms`, typedForm);
      const addArr = typedForm
        .filter(it => !it.projectDictId)
        .filter(it => !!it.dictName);
      const editArr = typedForm.filter(it => it.projectDictId);

      if (addArr.length === 0 && editArr.length === 0) {
        return this.$message.warning("请先添加或编辑数据");
      }

      if (editArr.length > 0) {
        // 编辑
        const JSONArr = this.resolvedData.projectDictConfigs.map(it =>
          JSON.stringify(it)
        );
        const editedArr = editArr.filter(it => {
          const jsonStr = JSON.stringify(it);
          const resolvedStrIndex = this.resolvedData.projectDictConfigs.findIndex(
            _it => _it.projectDictId === it.projectDictId
          );
          return jsonStr !== JSONArr[resolvedStrIndex];
        });
        const editData = editedArr.map(it => ({
          ...it,
          projectId: this.resolvedData.projectId,
          id: it.projectDictId,
          dictType: type,
          dictName: it.dictName,
          dictKey: it.dictKey
        }));
        this.handleRequest("edit", editData);
      }
      if (addArr.length > 0) {
        // 新增
        const addData = addArr.map(it => ({
          projectId: this.resolvedData.projectId,
          dictType: type,
          dictName: it.dictName,
          dictKey: it.dictKey
        }));
        this.handleRequest("add", addData);
      }
    },
    async handleRequest(type, requestBody) {
      try {
        let res = null;
        switch (type) {
          case "add":
            const len = requestBody.length;
            let idx = 0;
            res = [];
            while (idx < len) {
              const resIndexed = await createProjectDict({
                ...requestBody[idx],
                projectId: this.resolvedData.projectId
              });
              res.push(resIndexed);
              idx++;
            }
            break;
          case "edit":
            res = await Promise.all(
              requestBody.map(it =>
                editProjectDict({
                  ...it,
                  projectId: this.resolvedData.projectId,
                  id: it.projectDictId
                })
              )
            );
            break;
        }
        if (Array.isArray(res)) {
          // 多条请求结果处理
          const successRes = res.filter(it => it.code === 1);
          if (successRes.length === res.length) {
            this.$message.success(
              (type === "edit" ? "编辑" : "新增") + successRes.length + "条成功"
            );
          } else {
            this.$message.warning(
              (type === "edit" ? "编辑" : "新增") +
                successRes.length +
                "条成功，" +
                (res.length - successRes.length) +
                "条失败"
            );
          }
        } else {
          if (res.code === 1) {
            this.$message.success("操作成功");
            console.log(`output->res`, res);
          } else {
            this.$message.error("操作异常");
          }
        }
        return res;
      } catch (error) {
        console.log(error);
        this.$message.error("操作失败");
      } finally {
        this.updateResolvedFormData();
      }
    },
    handleAdd(type) {
      console.log(type);
      this.tableForms[type].push({ dictName: "" });
      return;
      if (
        this.tableForms[type].filter(it => it.projectDictId).length ===
        this.tableForms[type].length
      ) {
        this.tableForms[type].push({ dictName: "" });
      } else {
        this.$message.warning("请先保存当前行数据");
      }
    },
    async handleDel(type, idx) {
      console.log(type, idx);
      if (this.tableForms[type][idx].projectDictId) {
        try {
          await this.$confirm(
            '<i class="el-icon-warning"></i>确认删除此标准数据?',
            {
              title: "提示!",
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              dangerouslyUseHTMLString: true,
              customClass: "messageTips"
            }
          );
          const res = await deleteProjectDict({
            projectId: this.resolvedData.projectId,
            id: this.tableForms[type][idx].projectDictId
          });
          if (res.code === 1) {
            this.$message.success("删除成功");
            this.tableForms[type].splice(idx, 1);
          } else {
            this.$message.error("删除失败");
          }
        } catch (error) {
          console.log(`output->error`, error);
        } finally {
          this.updateResolvedFormData();
        }
      } else {
        // 前端删除
        this.tableForms[type].splice(idx, 1);
      }
    }
  }
};
</script>
<style lang="less" scoped>
@import "../../common.less";
.subject-member-container {
  padding: 24px 16px;
  font-size: 14px;
  color: #3a4762;
  line-height: 20px;

  .form-item {
    display: flex;
    flex-direction: row;
    line-height: 20px;
    margin-bottom: 16px;

    span {
      display: block;
      width: 126px;
      text-align: right;
      font-weight: 600;
      font-weight: 600;
    }

    p {
      color: #2e6be6;
      cursor: pointer;
      margin: 0;
      margin-left: 14px;
    }

    &:last-child {
      margin-top: 0;
    }
  }

  .table-form {
    border: 1px solid #dcdfe6;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .header {
      background: #f7f8fa;
      display: flex;
      align-items: center;
      min-height: 52px;
      justify-content: space-between;
      padding: 16px;
      box-sizing: border-box;
      color: #3a4762;
      font-weight: 500;

      .title {
        flex: 1;
        text-align: left;
      }

      .exit-standard {
        width: 120px;
        margin-right: 24px;
      }

      .opration {
        width: 100px;
        margin-left: 24px;
      }
    }

    .content {
      .items {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 16px;

        .sp {
          flex: 1;
          text-align: left;
        }

        /deep/.el-select {
          width: 120px;

          .el-input {
            width: 100%;
          }
        }

        .del-btn {
          display: block;
          width: 98px;
          color: #e63746;
          cursor: pointer;
        }
      }
    }

    .action-btn {
      text-align: left;
      padding: 4px 16px;
    }
  }
}
</style>
