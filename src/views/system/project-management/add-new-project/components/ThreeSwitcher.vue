<template>
  <div
    ref="switherRef"
    class="three-checker"
    :class="switcherClass"
    @click="handleClick"
  >
    <div v-if="disabled" class="mask"></div>
    <span>
      <i
        v-if="status === 2"
        class="el-icon-star-on"
        :style="{ color: rightColor }"
      ></i>
    </span>
    <div class="line">
      <span></span>
    </div>
  </div>
</template>

<script>
export default {
  name: "ThreeSwitcher",
  components: {
    // 如果有子组件可以在这里注册
  },
  mixins: [],
  props: {
    status: {
      type: Number,
      default: 3
    },
    left: {
      type: Boolean,
      default: false
    },
    right: {
      type: Boolean,
      default: false
    },
    leftColor: {
      type: String,
      default: "#B8BECC"
    },
    rightColor: {
      type: String,
      default: "#E6C833"
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  computed: {
    switcherClass() {
      const classes = {
        left: this.status === 0 || !this.left,
        middle: this.status === 1 || (this.left && !this.right),
        right: this.status === 2 || (this.left && this.right)
      };
      return classes;
    }
  },
  methods: {
    handleClick(event) {
      const { left, width } = this.$refs.switherRef.getBoundingClientRect();
      const clickX = event.clientX - left;
      const ratio = clickX / width;
      let newStatus = 0;

      if (ratio > 0.66) {
        newStatus = 2;
      } else if (ratio > 0.33) {
        newStatus = 1;
      }
      this.$emit("update:status", newStatus);
      this.$emit("update:left", newStatus !== 0);
      this.$emit("update:right", newStatus === 2);
      this.$emit("change", newStatus);
    }
  }
};
</script>

<style lang="less" scoped>
.three-checker {
  position: relative;
  width: 60px;
  height: 22px;
  background: #b8becc;
  border-radius: 11px;
  display: flex;
  align-items: center;
  padding: 2px;
  box-sizing: border-box;
  cursor: pointer;
  transition: 0.2s;
  .mask {
    position: absolute;
    z-index: 2;
    width: 100%;
    height: 100%;
    inset: 0;
    border-radius: inherit;
    background: #aaa;
    opacity: 0.3;
  }

  & > span {
    display: block;
    width: 18px;
    height: 18px;
    background: #fff;
    border-radius: inherit;
    transition: 0.2s;
    margin-left: 0;
    z-index: 1;
    text-align: center;
    line-height: 18px;

    & > i {
      font-size: 12px;
    }
  }

  .line {
    position: absolute;
    width: 40px;
    height: 2px;
    background: rgba(255, 255, 255, 0.5);
    z-index: 0;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);

    &::before,
    &::after,
    & > span {
      position: absolute;
      content: "";
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: inherit;
    }

    & > span {
      left: 50%;
      top: -2px;
      transform: translateX(-50%);
    }

    &::before {
      left: -6px;
      top: -2px;
    }

    &::after {
      right: -6px;
      top: -2px;
    }
  }

  &.left {
    & > span {
      transform: translateX(0);
    }
  }

  &.middle {
    background: #2e6be6;

    & > span {
      transform: translateX(100%);
      margin-left: 2px;
    }
  }

  &.right {
    background: #e6c833;

    & > span {
      transform: translateX(200%);
      margin-left: 2px;
    }
  }
}
</style>
