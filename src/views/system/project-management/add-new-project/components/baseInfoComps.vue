<template>
  <div class="form-container">
    <el-form
      ref="form"
      :model="form"
      label-width="122px"
      label-position="right"
      :rules="rules"
      :disabled="!isEdit"
    >
      <!-- 单行表单项 -->
      <el-form-item label="对照方式：" prop="compareMethodType">
        <el-select v-model="form.compareMethodType" placeholder="请选择">
          <el-option
            v-for="item in options.compareMethodTypeOptions"
            :key="item.value"
            :label="item.name"
            :value="item.value"
            :disabled="item.value === 'CC'"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="盲法：" prop="blindMethodType">
        <el-select v-model="form.blindMethodType" placeholder="请选择">
          <el-option
            v-for="item in options.blindMethodTypeOptions"
            :key="item.value"
            :label="item.name"
            :value="item.value"
            :disabled="
              item.value === 'SINGLE_BLIND' || item.value === 'DOUBLE_BLIND'
            "
          />
        </el-select>
      </el-form-item>

      <el-form-item label="项目总体本量：" prop="projectSampleCapacity">
        <el-input
          v-model.number="form.projectSampleCapacity"
          placeholder="请输入"
          maxlength="10"
          :disabled="disableCapacity"
        ></el-input>
      </el-form-item>

      <el-form-item label="入组时机：" prop="enrollmentMomentType">
        <el-select v-model="form.enrollmentMomentType" placeholder="请选择">
          <el-option
            v-for="item in options.enrollmentMomentTypeOptions"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!-- 多选组件 -->
      <el-form-item label="经济补偿方式：" prop="compensationMethodTypes">
        <el-select
          v-model="form.compensationMethodTypes"
          multiple
          placeholder="请选择，可多选"
        >
          <el-option
            v-for="item in options.compensationMethodTypesOptions"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="补偿硬件"
        prop="compensationMethodExtra.hc.hardwareTypes"
        v-if="form.compensationMethodTypes.includes('HC')"
      >
        <el-select
          v-model="form.compensationMethodExtra.hc.hardwareTypes"
          multiple
          placeholder="请选择，可多选"
        >
          <el-option
            v-for="item in options.CompensationHardwareMethodOptions"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 1、数字，保留两位小数2、若“经济补偿方式”选择“货币补偿”显示该字段、否则隐藏。 -->
      <el-form-item
        label="补偿金额"
        prop="compensationMethodExtra.mc.amount"
        v-if="form.compensationMethodTypes.includes('MC')"
      >
        <el-input
          v-model.number="form.compensationMethodExtra.mc.amount"
          type="number"
        />
      </el-form-item>
      <el-form-item
        label="其他补偿："
        v-if="form.compensationMethodTypes.includes('OC')"
      >
        <el-input
          v-model="form.compensationMethodExtra.remark"
          maxlength="100"
        ></el-input>
      </el-form-item>
      <!-- 文件上传 -->
      <el-form-item label="附件：">
        <UploadComps :file-url.sync="form.baseFileUrls" />
      </el-form-item>
      <el-form-item>
        <el-button v-if="isEdit" type="primary" @click="submitForm"
          >保存</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import UploadComps from "../../components/uploadComps.vue";

import {
  compareMethodTypeOptions,
  blindMethodTypeOptions,
  enrollmentMomentTypeOptions,
  compensationMethodTypesOptions,
  CompensationHardwareMethodOptions
} from "../../../constant/projectEnum";
import { createOrEditProjectScheme } from "@/api/system";
import { resolvedFormDataProp } from "../../mixins";
export default {
  name: "BaseInfoComps",
  components: {
    UploadComps
  },
  mixins: [resolvedFormDataProp],
  props: {
    isEdit: {
      type: Boolean,
      default: true
    },
    disableCapacity: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      baseForm: {
        compareMethodType: "PC",
        blindMethodType: "OPEN_LABEL",
        projectSampleCapacity: "",
        enrollmentMomentType: "",
        compensationMethodTypes: [],
        hardwareTypes: [],
        amount: 0,
        remark: "",
        baseFileUrls: []
      },
      form: {
        compareMethodType: "PC",
        blindMethodType: "OPEN_LABEL",
        projectSampleCapacity: "",
        enrollmentMomentType: "",
        compensationMethodTypes: [],
        compensationMethodExtra: {
          hc: {
            hardwareTypes: []
          },
          mc: {
            amount: 0
          },
          remark: ""
        },
        baseFileUrls: []
      },
      options: {
        compareMethodTypeOptions,
        blindMethodTypeOptions,
        enrollmentMomentTypeOptions,
        compensationMethodTypesOptions,
        CompensationHardwareMethodOptions
      },
      rules: {
        compareMethodType: [
          { required: true, message: "请选择对照方式", trigger: "change" }
        ],
        blindMethodType: [
          { required: true, message: "请选择盲法", trigger: "change" }
        ],
        projectSampleCapacity: [
          { required: true, message: "请输入项目总体本量", trigger: "blur" },
          {
            type: "number",
            min: 0,
            message: "请输入有效的数字",
            trigger: "blur"
          }
        ],
        enrollmentMomentType: [
          { required: true, message: "请选择入组时机", trigger: "change" }
        ],
        compensationMethodTypes: [
          {
            type: "array",
            min: 1,
            message: "请至少选择一种经济补偿方式",
            trigger: "change"
          }
        ],
        "compensationMethodExtra.hc.hardwareTypes": [
          {
            type: "array",
            min: 1,
            message: "请至少选择一种补偿硬件",
            trigger: "change"
          }
        ],
        "compensationMethodExtra.mc.amount": [
          { required: true, message: "请输入补偿金额", trigger: "blur" },
          {
            type: "number",
            min: 0,
            message: "请输入有效的数字",
            trigger: "blur"
          },
          {
            pattern: /^\d+(\.\d{1,2})?$/,
            message: "请输入正确的金额格式，最多保留两位小数",
            trigger: "blur"
          }
        ]
      }
    };
  },

  methods: {
    submitForm() {
      this.$refs.form.validate(async valid => {
        if (valid) {
          try {
            const res = await createOrEditProjectScheme({
              ...this.form,
              projectId: this.resolvedData.projectId
            });
            if (res.code === 1) {
              this.$message.success("方案配置基础信息保存成功");
              this.$emit("update", res);
              this.updateResolvedFormData();
            }
          } catch (error) {
            console.error(error);
            this.$message.error("方案配置基础信息提交失败：", error.msg);
          }
        } else {
          this.$message.error("表单验证失败，请检查填写内容");
          return false;
        }
      });
    },
    resetForm() {
      this.$refs.form.resetFields();
    }
  }
};
</script>
<style lang="less" scoped>
@import "../../common.less";

.form-container {
  padding: 24px 18px;

  /deep/.el-input {
    width: 240px;
  }
}
</style>
