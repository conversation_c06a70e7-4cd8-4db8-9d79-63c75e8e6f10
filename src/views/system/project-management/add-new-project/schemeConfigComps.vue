<template>
  <div class="scheme-config-container" ref="shemeConfig">
    <div class="nav-bar">
      <span
        v-for="(item, index) in nav"
        :key="index"
        :data-to="item.target"
        @click="navClick(item)"
      >
        {{ item.label }}
      </span>
    </div>
    <div v-for="(item, index) in nav" :key="index" :id="item.target">
      <div class="common-title-bar">
        {{ item.label }}
      </div>
      <component
        :is="item.comp"
        :key="item.target"
        :ref="`compRef${index}`"
        :resolvedData="resolvedFormData"
        :updateResolvedFormData="updateResolvedFormData"
      />
    </div>

    <el-backtop target=".el-container .el-main"></el-backtop>
    <div class="right-nav">
      <el-tabs v-model="navName" tab-position="right" @tab-click="navClick">
        <el-tab-pane
          v-for="tab in nav"
          :label="tab.label"
          :key="tab.target"
          :target="tab.target"
          :name="tab.target"
        ></el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import BaseInfoComps from "./components/baseInfoComps.vue";
import GroupConfigCompsVue from "./components/groupConfigComps.vue";
import CRFConfigComps from "./components/cRFConfigComps.vue";
import RandomConfigComps from "./components/randomConfigComps.vue";
import SubjectMemberComps from "./components/subjectMemberComps.vue";
import ReasarchTestComps from "./reasarchTestComps.vue";

import { initialFormData } from "../mixins";

export default {
  name: "SchemeConfigComps",
  components: {
    BaseInfoComps,
    GroupConfigCompsVue,
    CRFConfigComps,
    RandomConfigComps,
    SubjectMemberComps,
    ReasarchTestComps
  },
  mixins: [initialFormData],
  props: {},
  data() {
    return {
      nav: [
        {
          label: "基础信息",
          target: "info",
          comp: "BaseInfoComps"
        },
        {
          label: "分组设置",
          target: "groups",
          comp: "GroupConfigCompsVue"
        },
        {
          label: "CRF表配置",
          target: "crfTable",
          comp: "CRFConfigComps"
        },
        {
          label: "随机设置",
          target: "randomConfig",
          comp: "RandomConfigComps"
        },
        {
          label: "受试者选择",
          target: "subject",
          comp: "SubjectMemberComps"
        },
        {
          label: "研究评估和测定",
          target: "reasarch",
          comp: "ReasarchTestComps"
        }
      ],
      navName: "info"
    };
  },
  computed: {},
  watch: {},
  mounted() {
    if (IntersectionObserver) {
      this.observer = new IntersectionObserver(
        entries => {
          const entry = entries.find(entry => entry.isIntersecting);
          if (entry && !this.timer) {
            this.navName = entry.target.id ? entry.target.id : this.navName;
          }
        },
        { threshold: 0.1 }
      );
      this.nav.forEach(it =>
        this.observer.observe(
          this.$refs.shemeConfig.querySelector(`#${it.target}`)
        )
      );
    }
  },
  beforeDestroy() {
    this.observer.disconnect();
    this.observe = null;
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  },
  methods: {
    navClick(item) {
      if (!this.$refs.shemeConfig || (!item.target && !item.$attrs)) return;
      const el = this.$refs.shemeConfig.querySelector(
        `#${item.target || item.$attrs.target}`
      );
      this.navName = item.target ? item.target : item.$attrs.target;
      el &&
        el.scrollIntoView({
          behavior: "smooth",
          block: "start"
        });
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      this.timer = setTimeout(() => {
        if (this.timer) {
          clearTimeout(this.timer);
          this.timer = null;
        }
      }, 2000);
    }
  }
};
</script>
<style lang="less" scoped>
@import "../common.less";

.scheme-config-container {
  background: #fff;
  padding: 16px;
  position: relative;
}

html {
  scroll-behavior: smooth;
}

.nav-bar {
  padding-bottom: 17px;
  text-align: center;

  span {
    position: relative;
    display: inline-block;
    cursor: pointer;
    padding: 0 16.5px;
    color: #939cae;
    text-decoration: none;
    width: max-content;

    &::after {
      position: absolute;
      display: block;
      content: "";
      width: 1px;
      height: 12px;
      background: #e1e5ed;
      top: 50%;
      transform: translateY(-50%);
      right: 0;
      transform-origin: center center;
    }

    &:last-child::after {
      display: none;
    }
  }
  &.scrolled {
    span {
      padding: 0 8px;
    }
  }
}
/deep/.right-nav {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  .el-tabs {
    text-align: left;
    &__item {
      padding: 0 10px;
    }
  }
}
</style>
