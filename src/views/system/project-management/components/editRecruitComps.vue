<template>
  <el-dialog
    :visible.sync="visible"
    :title="title"
    width="912px"
    :show-close="false"
    :before-close="beforeClose"
    @close="close"
  >
    <template #title>
      <div class="header">
        <span>{{ title }}</span>
        <el-button
          type="text"
          icon="el-icon-view"
          @click="() => (showPreview = true)"
        >
          预览
        </el-button>
      </div>
    </template>
    <wangEditor @editorchange="editorchange" :id="1987" :content="content" />
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="submit">确认</el-button>
    </template>
    <el-dialog
      :visible.sync="showPreview"
      append-to-body
      width="325px"
      custom-class="preview"
    >
      <div class="container" v-html="html"></div>
    </el-dialog>
  </el-dialog>
</template>
<script>
import wangEditor from "./wang-editor.vue";
export default {
  name: "EditRecruitComps",
  components: {
    wangEditor
  },
  mixins: [],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ""
    },
    content: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      showPreview: false,
      html: ""
    };
  },
  computed: {},
  watch: {},
  mounted() {},
  methods: {
    submit() {
      this.$emit("update:visible", false);
      this.$emit("submit", this.html);
    },
    editorchange(html) {
      this.html = html;
    },
    async close() {
      this.$emit("update:visible", false);
    },
    beforeClose(done) {
      console.log(`output->this.content`, this.content);
      console.log(`output->this.html`, this.html);
      if (this.content !== this.html && this.html !== "<p><br></p>") {
        return this.$confirm("存在未保存内容，确认退出？")
          .then(_ => {
            done();
          })
          .catch(_ => {});
      } else {
        done();
      }
    }
  }
};
</script>
<style lang="less" scoped>
@import "../common.less";

.header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

/deep/.el-dialog.preview {
  background: transparent;
  box-shadow: none;

  .el-icon-close {
    font-size: 38px;
    font-weight: bold;
    color: #000;
    transform: translateX(156%);
  }

  .el-dialog__header {
    border: none;
  }

  .container {
    width: 100%;
    height: 625px;
    overflow-y: auto;
    border: 8px solid #000000d2;
    border-radius: 18px;
    background: #fff;
    padding: 8px 12px;
  }
}
</style>
