<template>
  <div class="searcher-container">
    <el-form
      :model="searchParams"
      inline
      ref="searchForm"
      label-width="98px"
      class="search-form"
      @submit.native.prevent="handleSearch"
    >
      <!-- 项目名称 -->
      <el-form-item label="项目名称">
        <el-input
          v-model="searchParams.projectContent"
          placeholder="请输入搜索"
          clearable
          size="small"
        ></el-input>
      </el-form-item>

      <!-- 项目来源 -->
      <el-form-item label="项目来源">
        <el-select
          v-model="searchParams.projectSource"
          placeholder="请选择或输入搜索"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="(item, index) in projectSourceOptions"
            :key="index"
            :value="item.value"
            :label="item.name"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 项目类型 -->
      <el-form-item label="项目类型">
        <el-select
          v-model="searchParams.projectType"
          placeholder="请选择或输入搜索"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="item in projectTypeOptions"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- 研究类型 -->
      <el-form-item label="研究类型">
        <el-select
          v-model="searchParams.projectResearchType"
          placeholder="请选择或输入搜索"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="item in projectResearchTypeOptions"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- 组长单位 -->
      <el-form-item label="组长单位">
        <el-select
          v-model="searchParams.leaderHospitalId"
          placeholder="请选择或输入搜索"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="(value, key) in dictHospitalList"
            :key="key"
            :value="key"
            :label="value"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- 研究负责人 -->
      <el-form-item label="研究负责人">
        <el-select
          v-model="searchParams.leaderDoctorId"
          placeholder="请选择或输入搜索"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="item in researchLeaderOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- 状态 -->
      <el-form-item label="状态">
        <el-select
          v-model="searchParams.projectStatus"
          placeholder="请选择或输入搜索"
          filterable
          clearable
          size="small"
        >
          <el-option
            v-for="item in projectStatusOptions"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- 顶部操作按钮 -->
      <div style="text-align: center;">
        <el-button type="primary" @click="handleSearch" size="small"
          >搜索</el-button
        >
        <el-button @click="resetForm" size="small">重置</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
import {
  projectSourceOptions,
  projectTypeOptions,
  projectResearchTypeOptions,
  projectStatusOptions
} from "../../constant/projectEnum";

import { getDoctorList } from "@/api/system";

export default {
  name: "SearchComps",
  components: {},
  data() {
    return {
      projectSourceOptions,
      projectTypeOptions,
      projectResearchTypeOptions,
      projectStatusOptions,
      searchParams: {
        projectContent: "",
        projectCodes: "",
        projectSource: "",
        projectType: "",
        projectResearchType: "",
        leaderHospitalId: "",
        leaderDoctorId: "",
        projectStatus: ""
      },
      dictHospitalList: sessionStorage.getItem("dictData-hospital-list")
        ? JSON.parse(sessionStorage.getItem("dictData-hospital-list"))
        : {},
      researchLeaderOptions: []
    };
  },
  computed: {},
  watch: {},
  mounted() {
    this.getResearchLeaderList(); // 获取研究负责人列表
  },
  methods: {
    // 处理搜索逻辑
    handleSearch() {
      console.log("搜索参数:", this.searchParams);
      this.$emit("search", {
        ...this.searchParams
      }); // 触发父组件的搜索事件
    },
    // 重置表单
    resetForm() {
      this.$refs.searchForm.resetFields();
      this.searchParams = {
        projectContent: "",
        projectCodes: "",
        projectSource: "",
        projectType: "",
        projectResearchType: "",
        leaderHospital: "",
        leaderDoctorId: "",
        projectStatus: ""
      };
      this.handleSearch();
    },
    // 获取研究负责人列表
    async getResearchLeaderList() {
      try {
        let allData = [];
        let pageNumber = 1;
        let pageSize = 200;
        let hasMore = true;
        while (hasMore) {
          const {
            data: { contents, total }
          } = await getDoctorList({
            pageNumber,
            pageSize
          });
          allData = allData.concat(contents);
          if (allData.length >= total) {
            hasMore = false;
          } else {
            pageNumber++;
          }
        }
        this.researchLeaderOptions = allData.map(item => ({
          id: item.doctorId,
          name: item.doctorName
        }));
      } catch (error) {
        console.error("获取研究负责人列表失败:", error);
      }
    }
  }
};
</script>
<style lang="less" scoped>
@import "../common.less";

.searcher-container {
  --row-height: 32px;

  display: flex;
  background-color: #fff;
  padding: 16px;
  text-align: left;

  .el-input,
  .el-select {
    width: 240px;
    height: var(--row-height);
  }

  .el-button {
    height: var(--row-height);
    line-height: var(--row-height);
    padding: 0;
    width: 70px;
    border-radius: 2px;

    &--primary {
      background: #2e6be6;
    }
  }

  /deep/.el-form-item {
    margin-bottom: 12px;

    &__label,
    &__content {
      line-height: var(--row-height);
    }
  }

  /deep/.el-input__inner {
    height: var(--row-height);
    line-height: var(--row-height);
  }
}
</style>
