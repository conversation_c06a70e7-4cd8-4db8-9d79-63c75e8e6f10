<template>
  <el-upload
    drag
    multiple
    :before-upload="beforeUpload"
    :file-list="fileList"
    :limit="limit"
    :http-request="uploadHandle"
    :on-change="fileChange"
    :on-remove="fileChange"
    :on-preview="preview"
    :auto-upload="true"
  >
    <div>
      <div class="upload-title">
        <i class="el-icon-upload2"></i><span>上传文件</span>
      </div>
      <div class="upload-tip">
        支持上传Word、PDF、JPG、JPEG、PNG格式
      </div>
    </div>
  </el-upload>
</template>
<script>
import * as qiniu from "qiniu-js";
import { httpReq } from "@/http";

export default {
  name: "UploadComps",
  props: {
    validTypes: {
      type: Array,
      default: () => {
        return ["doc", "docx", "pdf", "jpg", "jpeg", "png"];
      }
    },
    tokenUrl: {
      type: String,
      default: "/admins/getUploadToken"
    },
    fileBaseUrl: {
      type: String,
      default: "or-service/project/"
    },
    limit: {
      type: Number,
      default: 9
    },
    fileUrl: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      fileList: [],
      qiNiuToken: ""
    };
  },
  watch: {
    fileUrl: {
      handler(val) {
        if (val && !this.fileList.length) {
          const current = val
            .map(({ fileName, fileUrl, id }) => ({
              name: fileName
                ? fileName
                    .split("/")
                    .slice(-1)
                    .pop()
                : fileName,
              url: fileUrl,
              status: "success"
            }))
            .filter(it => it.name && it.url);
          this.fileList =
            this.fileList.length !== current.length ? current : this.fileList;
        }
      },
      immediate: true
    }
  },
  mounted() {},
  methods: {
    async uploadHandle(fileArgs) {
      await this.initQiuToken();

      const file = this.fileList.find(it => it.uid === fileArgs.file.uid);
      if (!file) return;
      try {
        const [files, uptoken, key, putExtra, config] = [
          file.raw,
          this.qiNiuToken,
          `${this.fileBaseUrl}${file.name}`,
          {
            fname: "", //文件原文件名
            params: {}, //用来放置自定义变量
            mimeType: null //用来限制上传文件的类型，为 null 时表示不对文件类型限制
          },
          {
            useCdnDomain: true, //表示是否使用 cdn 加速域名，为布尔值，true 表示使用，默认为 false。
            region: qiniu.region.z2 // 根据具体提示修改上传地区,当为 null 或 undefined 时，自动分析上传域名区域
          }
        ];
        const observer = qiniu.upload(files, key, uptoken, putExtra, config);
        const _this = this;
        observer.subscribe({
          next(res) {
            const {
              total: { loaded, percent, size }
            } = res;
            console.log(`output->next`, file.name, percent);
            file.status = "uploading";
            file.percentage = percent;
          },
          error(err) {
            console.log(`output->err`, err);
          },
          complete(res) {
            file.status = "success";
            const { key, hash } = res;
            file.url = `https://image.scheartmed.com/${key}`;
            console.log(`output->complete`, res);
            _this.fileChange(file, _this.fileList);
          }
        });
      } catch (error) {
        console.log(`output->error`, error);
      }
    },
    beforeUpload(file) {
      const extension = file.name
        .split(".")
        .pop()
        .toLowerCase();
      if (!this.validTypes.includes(extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      return true;
    },
    async fileChange(file, fileList) {
      this.fileList = fileList;
      this.$emit(
        "update:fileUrl",
        fileList
          .filter(it => it.status === "success")
          .map(it => ({ fileName: it.name, fileUrl: it.url || it.fileUrl }))
          .filter(it => it.fileUrl)
      );
      console.log(`output->updateFileUrl`, this.fileUrl);
    },
    async initQiuToken() {
      try {
        const { data } = await httpReq({ url: this.tokenUrl });
        this.qiNiuToken = data;
      } catch (error) {
        console.log(`output->error`, error);
      }
    },
    preview(file) {
      if (file.url) {
        // 如果文件有 URL，直接打开预览
        window.open(file.url, "_blank");
      } else if (file.raw) {
        // 如果没有 URL，使用 raw 数据生成临时 URL
        const tempUrl = URL.createObjectURL(file.raw);
        window.open(tempUrl, "_blank");

        // 注意：可以在不需要时释放临时 URL
        // URL.revokeObjectURL(tempUrl);
      } else {
        this.$message.error("无法预览该文件");
      }
    }
  }
};
</script>
<style lang="less" scoped>
/deep/.el-upload {
  &-dragger {
    height: fit-content;
    width: fit-content;
    background: #f7f8fa;
    border: 1px solid #dcdfe6;
    padding: 16px 35px;
  }

  .upload {
    &-title {
      font-size: 14px;
      color: #3a4762;
      line-height: 20px;

      span {
        margin-left: 8px;
      }
    }

    &-tip {
      color: #b8becc;
      line-height: 24px;
      margin-top: 4px;
      white-space: nowrap;
    }
  }
}
</style>
