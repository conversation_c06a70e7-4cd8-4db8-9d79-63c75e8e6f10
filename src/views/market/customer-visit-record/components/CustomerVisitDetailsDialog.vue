<template>
  <div>
    <CommonDialog
      @cancel="cancel"
      :visible.sync="visible"
      :title="title"
      width="800px"
      :showBtn="false"
    >
      <div class="visit-wrapper">
        <div class="top-content">
          <div class="form-item">
            <div class="form-label">拜访开始时间:</div>
            <div class="form-content">{{info.startTime}}</div>
          </div>
          <div class="form-item">
            <div class="form-label">拜访结束时间:</div>
            <div class="form-content">{{info.endTime}}</div>
          </div>
          <div class="form-item">
            <div class="form-label">拜访耗时:</div>
            <div class="form-content">{{duration}}</div>
          </div>
          <div class="form-item">
            <div class="form-label">拜访目标:</div>
            <div class="form-content">{{info.objective}}</div>
          </div>
        </div>
        <div class="middle-content">
          <div class="form-item">
            <div class="form-label">客户态度:</div>
            <div class="form-content">{{customerAttitudeMap[info.attitude]}}</div>
          </div>
          <div class="form-item">
            <div class="form-label">客户疑虑点:</div>
              <div class="form-content">{{info.doubt}}</div>
          </div>
        </div>
        <div class="top-content">
          <div class="form-item">
            <div class="form-label">下一步行动计划:</div>
            <el-tooltip class="item" effect="dark" placement="top">
              <div slot="content" style="max-width: 200px">{{info.nextAction}}</div>
              <div class="form-content">{{info.nextAction}}</div>
            </el-tooltip>
          </div>
          <div class="form-item">
            <div class="form-label">下一次拜访日期:</div>
            <div class="form-content">{{info.nextTime}}</div>
          </div>
          <div class="form-item">
            <div class="form-label">下一次拜访预期:</div>
            <el-tooltip class="item" effect="dark" placement="top">
              <div slot="content" style="max-width: 200px">{{info.nextExpect}}</div>
              <div class="form-content">{{info.nextExpect}}</div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </CommonDialog>
  </div>
</template>

<script>
import CommonDialog from "../../../../components/CommonDialog.vue";
import {customerAttitudeMap} from "../../constant/marketEnum";
import {getVisitDetailsReq} from "../../../../api/market";
import {formatTime} from "@/util/util";
export default {
  name: "CustomerVisitDetailsDialog",
  components:{CommonDialog},
  props: {
    visible: {
      require: true,
      type:Boolean
    },
    title: {
      require: true,
      type:String
    },
    visitId:{
      require:true
    },
    duration:{
      require:true
    }
  },
  data(){
    return{
      customerAttitudeMap,
      formatTime,
      info:{
        userRole:'',
        objective:'',
        attitude:'',
        doubt:'',
        startTime:'',
        endTime:'',
        nextAction:"",
        nextTime:'',
        nextExpect:'',
        duration:""
      },
    }
  },
  created() {
    this.getDetail()
  },
  methods:{
    cancel(){
      this.$emit('update:visible', false)
    },
    getDetail(){
      getVisitDetailsReq({id:this.visitId}).then(res=>{
        this.info = res.data
      }).catch(err=>{
        this.$message.error(`系统错误${err.msg}`)
      })
    }
  }
}
</script>

<style scoped lang="less">
.visit-wrapper{
  width: 100%;
  box-sizing: border-box;
  padding: 24px 84px;
  text-align: left;
  color: #15233F;
  overflow-y: auto;
  .top-content{
    display: grid;
    grid-template-columns: 1fr 1fr ;
    grid-row-gap: 8px;
    .form-item{
      display: flex;
      align-items: center;
      max-width: 400px;
      .form-label{
        width: 120px;
        font-weight: 400;
        font-size: 14px;
        color: #939CAE;
      }
      .form-content{
        flex: 1;
        font-weight: 400;
        font-size: 14px;
        color: #3A4762;
        margin-left: 20px;
        white-space: nowrap; /* 保证文本在一行内显示 */
        overflow: hidden; /* 隐藏溢出的内容 */
        text-overflow: ellipsis;
        flex-wrap: nowrap;
      }
    }
  }
  .middle-content{
    box-sizing: border-box;
    padding: 8px;
    background: #F7F8FA;
    border-radius: 4px;
    margin: 16px 0;
    .form-item{
      margin: 8px 0;
      display: flex;
      align-items: center;
      .form-label{
        width: 120px;
        font-weight: 400;
        font-size: 14px;
        color: #939CAE;
      }
      .form-content{
        flex: 1;
        font-weight: 400;
        font-size: 14px;
        color: #3A4762;
        margin-left: 20px;
      }
    }
  }
}
</style>
