<template>
  <div class="indicator-wrapper">
    <div class="search-box box">
      <el-form
        ref="searchFrom"
        size="mini"
        :inline="true"
        :model="searchFrom"
      >
        <el-form-item label="指标责任人" class="minclass" prop="keyword">
          <el-input
            v-model="searchFrom.keyword"
            placeholder="指标责任人姓名"
          ></el-input>
        </el-form-item>
        <el-form-item label="地区医院" prop="provinceAndCity" class="minclass select-box">
          <el-cascader
            size="mini"
            filterable
            :props="{ value: 'id',label:'name',children:'children',expandTrigger: 'hover'}"
            separator="-"
            v-model="searchFrom.provinceAndCity"
            placeholder="请选择地区医院"
            :options="regionOptions"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="医院分类" class="minclass" prop="type">
          <el-select
            v-model="searchFrom.type"
            filterable
            placeholder="请先选择医院分类"
          >
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="(item, index) in hosTypes"
              :key="index"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="医院等级" class="minclass" prop="level">
          <el-select
            v-model="searchFrom.level"
            placeholder="选择医院等级"
          >
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="(item, index) in hosLevelList"
              :key="index"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="指标确认人" class="minclass" prop="managerId">
          <el-select v-model="searchFrom.managerId" placeholder="指标确认人">
            <el-option :label="item.name" :value="item.userId" v-for="item in indicatorManagerOption" :key="item.userId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="指标状态" class="minclass" prop="status">
          <el-select v-model="searchFrom.status" placeholder="指标状态">
            <el-option label="全部" value=""></el-option>
            <el-option :label="item.name" :value="item.value" v-for="item in indicatorStatusOptions" :key="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="指标月份" class="date-month-mini-class" prop="quotaTime">
          <el-date-picker
            v-model="searchFrom.quotaTime"
            type="month"
            align="right"
            value-format="yyyy-MM"
            placeholder="指标月份"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="指标制定时间" class="date-mini-class" prop="generateTime">
          <el-date-picker
            v-model="searchFrom.generateTime"
            type="daterange"
            align="right"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <span class="btn-group">
          <el-button type="primary" @click="onSubmit" class="search-btn">查询</el-button>
          <el-button @click="resetForm" class="reset-btn">重置</el-button>
        </span>
      </el-form>
    </div>
    <div class="data-box-wrapper">
      <div class="data-box box">
        <div class="table">
          <el-table
            :data="tableData"
            style="width: 100%"
            ref="table"
            height="98%"
            :header-cell-style="{
              color: '#15233F',
              background: '#F7F8FA',
              'font-weight': 'bold',
              'font-size': '14px'
          }"
          >
            <el-table-column
              prop="groupId"
              align="left"
              label="序号"
              width="60"
            >
              <template slot-scope="scope">
                {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column
              prop="month"
              label="指标月份"
              width="150"
              align="left"
            >
              <template slot-scope="scope">
                {{scope.row.year}}年{{scope.row.month}}月
              </template>
            </el-table-column>
            <el-table-column prop="marketName" align="left" label="指标责任人" width="150">
            </el-table-column>
            <el-table-column prop="hospitalName" align="left" label="指标所属医院" width="150">
            </el-table-column>
            <el-table-column prop="quotaNum" align="left" label="指标量 (台手术/年)" width="150">
            </el-table-column>
            <el-table-column prop="completeNum" align="left" label="完成量 (台手术/年)" width="150">
            </el-table-column>
            <el-table-column prop="finishedPercentage" align="left" label="指标达成率" width="150">
              <template slot-scope="scope">
                <span v-if="scope.row.quotaNum">
                  {{Math.round((scope.row.completeNum ? scope.row.completeNum/scope.row.quotaNum : 0 / scope.row.quotaNum).toFixed(2)*100)}}%
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="areas" align="left" label="医院所属地区" width="150">
              <template slot-scope="scope">
                {{scope.row.province}}{{scope.row.city}}
              </template>
            </el-table-column>
            <el-table-column prop="type" align="left" label="医院分类" width="150">
              <template slot-scope="scope">
                <span>{{hosTypesMap[scope.row.type]}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="hospitalLevel" align="left" label="医院等级" width="150">
              <template slot-scope="scope">
                <span>{{hosLevelMap[scope.row.hospitalLevel]}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="expectNum" align="left" label="医院预计年总手术量" width="150">
            </el-table-column>
            <el-table-column prop="generateTime" align="left" label="指标制定时间" width="200">
            </el-table-column>
            <el-table-column prop="managerName" align="left" label="指标确认人" width="150">
            </el-table-column>
            <el-table-column prop="status" align="left" label="指标记录状态" width="150">
              <template slot-scope="scope">
                <span>{{indicatorStatusMap[scope.row.status]}}</span>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              align="center"
              label="操作"
              width="150"
            >
              <template slot-scope="scope">
                <el-button
                  @click="lookDetail(scope.row)"
                  type="text"
                  size="mini"
                  class="normal-text-btn"
                >详情</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          :page-sizes="[10, 20, 50]"
          :page-size="pageSize"
          layout="total,sizes, prev, pager, next"
          :total="total"
          size="mini"
        >
        </el-pagination>
      </div>
    </div>
    <IndicatorDetailsDialog
      :visible.sync="detailsDialogVisible"
      title="任务指标详情"
      :indicatorInfo="indicatorInfo"
    ></IndicatorDetailsDialog>
  </div>
</template>

<script>
import { timeMode } from "@/util/util";
import IndicatorDetailsDialog from "./components/IndicatorDetailsDialog.vue";
import {
  getRegionListReq
} from "../../../api/client";
import {hosTypes,hosLevelList,hosLevelMap,hosTypesMap} from "../../client/partner-hospital/hospital-management/constant/hospitalEnum";
import {indicatorStatusOptions,indicatorStatusMap} from "../constant/marketEnum";
import {getIndicatorManagerOptionsReq, getMonthQuotaListReq} from "../../../api/market";
export default {
  name: "development-month-indicator",
  components:{IndicatorDetailsDialog},
  data() {
    return {
      timeMode,
      hosTypes,
      hosLevelList,
      indicatorStatusOptions,
      hosLevelMap,
      hosTypesMap,
      indicatorStatusMap,
      detailsDialogVisible: false,
      regionOptions:[],
      indicatorManagerOption:[],
      searchFrom: {
        keyword: "",
        provinceAndCity:"",
        type: "", //工作室病区id
        searchKey: "",
        generateTime: [],
        level: "",
        status: "",
        regionId:'',
        managerId:''
      },
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 1,
      dialogFormVisibleEditBox: false,
      pickerOptions: {
        onPick: this.getPickDate,
        disabledDate: this.disabledDate
      },
      pickDate: {},
      indicatorInfo:{}
    };
  },
  watch: {},
  created() {
    this.getRegionOptions()
    this.getIndicatorManagerOptions()
    this.getMonthIndicatorList(this.currentPage, this.pageSize, this.searchFrom);
  },
  methods: {
    onSubmit() {
      console.log("submit!", this.searchFrom);
      this.getMonthIndicatorList(1, this.pageSize, this.searchFrom);
    },
    resetForm() {
      this.$refs['searchFrom'].resetFields();
      this.getMonthIndicatorList(this.currentPage, this.pageSize, this.searchFrom);
    },
    // 切换每页条数
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.pageSize = val;
      this.getMonthIndicatorList(1, this.pageSize, this.searchFrom);
    },
    // 切换页码
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.currentPage = val
      this.getMonthIndicatorList(val, this.pageSize, this.searchFrom);
    },
    getMonthIndicatorList(pageNumber, pageSize, info) {
      const  {generateTime,provinceAndCity,...resParams } = info
      let params = {
        startTime:generateTime.length? generateTime[0] : null,
        endTime:generateTime.length? generateTime[1] : null,
        hospitalId:provinceAndCity.length? provinceAndCity[2] : '',
        ...resParams,
        pageNumber,
        pageSize
      }
      getMonthQuotaListReq(params).then(res => {
        this.tableData = res.data.contents;
        this.total = res.data.total;
      }).catch(err=>{
        this.$message.error(`系统错误:${err.msg}`)
        this.initDataByError()
      });
    },
    // 点击查看
    lookDetail(row) {
      this.indicatorInfo = row
      this.detailsDialogVisible = true
    },
    //获取筛选框地区列表
    getRegionOptions(){
      getRegionListReq().then(res=>{
        this.regionOptions = res.data
      })
    },
    //获取指标确认人筛选
    getIndicatorManagerOptions(){
      getIndicatorManagerOptionsReq().then(res=>{
        this.indicatorManagerOption = res.data
      })
    },
    initDataByError(){
      this.total = 1
      this.currentPage = 1
      this.pageSize = 10
      this.tableData = []
    },
  }
}
</script>

<style lang="less" scoped>
.indicator-wrapper {
  display: flex;
  flex-direction: column;
  .search-box {
    box-sizing: border-box;
    text-align: left;
    display: flex;
    align-items: center;
    /deep/.el-form {
      .el-form-item {
        margin: 0 20px 8px 0;
      }
      .minclass .el-form-item__content {
        .el-date-editor.el-input__inner {
          width: 360px;
        }
        .el-input__inner {
          border-radius: 2px;
          height: 32px;
          font-size: 14px;
          width: 220px;
        }
        .el-cascader{
          .el-input__inner{
            border-radius: 2px;
            height: 32px;
            font-size: 14px;
            width: 220px;
          }
        }
      }
      .minclass .el-form-item__label {
        line-height: 32px;
        font-size: 14px;
        color: #000000;
      }
      .minclass .el-icon-arrow-up:before {
        content: "";
      }
      .date-month-mini-class .el-form-item__label {
        line-height: 32px;
        font-size: 14px;
        color: #000000;
      }
      .date-month-mini-class{
        .el-input__inner {
          border-radius: 2px;
          height: 32px;
          font-size: 14px;
        }
      }
      .date-mini-class {
        .el-form-item__content{
          width: 300px;
        }
        .el-input__inner {
          width: 300px;
          border-radius: 2px;
          height: 32px;
          font-size: 14px;
          color: #000000;
          .el-range-separator{
            line-height: 24px;
          }
        }
        .el-range-input{
          font-size: 14px !important;
          .el-input__icon{
            text-align: right;
          }
        }
      }
      .date-mini-class .el-form-item__label {
        line-height: 32px;
        font-size: 14px;
        color: #000000;
      }
    }
  }
  .data-box-wrapper{
    box-sizing: border-box;
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .data-box {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #FFFFFF;
    border-radius: 6px;
    padding: 24px;
    .function-box {
      height: 40px;
      text-align: left;
      padding-bottom: 24px;
      .operation-btn {
        background-color: #2E6BE6;
      }
    }
    .table {
      flex: 1;
    }
    .el-pagination {
      text-align: center;
    }
  }
  .box {
    padding: 24px;
  }
}
.select-box{
  position: relative;
  .add-text{
    position: absolute;
    right: -40px;
    color: #1890FF;
    cursor: pointer;
  }
}
/deep/.select-box .el-icon-arrow-up:before {
  content: "";
}
/deep/.select-box .el-icon-arrow-down:before {
  content: "\e790";
}

.edit-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  /deep/.el-form.self-rule-from {
    margin-bottom: 32px;
    display: flex;
    flex-direction: column;
    .el-form-item__label {
      width: 86px !important;
      line-height: 40px;
      padding-right: 20px;
    }
    .el-form-item__content {
      width: 442px;
      line-height: 40px;
      border-radius: 4px;
      margin-left: 86px !important;
      .el-input__inner {
        width: 442px;
        height: 40px;
        line-height: 40px;
        padding: 13px 15px;
        font-size: 14px;
        border-radius: 2px;
        &::placeholder {
          color: #909399;
        }
        // border-radius: 4px;
      }
    }
    .el-form-item{
      .el-select{
        width: 100%;
      }
    }
  }
  /deep/.el-textarea{
    .el-textarea__inner{
      font-family: PingFangSC-Regular, PingFang SC;
      min-height: 60px !important;
    }
  }
}
.sub-btn {
  box-sizing: border-box;
  width: 100%;
  text-align: right;
  padding: 16px;
  border-top: 1px solid #E9E8EB;
  .el-button {
    width: 104px;
    height: 36px;
    font-size: 14px;
    color: #ffffff;
    background: #2E6BE6;
    margin-left: 20px;
    border-radius: 2px;
    &:last-of-type {
      color: #303133;
      background: #fff;
      border: 1px solid #dcdfe6;
    }
  }
}
.delete-text{
  width: 100%;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  margin: 0 auto;
}
.show-title{
  font-weight: bold;
}
.btn-group{
  line-height: 32px;
  .search-btn {
    width: 88px;
    height: 32px;
    color: #ffffff;
    font-size: 12px;
    background: #2E6BE6;
    padding: 0;
    &:first-of-type {
      margin: 0 16px 0 20px;
    }
  }
  .reset-btn{
    width: 88px;
    height: 32px;
    font-size: 12px;
    padding: 0;
  }
}
.el-button{
  border-radius: 2px;
}
</style>

