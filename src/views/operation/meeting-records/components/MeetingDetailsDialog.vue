<template>
  <div>
    <CommonDialog
      @cancel="cancel"
      @confirm="confirm"
      :visible.sync="visible"
      :title="title"
      width="800px"
      :showBtn="false"
    >
      <div class="meeting-details-wrapper">
        <div class="detail-label">基本信息</div>
        <div class="top-content">
          <div class="form-item">
            <div class="form-label">会议类型:</div>
            <div class="form-content">{{meetingTypeMap[currentMeetingDetail.meetingType]}}</div>
          </div>
          <div class="form-item">
            <div class="form-label">会议名称:</div>
            <div class="form-content">{{ currentMeetingDetail.name }}</div>
          </div>
          <div class="form-item">
            <div class="form-label">会议发起人:</div>
            <div class="form-content">{{currentMeetingDetail.marketName}}</div>
          </div>
          <div class="form-item">
            <div class="form-label">会议预算:</div>
            <div class="form-content">{{currentMeetingDetail.budgetingPlan}}</div>
          </div>
          <div class="form-item">
            <div class="form-label">会议开始时间:</div>
            <div class="form-content">{{currentMeetingDetail.startTime}}</div>
          </div>
          <div class="form-item">
            <div class="form-label">会议结束时间:</div>
            <div class="form-content">{{currentMeetingDetail.endTime}}</div>
          </div>
          <div class="form-item">
            <div class="form-label">会议状态:</div>
            <div class="form-content">{{meetingStatusMap[currentMeetingDetail.status]}}</div>
          </div>
        </div>
        <div class="detail-label">参会情况</div>
        <div class="middle-content">
          <div class="row-form-item" >
            <div class="form-label">参会医院:</div>
            <div class="form-content">{{ currentMeetingDetail.hospitalName }}</div>
          </div>
          <div class="row-form-item">
            <div class="form-label">医院参会人:</div>
            <el-tooltip effect="dark" placement="top">
              <div slot="content" style="max-width: 300px">{{currentMeetingDetail.doctorInfoStr}}</div>
              <div class="form-content">
                {{ currentMeetingDetail.doctorInfoStr }}
              </div>
            </el-tooltip>
          </div>
          <div class="row-form-item">
            <div class="form-label">哈瑞特参会人:</div>
            <el-tooltip effect="dark" placement="top">
              <div slot="content" style="max-width: 300px">{{currentMeetingDetail.userInfoStr}}</div>
              <div class="form-content">
                {{ currentMeetingDetail.userInfoStr }}
              </div>
            </el-tooltip>
          </div>
        </div>
        <div class="detail-label">会议证明</div>
        <div class="image-content">
          <div class="img-box" v-for="(url,index) in curImgList" :key="index">
            <el-image
              style="width: 80px; height: 80px"
              :preview-src-list="curImgList"
              :src="url"
              :fit="fit"></el-image>
          </div>
        </div>
      </div>
    </CommonDialog>
  </div>
</template>

<script>
import CommonDialog from "../../../../components/CommonDialog.vue";
import {meetingStatusMap,meetingTypeMap} from "../../constant/meetingEnum";
export default {
  name: "MeetingDetailsDialog",
  components:{CommonDialog},
  props: {
    visible: {
      require: true,
      type:Boolean
    },
    title: {
      require: true,
      type:String
    },
    currentMeetingDetail:{
      require:true,
      type:Object,
      default: ()=>{
        return{
          meetingType:'',
          name:'',
          hospitalName:'',
          marketName:'',
          doctorMeetingList:[],
          userList:[],
          startTime:'',
          endTime:'',
          budgetingPlan:'',
          status:'',
          sitePhotosList:[],
          meetingAttachmentList: [],
          signInSheetList: []
        }
      }
    }
  },
  data(){
    return{
      meetingStatusMap,
      meetingTypeMap,
    }
  },
  computed: {
    curImgList() {
      const { sitePhotosList = [], meetingAttachmentList = [], signInSheetList = [] } = this.currentMeetingDetail;
      return [...sitePhotosList, ...meetingAttachmentList, ...signInSheetList]
    }
  },
  methods:{
    cancel(){
      this.$emit('update:visible', false)
    },
    confirm(){

    },
  }
}
</script>

<style scoped lang="less">
.meeting-details-wrapper{
  width: 100%;
  box-sizing: border-box;
  padding: 24px 84px;
  text-align: left;
  color: #15233F;
  overflow-y: auto;
  .detail-label{
    font-weight: bold;
    font-size: 16px;
    color: #15233F;
    margin-bottom: 16px;
  }
  .top-content{
    display: grid;
    grid-template-columns: 1fr 1fr ;
    grid-row-gap: 12px;
    margin-bottom: 16px;
  }
  .middle-content{
    margin-bottom: 16px;
  }
  .image-content{
    display: flex;
    flex-wrap: wrap;
    .img-box{
      width: 80px;
      height: 80px;
      border-radius: 6px;
      margin-right: 16px;
      .el-image{
        border-radius: 6px;
      }
    }
  }
}
.row-form-item{
  display: flex;
  align-items: center;
}
.row-form-item:not(:last-child){
  margin-bottom: 12px;
}
.form-item{
  display: flex;
  align-items: center;
  max-width: 400px;
}
.form-label{
  width: 120px;
  font-weight: 400;
  font-size: 14px;
  color: #939CAE;
}
.form-content{
  flex: 1;
  font-weight: 400;
  font-size: 14px;
  color: #3A4762;
  margin-left: 20px;
  white-space: nowrap; /* 保证文本在一行内显示 */
  overflow-x: hidden; /* 隐藏溢出的内容 */
  text-overflow: ellipsis;
  flex-wrap: nowrap;
}
</style>
