<!-- 问卷管理 -->
<template>
  <div class="drug-storage">
    <div class="preview">
      <div class="remind">
        <img src="@/assets/image/notice.png" alt="" />
        <span>完成问卷后会有专业的医师来为您评估～</span>
      </div>

      <div class="li" v-if="currQuestionTest">
        <p
          class="question"
          v-if="
            currQuestionTest.question_type == 1 ||
              currQuestionTest.question_type == 2
          "
        >
          {{ currIndex + 1 }}、{{ currQuestionTest.question_content }}。
          <span v-show="currQuestionTest.question_type == '2'">
            (可多选)
          </span>
        </p>
        <!-- 单项填空题 -->
        <div class="itemLeft4" v-if="currQuestionTest.question_type == 3">
          <span
            >{{ currIndex + 1 }}、{{
              currQuestionTest.question_content[0]
                ? currQuestionTest.question_content[0]
                : currQuestionTest.question_content
            }}
            <span class="blanksAnswer" v-show="currQuestionTest.answers">
              ({{ currQuestionTest.answers[0].content }})
            </span>
            {{
              currQuestionTest.question_content[1]
                ? currQuestionTest.question_content[1]
                : ""
            }}。
          </span>
        </div>

        <!-- 多项填空题 -->
        <div class="itemLeft3" v-if="currQuestionTest.question_type == 8">
          <span>{{ currIndex + 1 }}、</span>
          <span v-for="(ite, i) in currQuestionTest.question_content" :key="i">
            <span
              class="blanks"
              v-if="
                i > 0 &&
                  currQuestionTest.answers &&
                  currQuestionTest.answers.length > 0
              "
            >
              ({{
                currQuestionTest.answers[i - 1]
                  ? currQuestionTest.answers[i - 1].content
                  : ""
              }})
            </span>
            <span
              v-if="i < currQuestionTest.question_content.length"
              class="and"
              >{{ ite }}</span
            >
          </span>
          。
        </div>
        <!-- 单选题 -->
        <div class="answer-label" v-if="currQuestionTest.question_type == 1">
          <template v-for="item in currQuestionTest.answers">
            <el-button
              v-if="item.content"
              style="width:100%;margin: 10px 0;"
              @click="selectInfo(item)"
              :key="item"
            >
              {{ item.content }}
            </el-button>
          </template>
        </div>
        <!-- 多选题 -->
        <div
          class="answer-label"
          v-else-if="currQuestionTest.question_type == 2"
        >
          <el-checkbox-group v-model="checkbox" class="group">
            <el-checkbox-button
              style="width:100%;margin: 10px 0;"
              v-for="item in currQuestionTest.answers"
              :label="item.answer_id"
              :key="item.content"
            >
              {{ item.content }}
            </el-checkbox-button>
          </el-checkbox-group>
        </div>
      </div>
      <div class="change-btn">
        <el-button type="primary" @click="goLastProblem" v-if="currIndex > 0"
          >上一题</el-button
        >
        <el-button
          type="primary"
          v-show="currIndex != showQs.length - 1"
          @click="goNextProblem"
          >下一题</el-button
        >
        <el-button type="primary" @click="goBack">返回</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { httpReq } from "@/http";
export default {
  components: {},
  data() {
    return {
      currIndex: 0,
      choice: {
        answer_id: "",
        content: ""
      },
      checkbox: [],
      currQuestion: { question_type: 0 },
      currQuestionList: [],
      allQuestion: [],

      fatherQs: [],
      childrenIds: [],
      showQs: [],
      currIndex: 0
    };
  },
  computed: {
    currQuestionTest() {
      console.log("////", this.showQs[this.currIndex]);
      return this.showQs[this.currIndex];
    }
  },
  watch: {},
  created() {
    // this.getAllTypeList();
    // this.getQuestionList(this.currentPage, this.pageSize, this.searchFrom);
  },
  mounted() {
    if (this.$route.query.questionnaireId) {
      httpReq({
        url: "/questionnaire/previewQuestionnaire",
        method: "post",
        data: {
          questionnaireId: this.$route.query.questionnaireId
        }
      }).then(res => {
        //题目关联
        res.data = res.data.map(v => {
          v.question_content =
            v.question_type == 8 || v.question_type == 3
              ? v.question_content.split("$$")
              : v.question_content;
          return v;
        });
        // console.log(res.data);
        //获取是否含有关联问题
        let isRelationIQs = res.data.some(re => re.relation);
        // console.log("----isRelationIQs", isRelationIQs);
        if (isRelationIQs) {
          // 筛选出被关联的问题（所有子问题）
          for (let i = 0; i < res.data.length; i++) {
            const el = res.data[i];
            if (el.relation) {
              let childItem = el.relation.map(v => {
                let itemInfo = res.data.find(
                  re => re.question_id == v.relation_question_id
                );
                // 给子问题添加标识
                itemInfo["isChildren"] = true;
                return itemInfo;
              });
              this.childrenIds = [...this.childrenIds, ...childItem];
            }
          }
          // 筛选出所有一级问题
          this.fatherQs = JSON.parse(JSON.stringify(res.data));
          for (let i = 0; i < this.childrenIds.length; i++) {
            for (let j = 0; j < this.fatherQs.length; j++) {
              if (
                this.childrenIds[i].question_id == this.fatherQs[j].question_id
              ) {
                // console.log(this.childrenIds[i].question_id);
                this.fatherQs.splice(j, 1);
              }
            }
          }
          // console.log("///this.childrenIds", this.childrenIds, this.fatherQs);
          this.showQs = JSON.parse(JSON.stringify(this.fatherQs));
        } else {
          // 筛选出所有一级问题
          this.fatherQs = JSON.parse(JSON.stringify(res.data));
          this.showQs = JSON.parse(JSON.stringify(this.fatherQs));
        }
      });
    }
  },
  methods: {
    //单选框选择
    selectInfo(item) {
      if (this.showQs[this.currIndex].question_type == 1) {
        console.log("单选");
        this.showQs[this.currIndex]["answer_id"] = item.answer_id;
      }
      if (this.showQs[this.currIndex].question_type == 2) {
        console.log("多选");
        let isHaveIndex = this.showQs[this.currIndex].answer_id.findIndex(
          re => re == item.answer_id
        );
        if (isHaveIndex !== -1) {
          this.showQs[this.currIndex].answer_id.splice(isHaveIndex, 1);
        } else {
          this.showQs[this.currIndex].answer_id.push(item.answer_id);
        }
      }

      let sourceArr = JSON.parse(JSON.stringify(this.fatherQs));
      // 当前问题在源数据中的索引值
      let sourceQuestionIndex = this.fatherQs.findIndex(re => {
        return re.question_id == this.showQs[this.currIndex].question_id;
      });
      // 跳题逻辑
      let jumpInfo =
        this.currQuestionTest.jump &&
        this.currQuestionTest.jump.find(re => re.answer_id == item.answer_id);
      console.log("///", jumpInfo, this.currQuestionTest, item);
      // 选中了需要跳转的答案选项
      if (jumpInfo && jumpInfo.jump_id > 0) {
        this.showQs = [...this.showQs.slice(0, this.currIndex + 1)];
        // 需要跳转问题
        let jumpIdIndex = this.fatherQs.findIndex(re => {
          return re.question_id == jumpInfo.jump_id;
        });
        console.log(
          "跳转问题id：",
          jumpInfo.jump_id,
          "源数据中跳转问题的索引",
          jumpIdIndex
        );
        this.showQs = [
          ...this.showQs.slice(0, i + 1),
          ...sourceArr.slice(jumpIdIndex, this.fatherQs.length)
        ];
      } else if (jumpInfo && jumpInfo.jump_id == 0) {
        // 选中了需要跳转的答案选项,且直接结束问卷
        this.showQs = [...this.showQs.slice(0, this.currIndex + 1)];
      } else if (
        this.showQs[this.currIndex]["answer_id"] &&
        this.currQuestionTest.jump &&
        !jumpInfo
      ) {
        console.log(
          "重新选择了答案，不用跳转问题,需要恢复正常问题序号",
          sourceQuestionIndex
        );
        this.showQs = [
          ...this.showQs.slice(0, this.currIndex + 1),
          ...sourceArr.slice(sourceQuestionIndex + 1, sourceArr.length)
        ];
      }
      // 关联问题逻辑,需要插入子问题
      let relationInfo =
        this.currQuestionTest.relation &&
        this.currQuestionTest.relation.find(
          re => re.answer_id == item.answer_id
        );
      console.log(relationInfo);
      let isHaveChild =
        relationInfo &&
        this.showQs.some(
          re => re.question_id == relationInfo.relation_question_id
        );
      console.log(isHaveChild);
      if (relationInfo && !isHaveChild) {
        let childInfo = this.childrenIds.find(
          re => re.question_id == relationInfo.relation_question_id
        );
        this.showQs.splice(this.currIndex + 1, 0, childInfo);
      }

      console.log(this.showQs);
      console.log(this.showQs[this.currIndex].answer_id);
    },
    //返回上一页
    goBack() {
      this.$router.push("/index/LibraryquestionaireManagement");
    },
    goLastProblem() {
      console.log("///---------");
      this.currIndex--;
    },
    goNextProblem() {
      console.log("///++++++");
      this.currIndex++;
    }
  }
};
</script>
<style lang="less" scoped>
.drug-storage {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  .preview {
    width: 500px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    margin: 10px 24px 20px;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    .remind {
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 45px;
        height: 45px;
      }
      span {
        font-size: 20px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
      }
    }
    .li {
      .question {
        text-align: left;
      }
      .answer-label {
        display: flex;
        flex-direction: column;
      }
      .answer-label /deep/ .el-radio-button__inner,
      .answer-label /deep/ .el-radio-button:first-child .el-radio-button__inner,
      .answer-label /deep/ .el-checkbox-button__inner,
      .answer-label
        /deep/
        .el-checkbox-button:first-child
        .el-checkbox-button__inner {
        border: 1px solid #dcdfe6;
        border-radius: 4px !important;
        margin: 5px 0;
        width: 100%;
      }
      /deep/.el-checkbox-button__inner:hover {
        color: #409eff;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }
      /deep/.el-checkbox-button.is-checked .el-checkbox-button__inner {
        color: #409eff;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }
      .answer-label
        /deep/
        .el-radio-button__orig-radio:checked
        + .el-radio-button__inner {
        background: #f5f5f5;
      }

      .itemLeft3 {
        font-size: 18px;
        margin-top: 20px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #000000;
        .blanks {
          display: inline-block;
          min-width: 42px;
          border-bottom: 1px solid #000;
          text-align: center;
        }
        .and {
          display: inline-block;
          margin: 0 2px;
        }
      }
      .itemLeft4 {
        font-size: 18px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #000000;
        display: flex;
        margin-top: 20px;
        .blanksAnswer {
          margin-left: 5px;
          width: 690px;
          border-bottom: 1px solid #000;
          margin-right: 3px;
        }
      }
    }
    .change-btn {
      margin: 10px 0;
      display: flex;
      flex-direction: column;
      .el-button {
        margin-left: 0;
        margin-top: 10px;
      }
    }
  }
}
</style>
