<!-- 新增问题 -->
<template>
  <div class="add-issue">
    <div class="box select-box">
      <el-row align="top">
        <div
          @click="showBtn(1, 1)"
          @mousemove="showBack(1)"
          @mouseleave="noBack()"
          class="list-left"
          :class="type == 1 || showBacks == 1 ? 'activeClass1' : 'activeClass2'"
        >
          单选
        </div>
        <div
          @click="showBtn(2, 2)"
          @mousemove="showBack(2)"
          @mouseleave="noBack()"
          class="list-left"
          :class="type == 2 || showBacks == 2 ? 'activeClass1' : 'activeClass2'"
        >
          多选
        </div>
        <div
          @click="showBtn(3, 3)"
          class="list-left"
          @mouseleave="noBack()"
          @mousemove="showBack(3)"
          :class="type == 3 || showBacks == 3 ? 'activeClass1' : 'activeClass2'"
        >
          填空
        </div>
        <div
          @click="showBtn(4, 8)"
          @mouseleave="noBack()"
          class="list-left"
          @mousemove="showBack(4)"
          :class="type == 4 || showBacks == 4 ? 'activeClass1' : 'activeClass2'"
        >
          多项填空
        </div>
      </el-row>
    </div>
    <div class="content-box">
      <!-- 单选 -->
      <el-form
        class="table-box"
        :model="addForm"
        ref="rules"
        label-width="110px"
        size="mini"
        style="width:100%"
      >
        <el-form-item label="请选择问题分类" prop="type" class="float-left">
          <el-select
            size="mini"
            multiple
            v-model="addForm.questionnaireTypeIds"
            filterable
            :disabled="this.$route.query.goType == 1 ? true : false"
            placeholder="请选择问题分类"
          >
            <el-option
              :label="item.TypeName"
              :value="item.questionnaireTypeId"
              v-for="item in allClass"
              :key="item.questionnaireTypeId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="float" v-show="special">
          <el-select
            v-model="ruleName"
            filterable
            @change="selectRule"
            size="mini"
            placeholder="请选择选项"
          >
            <el-option
              :label="item.name"
              :value="item.ruleId"
              v-for="item in rule"
              :key="item.ruleId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="float" v-show="special">
          <el-checkbox
            v-model="checked"
            @change="useApplication"
            style="margin-right:20px;"
            >应用特殊计算规则</el-checkbox
          >
        </el-form-item>
        <el-form-item class="title-input" prop="name">
          <el-input
            type="textarea"
            placeholder="请输入标题"
            maxlength="100"
            show-word-limit
            v-model="addForm.questionContent"
            :rows="3"
          >
          </el-input>
        </el-form-item>
        <el-form-item class="must">
          <el-checkbox v-model="addForm.required" style="margin-left:20px"
            >必填</el-checkbox
          >

          <el-button
            type="primary"
            v-show="type == 3 || type == 4"
            @click="inputContent"
            >插入填空符</el-button
          >
        </el-form-item>
        <el-form-item class="must input-content">
          <el-table
            :data="addForm.answerList"
            ref="multipleTable"
            v-if="type == 2 || type == 1"
            max-height="520px"
            class="contact-table"
            @selection-change="handleSelectionChange"
            style="width:70%;margin-left: 20px;"
          >
            <el-table-column type="selection" width="30"></el-table-column>
            <el-table-column prop="date">
              <template slot-scope="scope">
                <el-input
                  style="width: 50%;"
                  v-model="scope.row.answerContent"
                  placeholder="请输入选项"
                  size="mini"
                ></el-input>
                <span style="color:red" v-show="scope.row.isShow"
                  >(正确答案)</span
                >
              </template>
            </el-table-column>

            <el-table-column prop="highStake" label="高风险项">
              <template slot-scope="scope">
                <el-checkbox
                  v-model="scope.row.isRisk"
                  style="margin-left:25px"
                ></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="新增" width="60">
              <template slot-scope="scope">
                <el-button
                  icon="el-icon-plus"
                  @click="addSelect(scope)"
                  circle
                ></el-button>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="删除" width="100">
              <template slot-scope="scope">
                <el-button
                  icon="el-icon-minus"
                  @click="delSelect(scope)"
                  circle
                ></el-button>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="上移" width="60">
              <template slot-scope="scope">
                <el-button
                  :disabled="scope.$index === 0"
                  icon="el-icon-top"
                  @click="moveUp(scope.$index, scope.row)"
                  circle
                ></el-button>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="下移" width="60">
              <template slot-scope="scope">
                <el-button
                  :disabled="scope.$index === addForm.answerList.length - 1"
                  icon="el-icon-bottom"
                  @click="moveDown(scope.$index, scope.row)"
                  circle
                ></el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-input
            v-if="type == 3"
            type="textarea"
            :rows="3"
            maxlength="100"
            placeholder="请输入填写提示"
            v-model="addForm.answerList[0].answerContent"
          >
          </el-input>
          <template v-if="type == 4">
            <el-input
              type="textarea"
              :rows="3"
              maxlength="100"
              v-for="(item, index) in addForm.answerList"
              :key="index"
              style="margin-bottom:20px"
              placeholder="请输入填写提示"
              v-model="item.answerContent"
            >
            </el-input>
            <el-button
              style="border:0"
              @click="addInput"
              icon="el-icon-circle-plus-outline"
              circle
              >添加</el-button
            >
            <el-button
              style="border:0"
              @click="removeInput"
              icon="el-icon-remove-outline"
              circle
              >删除</el-button
            >
          </template>
        </el-form-item>
        <el-form-item class="showComputed" v-show="showCompute">
          <span>{{ ruleShowName }}:</span>
          <el-select
            v-model="contentOne"
            style="margin-left:10px"
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in addForm.answerList"
              :key="index"
              :label="item.answerContent"
              :value="item.answerContent"
            >
            </el-option>
          </el-select>
          <span>/</span>
          <el-select v-model="contentTwo" placeholder="请选择">
            <el-option
              v-for="(item, index) in addForm.answerList"
              :key="index"
              :label="item.answerContent"
              :value="item.answerContent"
            >
            </el-option>
          </el-select>
          <span class="multiple">2</span>
        </el-form-item>
      </el-form>
      <div class="btn-box">
        <el-button type="primary" @click="addInfo(addForm)">完成</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { httpReq } from "@/http";
export default {
  components: {},
  data() {
    return {
      hintOne: "",
      type: 1, //题目类型
      checked: false,
      correct: null,
      showCompute: false,
      showBacks: 0,
      ruleName: "",
      ruleShowName: "",
      rules: {
        type: [{ required: true, message: "请选择问题分类", trigger: "blur" }],
        name: [{ required: true, message: "请输入标题", trigger: "blur" }]
      },
      rule: [],
      addForm: {
        questionnaireTypeIds: [], //问卷分类id集合
        questionType: 1, //问题类型
        questionContent: "", //问题题目
        required: "", //是否必填 0非必填 1必填
        questionMath: "", //计算公式   @@?,##/,##(,@@?,##*,@@?,##)     每个？代表每个选项
        answerList: [
          {
            answerContent: "",
            isRisk: false,
            isShow: false,
            correct: 0 //是否为正确 1正确 0或null错误
          },
          {
            answerContent: "",
            isRisk: false,
            isShow: false,
            correct: 0 //是否为正确 1正确 0或null错误
          }
        ]
      },
      contentOne: "",
      contentTwo: "",
      multipleTable: [],
      allClass: [],
      special: false
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getAllTypeList();
  },
  mounted() {},
  methods: {
    inputContent() {
      this.addForm.questionContent = this.addForm.questionContent + "$$";
    },
    addInput() {
      this.addForm.answerList.push({
        answerContent: "",
        isRisk: false,
        isShow: false,
        correct: 0 //是否为正确 1正确 0或null错误
      });
    },
    removeInput() {
      if (this.addForm.answerList.length == 1) {
        this.$message({
          message: "不能删除最后一个选项",
          center: true,
          type: "error"
        });
      } else {
        this.addForm.answerList.pop();
      }
    },
    //应用特殊计算规则
    useApplication(val) {
      if (val) {
        this.getRule();
      } else {
        this.rule = [];
        this.ruleName = "";
        this.ruleShowName = "";
        this.showCompute = false;
      }
    },
    selectRule(val) {
      this.rule.forEach(el => {
        this.ruleShowName = el.ruleId == this.ruleName ? el.name : "";
      });
      this.showCompute = true;
      console.log(val, "val的值");
    },
    handleSelectionChange(val) {
      this.addForm.answerList.forEach(item => {
        item.isShow = false;
        item.correct = 0;
      });
      if (this.type == 2) {
        val.forEach(el => {
          el.isShow = true;
        });
        return;
      }
      if (val.length > 1) {
        this.$refs.multipleTable.clearSelection(); //清空列表的选中
        this.$refs.multipleTable.toggleRowSelection(val[val.length - 1]); //只显示选中最后一个 这时val还是多选的列表(就是你选中的几个数据)
      } else if (val.length === 1) {
        this.multipleTable = val[val.length - 1];
        val[0].isShow = true;
        val[0].correct = 1;
      } else {
        this.multipleTable = []; //this.multipleTable是选中行的最后一条数据
      }
    },

    //新增问题
    addInfo(info) {
      this.$refs["rules"].validate(valid => {
        if (valid) {
          console.log("submit!!");
          let newArr = JSON.parse(JSON.stringify(info.answerList));
          newArr.forEach(el => {
            delete el.isShow;
          });
          newArr;
          let questionMath = "";
          let oneSelect = -1;
          let twoSelect = -1;
          info.answerList.forEach((item, index) => {
            if (this.contentOne && this.contentOne == item.answerContent) {
              oneSelect = index + 1;
            }
            if (this.contentTwo && this.contentTwo == item.answerContent) {
              twoSelect = index + 1;
            }
          });
          if (oneSelect > 0 && twoSelect > 0) {
            questionMath = `@@${oneSelect},##/,##(,@@${twoSelect},##*,@@2,##)`;
          }
          let data = {
            questionnaireTypeIds: info.questionnaireTypeIds, //问卷分类id集合
            questionType: info.questionType, //问题类型
            questionContent: info.questionContent, //问题题目
            required: info.required ? 1 : 0, //是否必填 0非必填 1必填
            questionMath: questionMath, //计算公式   @@?,##/,##(,@@?,##*,@@?,##)     每个？代表每个选项
            answerList: newArr
          };
          console.log(data, "@@1,##/,##(,@@2,##*,@@2,##)");
          httpReq({
            url: "/question/type/addQuestionByQuestionnaireTypeId",
            method: "post",
            data: data
          })
            .then(res => {
              if (this.$route.query.goType == 2) {
                this.$router.push({
                  path: "/index/LibrarytopicManagement",
                  query: {
                    questionnaireTypeId: this.$route.query.questionnaireTypeId
                  }
                });
              } else {
                this.$router.push({
                  path: "/index/LibraryIssueManagement",
                  query: {
                    questionnaireTypeId: this.$route.query.questionnaireTypeId
                  }
                });
              }

              this.$message.success("新增成功");
              console.log(res, "dsdsdsssssssssssss");
            })
            .catch(err => {
              console.log(err);
              this.$message.error(`失败：${err.msg}`);
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //查询计算规则
    getRule() {
      httpReq({
        url: "/question/type/getQuestionRule",
        method: "post"
      }).then(res => {
        this.rule = res.data;
      });
    },
    //获取所有问卷分类
    getAllTypeList() {
      httpReq({
        url: "/questionnaire/getAllTypeList",
        method: "post"
      }).then(res => {
        this.allClass = res.data;
        if (this.$route.query.goType == 1) {
          this.addForm.questionnaireTypeIds = [
            this.$route.query.questionnaireTypeId - 0
          ];
        }
      });
    },
    //切换问题类型
    showBtn(type, info) {
      this.type = type;
      this.special = type !== 3 && type !== 1;
      this.addForm.questionType = info;
      // this.addForm.questionContent = "";
      // this.addForm.questionnaireTypeIds = [];
      // this.addForm.required = "";
      this.addForm.questionMath = "";
      this.checked = false;
      this.rule = [];
      if (this.type == 3 || this.type == 4) {
        this.addForm.answerList = [
          {
            answerContent: "",
            isRisk: false,
            isShow: false,
            correct: 0 //是否为正确 1正确 0或null错误
          }
        ];
      }

      this.showCompute = false;
      this.ruleName = "";
      this.ruleShowName = "";
      this.contentOne = "";
      this.contentTwo = "";
    },
    //切换问题类型
    showBack(type) {
      this.showBacks = type;
    },
    //切换问题类型
    noBack() {
      this.showBacks = 0;
    },
    //新增选项
    addSelect(row) {
      var sss = this.addForm.answerList.splice(row.$index + 1, 0, {
        answerContent: "",
        isRisk: false,
        isShow: false,
        correct: 0 //是否为正确 1正确 0或null错误
      });
    },
    //删除选项
    delSelect(row) {
      if (this.addForm.answerList.length == 1) {
        this.$message({
          message: "不能删除最后一个选项",
          center: true,
          type: "error"
        });
      } else {
        this.addForm.answerList.splice(row.$index, 1);
      }
    },
    //向上移动
    moveUp(index, row) {
      var that = this;
      console.log("上移", index, row);
      if (index > 0) {
        let upDate = that.addForm.answerList[index - 1];
        that.addForm.answerList.splice(index - 1, 1);
        that.addForm.answerList.splice(index, 0, upDate);
      } else {
        alert("已经是第一条，不可上移");
      }
    },
    //向下移动
    moveDown(index, row) {
      var that = this;
      console.log("下移", index, row);
      if (index + 1 === that.addForm.answerList.length) {
        alert("已经是最后一条，不可下移");
      } else {
        console.log(index);
        let downDate = that.addForm.answerList[index + 1];
        that.addForm.answerList.splice(index + 1, 1);
        that.addForm.answerList.splice(index, 0, downDate);
      }
    }
  }
};
</script>
<style lang="less" scoped>
/deep/.el-table__cell {
  height: 16px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #000000;
  line-height: 16px;
}
.list-left {
  width: 100%;
  height: 30px;
  padding-top: 15px;
  vertical-align: baseline;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 20px;
}
.activeClass1 {
  background-color: #409eff;
  color: #fff;
}
.activeClass2 {
  background-color: #fff;
}
.input-content {
  /deep/.el-form-item__content {
    padding: 20px;
  }
}
.btn-box {
  text-align: right;
}
.add-issue {
  display: flex;

  .box {
    background: #fff;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    margin-bottom: 20px;
  }
  .select-box {
    width: 126px;
    margin: 10px 16px 20px 24px;
    // /deep/.el-button--default:hover {
    //   background: #0c88be;

    //   border-color: #c6e2ff;
    //   color: #ffffff;
    // }
    // /deep/.el-button--default:focus {
    //   background-color: #0c88be;
    //   color: #ffffff;
    // }
    // /deep/.el-button {
    //   margin: 0 !important;
    //   width: 100%;
    //   border-radius: 0;
    //   border: 0;
    //   font-size: 14px;
    //   font-family: PingFangSC-Regular, PingFang SC;
    //   font-weight: 400;
    //   //   color: #303133; //#0C88BE
    //   line-height: 20px;
    // }
  }
  .content-box {
    display: flex;
    flex-direction: column;
    flex: 1;
    margin: 10px 10px 20px 0px;
    .table-box {
      background: #fff;
      box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
      margin-bottom: 20px;
    }
    .float-left {
      float: left;
      padding: 20px 0 0 20px;
    }
    .float {
      float: right;
      padding: 20px 20px 0 0;
      /deep/ .el-form-item__content {
        margin: 0 !important;
      }
    }
    .title-input {
      display: flex;
      width: 100%;
      /deep/.el-form-item__content {
        width: 100%;
        margin-left: 0 !important;
        padding: 0 20px;
      }
    }
    .must {
      display: flex;
      width: 100%;
      /deep/ .el-table__row > td {
        /* 去除表格线 */
        border: none;
      }
      /deep/.el-table th.is-leaf {
        /* 去除上边框 */
        border: none;
      }
      /deep/ .el-table::before {
        /* 去除下边框 */
        height: 0;
      }
      /deep/.el-button--mini.is-circle {
        padding: 1px;
      }
      /deep/.el-radio {
        margin-right: 0;
      }
      /deep/.el-input__inner {
        border: 0;
        padding: 0;
      }
      /deep/.el-radio__label {
        padding: 0;
      }
      /deep/.is-circle {
        margin-left: 8px;
      }
      /deep/.el-form-item__content {
        width: 100%;
        margin-left: 0 !important;
        text-align: left;
      }
    }
  }
}
.contact-table {
  /deep/ .el-table__header-wrapper .el-table__header .el-checkbox {
    display: none;
  }
  /deep/.el-checkbox__inner {
    border-radius: 50%;
  }
}
.showComputed {
  text-align: left;
  /deep/.el-input__inner {
    border: 1;
    padding: 10px;
  }
  /deep/.el-form-item__content {
    margin-left: 20px !important;
  }
}
.multiple {
  font-size: 8px;
  position: absolute;
  margin: -10px 0 0 3px;
}
</style>
