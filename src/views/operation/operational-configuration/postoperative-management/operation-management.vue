<template>
<div class="main">
  <div class="content">
    <div class="content-title">
      <span>健康贴士(共{{total}}周)</span>
      <div class="addbtn" @click="addWeekTips">
        <i class="el-icon-circle-plus-outline"></i>
      </div>
    </div>
    <div class="weekgroups">
      <div class="arrowbtnleft arrow" @click="nextgroup(1)"><i class="el-icon-d-arrow-left"></i></div>
      <div class="weekgroups-box">
        <el-empty description="暂无内容,请添加内容!" v-if="weekTipsList.length===0" class="emptystatus"></el-empty>
        <div class="weekgroups-item" v-for="(item,index) in weekTipsList" :key="item" v-if="weekTipsList.length!==0">
          <div class="weekgroups-item-title">
            <span>术后第{{item[0]? item[0].week : newweek}}周</span>
            <i class="el-icon-delete" @click="deleTipsbyWeek(1,item)"></i>
          </div>
          <div class="weekgroups-item-content">
            <div class="weekgroups-item-content-card" v-for="(i,_index) in item" :key="i.id" v-if="item">
              <div class="weekgroups-item-content-card-text" >
                <el-popover
                  ref="popover"
                  placement="top"
                  title="详细信息"
                  width="300"
                  trigger="hover"
                  open-delay="1000"
                  :content="i.content"
                >
                  <span slot="reference">{{i.content}}</span>
                </el-popover>
              </div>
              <div class="weekgroups-item-content-card-editInfo">
                <div class="editbox-firstrow">
                  <span>术后第{{(i.week-1)*7+i.day}}天</span>
                  <span v-if="i.status==1">已上架</span>
                  <span v-if="i.status==0">已下架</span>
                </div>
                <div class="editbox-secondrow">
                  <span>创建人:{{i.creatorName}} {{timeMode(i.createTime).datestr}}</span>
                  <div>
                    <span @click="showDialog(2,i)" v-if="i.status==0">上架</span>
                    <span @click="showDialog(4,i)" v-if="i.status==1">下架</span>
                    <span @click="editTps(0,i)">编辑</span>
                    <span @click="showDialog(3,i)">删除</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="addbtn" @click="showDialogAdd(0,item )" v-if="item.length<7">
            <i class="el-icon-circle-plus-outline"></i>
            <span>添加</span>
          </div>
        </div>
      </div>

      <div class="arrowbtnright arrow"><i class="el-icon-d-arrow-right" @click="nextgroup(0)"></i></div>
    </div>
    <el-dialog
      :title="dialogContentType===0? '添加' : '温馨提示'"
      :visible.sync="centerDialogVisible"
      width="30%"
      @close="closeDialogContentType"
      center>
      <div style="text-align: center">
        <span v-if="dialogContentType===1">确定要删除第{{currentItem[0].week}}周健康小贴士吗?</span>
        <span v-if="dialogContentType===2">需要将该条健康小贴士上架吗?</span>
        <span v-if="dialogContentType===3">确定要删除该条健康小贴士吗?</span>
        <span v-if="dialogContentType===4">需要将该条健康小贴士下架吗?</span>
        <div v-if="dialogContentType===0" class="form_class" style="text-align: left">
          <el-form ref="addRulesForm" :model="addForm" label-width="120px" size="mini" :rules="addRules">
          <el-form-item label="内容" prop="content">
            <el-input v-model="addForm.content" placeholder="请输入健康小贴士内容" type="textarea" :show-word-limit="true" :maxlength="200"></el-input>
          </el-form-item>
          <el-form-item label="日期" prop="day">
            <el-select v-model="addForm.day" placeholder="请选择天数">
              <el-option label="第一天" :value="1"></el-option>
              <el-option label="第二天" :value="2"></el-option>
              <el-option label="第三天" :value="3"></el-option>
              <el-option label="第四天" :value="4"></el-option>
              <el-option label="第五天" :value="5"></el-option>
              <el-option label="第六天" :value="6"></el-option>
              <el-option label="第七天" :value="7"></el-option>
            </el-select>
          </el-form-item>
          </el-form>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
    <el-button @click="centerDialogVisible = false" v-if="dialogContentType!==0">取 消</el-button>
    <el-button type="primary" @click="operationConfirm" v-if="dialogContentType!==0">确 定</el-button>
    <el-button  @click="addConfirm(0)" v-if="dialogContentType===0">保存</el-button>
    <el-button type="primary" @click="addConfirm(1)" v-if="dialogContentType===0">发布</el-button>
  </span>
    </el-dialog>
  </div>
</div>
</template>

<script>
import { httpReq } from "@/http";
import { timeMode } from "@/util/util";
import 'animate.css'
export default {
  name: "operation-management",
  data(){
    return{
      timeMode,
      currentPage: 1,
      total: 0,
      centerDialogVisible:false,
      dialogContentType:0,
      addForm:{
        content:'',
        week:'',
        status:"",
        day:1,
        id:""
      },
      currentItem:'',
      addRules: {
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' },
        ],
        day:[
          { required: true, message: '请选择日期', trigger: 'change' }
        ]
      },
      weekTipsList:[],
      newweek:'',
      empty:[

      ],
      maxWeek:"",
      operationType:0,
      deleWeekTips:[],
      operateStatus:'',
    }
  },
  created() {
    this.getHealthTipsList(this.currentPage)
  },
  methods:{
    // 切换每页条数
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.pageSize = val;
      this.getSellerList(1, this.pageSize, this.searchFrom);
    },
    // 切换页码
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.getSellerList(val, this.pageSize, this.searchFrom);
    },
    showDialog(type,item){
      this.dialogContentType=type
      this.centerDialogVisible = true
      this.operationType = 0
      this.currentItem = item
      //上架下架操作状态
      if (type==2){
        this.operateStatus = 1
      }
      if (type==4){
        this.operateStatus = 0

      }
    } ,
    showDialogAdd(type,item){
      console.log('456456456')
      console.log(item,'=============当前周数据')
      this.dialogContentType=type
      this.centerDialogVisible = true
      this.operationType = 0
      if (item.length==0){
        this.addForm.week = this.maxWeek+1
      }else {
        this.addForm.week = item[0].week
      }
    },
    //操作确认
    operationConfirm(){
      const type = this.dialogContentType
      switch (type){
        case 1 :
          console.log('执行删除第一周健康小贴士')
          this.deleTips(this.deleWeekTips)
          break
        case 2:
          console.log('执行上架小贴士')
          this.centerDialogVisible = false
          this.onShelfTips(this.currentItem.id,this.operateStatus)
          break
        case 3:
          console.log('执行删除该条健康小贴士')
          this.deleTips([this.currentItem.id])
          this.centerDialogVisible = false
          break
        case 4:
          console.log('执行下架小贴士')
          this.onShelfTips(this.currentItem.id,this.operateStatus)
          this.centerDialogVisible = false
          break

      }
    },
    //添加和保存操作 type 0 为保存 1为保存切发布
    addConfirm(type){
      this.$refs.addRulesForm.validate((valid)=>{
        console.log(valid)
        if (valid){
          console.log('验证通过')
          if (type==0){
            console.log('保存')
            if (this.operationType===0){
              this.addTips(0,this.addForm.week)
            }else {
              this.eddTips(0,this.addForm.week)
            }
          }
          if (type==1){
            console.log('保存切发布')
            const week = this.newweek? this.newweek : this.addForm.week
            if (this.operationType===1){
              this.eddTips(1,this.addForm.week)
            }else {
              this.addTips(1,this.addForm.week)
            }
          }
        }else {
          return  this.$message.error('请填写完整');
        }
      })
    },
    //获取健康小贴士列表
    getHealthTipsList(page){
      console.log(page)
      httpReq({
        url: "/operations/getList",
        method: "post",
        data:{
          page:page
        }
      }).then((res) => {
        console.log(res)
        const {page,records,maxWeek,...obj} =res.data
        console.log(obj)
        this.addForm.week = ""
        this.maxWeek = maxWeek
        this.total=maxWeek
        this.weekTipsList=[]
        for (let i in obj){
          this.weekTipsList.push(obj[i])
        }
        console.log(this.weekTipsList)
      });
    },
    //编辑健康小贴士
    editTps(type,item){
      this.dialogContentType=type
      console.log(item)
      this.centerDialogVisible = true
      this.addForm.content = item.content
      this.addForm.day = item.day
      this.addForm.week = item.week
      this.addForm.id = item.id
      this.newweek = ""
      this.operationType = 1

    },
    closeDialogContentType(){
      this.addForm.content = ""
      this.addForm.day = 1
    },
    //切换下一组
    nextgroup(nextgroup){
      if (nextgroup==1){
        this.currentPage=this.currentPage-1
        if (this.currentPage==0){
          this.currentPage = 1
           this.$message.error('已经是第一页了')
        }
        this.getHealthTipsList(this.currentPage)
      }
      if (nextgroup==0){
        this.currentPage=this.currentPage+1
        let page = this.maxWeek%4
        let _page = Math.floor(this.maxWeek/4)
        _page = page? _page+1 : _page
        console.log(_page)
        if (this.currentPage>_page){
          this.currentPage = _page
          this.$message.error('我也是有底线的!')
        }else {
          this.getHealthTipsList(this.currentPage)
        }
      }
    },
    //添加周
    addWeekTips(){

      let isEmpty
      if (this.weekTipsList.length===0){
        isEmpty = true
      }else {
        this.weekTipsList.forEach(item=>{
          if (item.length===0){
            isEmpty = false
          }else {
            isEmpty = true
          }
        })
      }
      if (isEmpty){
        this.newweek = this.maxWeek+1
        if (this.weekTipsList.length==4) {
          this.weekTipsList=[[]]
          let page = this.maxWeek%4
          let _page = Math.floor(this.maxWeek/4)
          _page =  _page+1
          this.currentPage=_page
        }else {
          this.weekTipsList.push([])
        }
      }else {
        this.$message.error('已经存在空模板');
      }

      console.log(this.weekTipsList)
    },
    addTips(type,week){
      console.log(this.operationType)
      console.log(week,'=========添加时的参数===========')
      httpReq({
        url: "/operations/addContent",
        method: "post",
        data:{
          content:this.addForm.content,
          week:week,
          status:type,
          day:this.addForm.day
        }
      }).then((res) => {
        console.log(res)
        this.centerDialogVisible = false
        this.getHealthTipsList(this.currentPage)
      }).catch(error => {
        console.log(error)
        this.$message.error(`添加失败:${error.msg}`)
      });
    },
    eddTips(type,week){
      httpReq({
        url: "/operations/updateContent",
        method: "patch",
        data:{
          content:this.addForm.content,
          week:week,
          status:type,
          day:this.addForm.day,
          id:this.addForm.id
        }
      }).then((res) => {
        console.log(res,'========================')
            this.$message.success('添加成功')
          this.centerDialogVisible = false
          this.getHealthTipsList(this.currentPage)

      }).catch(error=>{
        this.$message.error(`修改失败:${error.msg}`)
      })
    },
    //上架下架小贴士
    onShelfTips(id,status){
      console.log(id,status)
      httpReq({
        url: "/operations/updateStatus",
        method: "patch",
        data:{
          id:id,
          status:status,
        }
      }).then((res) => {
        console.log(res)
        this.$message.success('操作成功')
        this.centerDialogVisible = false
        this.getHealthTipsList(this.currentPage)
      }).catch(error=>{
        this.$message.error(`上架失败:${error.msg}`)
      });
    },
    //删除一周健康小贴士的数组处理
    deleTipsbyWeek(type,item){
      if (item.length!==0){
        this.currentItem = item
        this.deleWeekTips = item.map(res=>{
          return res.id
        })
        this.dialogContentType=type
        this.centerDialogVisible = true
        this.operationType = 0
      }else {
        this.weekTipsList.splice(this.weekTipsList.length-1, 1);
      }

      console.log(this.deleWeekTips)
    },
    //删除健康小贴士请求
    deleTips(ids){
      httpReq({
        url: "/operations/delete",
        method: "delete",
        data:{
          ids:ids,
        }
      }).then((res) => {
        console.log(res)
        this.$message.success('删除成功')
        this.centerDialogVisible = false
        this.getHealthTipsList(this.currentPage)
      }).catch(error=>{
        this.$message.error(`删除失败:${error.msg}`)
      });
    }
  }
}
</script>

<style scoped lang="less">
.main{
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.el-pagination {
  text-align: right;
  flex: 1;
  margin-top: 10px;
}
.content{
  background: #fff;
  margin: 0 24px;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
  margin-bottom: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  &-title{
    margin-bottom: 8px;
    text-align: left;
    border-left: 10px solid skyblue;
    display: flex;
    align-items: center;
    justify-content: space-between;
    span{
      display: block;
      margin-left: 10px;
    }
    .addbtn{
      margin-right: 60px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

  }
}
.weekgroups{
  height: 97%;
  box-sizing: border-box;
  position: relative;
}
.weekgroups-box{
  height: 100%;
  width: 100%;
  display: flex;
}
.weekgroups-item{
  height: 100%;
  width: 300px;
  border-top: 6px solid skyblue;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
  display: flex;
  flex-direction: column;
  margin-left: 80px;
}
.weekgroups-item-title{
  padding: 10px;
  position: relative;
  border-bottom: 1px solid #eeeeee;
  color: #3a3b45;
  i{
    position: absolute;
    right: 20px;
    top: 14px;
    color: skyblue;
  }
}
.weekgroups-item-content-card{
  height:130px;
  margin-bottom: 10px;
  border: 1px solid #eeeeee;
  border-radius: 4px;
  padding: 10px;
  box-sizing: border-box;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  &:hover {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);

  }
  .weekgroups-item-content-card-text{
    padding-bottom: 10px;
    border-bottom: 1px solid #eeeeee;
    height: 60px;

    span {
      text-align: left;
      overflow : hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      color:#606266;
    }
  }
  .weekgroups-item-content-card-editInfo{
    display: flex;
    flex-direction: column;
    font-size: 14px;
    color: skyblue;
    margin-top: 4px;
  }
}
.weekgroups-item-content{
  padding: 10px;
  height: 630px;
  //实现隐藏滚动条但是能垂直滚动
  overflow-y: auto;
  overflow-x: hidden;
  // IE
  &::-webkit-scrollbar {
    display: none;
  }
  // Chrome
  -ms-overflow-style: none;
  // Firefox
  scrollbar-width: none;

}
.addbtn{
  font-size: 28px;
  margin-left: 20px;
  color: skyblue;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  span{
    font-size: 16px;
    margin-left: 10px;
  }
}
.weekgroups-item-content-card::-webkit-scrollbar {
  display: none;
}
//.warp-hiddenscroll{
//  overflow: hidden;
//  height: 630px;
//}
.editbox-firstrow{
  display: flex;
  justify-content: space-between;
  align-items: center;
  &>span:nth-child(2){
    font-size: 10px;
    color: #bbbbbb;
  }
}
.editbox-secondrow{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.editbox-secondrow>span{
  font-size: 10px;
  color: #bbbbbb;
}
.editbox-secondrow>div{
  display: flex;
  justify-content: space-between;
}
.editbox-secondrow>div span{
  cursor: pointer;
  margin-left: 10px;
}
.el-textarea{
  width: 65%;
}
.el-input{
  width: 500px;

}
.form_class{
  /deep/.el-input__inner{
    width: 100%;
  }
}
.weekgroups{
  display: flex;
}
.arrowbtnleft{
  color: skyblue;
  font-weight: bold;
  font-size: 40px;
  margin-left: 20px;
  position: absolute;
  top: 50%;
  left: -10px;
}
.arrowbtnright{
  color: skyblue;
  font-weight: bold;
  font-size: 40px;
  margin-left: 20px;
  position: absolute;
  top: 50%;
  right: 10px;
}
.arrow{
  cursor: pointer;
}
/deep/.el-textarea__inner{
  height: 200px;
  min-height: 200px;
}
.emptystatus{
  margin: 0 auto;
}
</style>
