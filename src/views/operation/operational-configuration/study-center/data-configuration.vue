<!-- 问题问答页面 -->
<template>
  <div class="content">
    <div class="search box">
      <el-form
        ref="searchFrom"
        size="mini"
        :inline="true"
        :model="searchFrom"
        class="demo-form-inline"
      >
        <el-form-item label="标题">
          <el-input
            v-model.trim="searchFrom.title"
            placeholder="请输入标题"
          ></el-input>
        </el-form-item>
        <el-form-item label="类型" class="minclass">
          <el-select
            v-model="searchFrom.type"
            filterable
            placeholder="请选择类型"
          >
            <el-option
              v-for="(item, index) in contentTypeList"
              :key="index"
              :label="item.name"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="发布时间" class="minclass2">
          <el-date-picker
            v-model="searchFrom.publishTime"
            type="daterange"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            placeholder="请选择时间"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="创建人" class="minclass">
          <el-input
            v-model.trim="searchFrom.createName"
            placeholder="请输入创建人"
          ></el-input>
        </el-form-item>
        <el-form-item label="更新人" class="minclass">
          <el-input
            v-model.trim="searchFrom.updateName"
            placeholder="请输入更新人"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态" class="minclass">
          <el-select v-model="searchFrom.status" placeholder="状态">
            <el-option
              v-for="(item, index) in statusList"
              :key="index"
              :label="item.name"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="search-btn">
          <el-button type="primary" @click="onSubmit">查询</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="data box">
      <div class="add-btn">
        <el-button
          type="primary"
          size="mini"
          @click="
            (dialogFormVisibleEditBox = true),
              (dataId = ''),
              (dialogShowType = 0),
              (title = '新增资料配置')
          "
          >新增资料</el-button
        >
      </div>
      <div class="table-box">
        <div class="table">
          <el-table
            :data="tableData"
            border
            size="mini"
            style="width: 100%"
            max-height="500"
          >
            <el-table-column align="center" label="序号" width="60">
              <template slot-scope="scope">
                <!-- {{ total - (currentPage - 1) * pageSize - scope.$index }} -->
                {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column
              prop="title"
              align="center"
              label="标题"
              width="220"
            >
            </el-table-column>
            <el-table-column
              prop="type"
              align="center"
              label="类型"
              width="160"
            >
              <template slot-scope="scope">
                {{
                  scope.row.type == 0
                    ? "工作手册"
                    : scope.row.type == 1
                    ? "产品价值"
                    : scope.row.type == 2
                    ? "B端客户维护"
                    : scope.row.type == 3
                    ? "C端客户转化"
                    : scope.row.type == 4
                    ? "操作指南"
                    : scope.row.type == 5
                    ? "医学知识"
                    : scope.row.type == 6
                    ? "案例分享"
                    : scope.row.type == 7
                    ? "其他"
                    : ""
                }}
              </template>
            </el-table-column>
            <el-table-column prop="show_port" align="center" label="展示端口">
              <template slot-scope="scope">
                {{
                  scope.row.show_port == 2
                    ? "市场端"
                    : scope.row.show_port == 1
                    ? "健康顾问端"
                    : scope.row.show_port == 0
                    ? "市场端,健康顾问端"
                    : ""
                }}
              </template>
            </el-table-column>
            <el-table-column prop="createName" align="center" label="创建人">
            </el-table-column>
            <el-table-column
              prop="status"
              align="center"
              label="状态"
              width="120"
            >
              <template slot-scope="scope">
                {{
                  scope.row.status == 0
                    ? "未发布"
                    : scope.row.status == 1
                    ? "已发布"
                    : ""
                }}
              </template>
            </el-table-column>
            <el-table-column
              prop="publish_time"
              align="center"
              label="发布时间"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.publish_time">
                  {{ timeMode(scope.row.publish_time).dateMin }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              prop="updateName"
              align="center"
              label="最近更新人"
            >
            </el-table-column>
            <el-table-column
              prop="update_time"
              align="center"
              label="最近更新时间"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.update_time">
                  {{ timeMode(scope.row.update_time).dateMin }}
                </span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" align="center" label="操作">
              <template slot-scope="scope">
                <el-button
                  @click="lookDetail(scope.row)"
                  type="text"
                  size="mini"
                  >查看详情</el-button
                >
                <el-button
                  size="mini"
                  type="text"
                  v-if="scope.row.status == 1"
                  @click="unshelveShow(scope.row)"
                  >下架</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          :page-sizes="[10, 20, 50]"
          :page-size="pageSize"
          layout="total,sizes, prev, pager, next"
          :total="total"
          size="mini"
        >
        </el-pagination>
      </div>
    </div>

    <el-dialog
      :title="title"
      :visible.sync="dialogFormVisibleEditBox"
      width="600px"
      :close-on-click-modal="false"
      class="edit-content"
    >
      <EditInfo
        :id="dataId"
        :dialogShowType="dialogShowType"
        @updateList="updateList"
        v-if="dialogFormVisibleEditBox"
      ></EditInfo>
    </el-dialog>
    <el-dialog
      title="温馨提示"
      :visible.sync="dialogVisibleUnshelve"
      center
      width="30%"
    >
      <div style="text-align: center">
        <span>确定要下架该资料吗?</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleUnshelve = false">取 消</el-button>
        <el-button type="primary" @click="unshelve(dataId)">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { timeMode } from "@/util/util";
import { httpReq } from "@/http";
import EditInfo from "./components/add-datum.vue";
export default {
  components: { EditInfo },
  data() {
    return {
      dialogVisibleUnshelve: false,
      timeMode,
      searchFrom: {
        title: "",
        type: null,
        publishTime: [],
        createName: "",
        updateName: "",
        status: null
      },
      contentTypeList: [
        {
          value: 0,
          name: "工作手册"
        },
        {
          value: 1,
          name: "产品价值"
        },

        {
          value: 2,
          name: "B端客户维护"
        },
        {
          value: 3,
          name: "C端客户转化"
        },
        {
          value: 4,
          name: "操作指南"
        },
        {
          value: 5,
          name: "医学知识"
        },
        {
          value: 6,
          name: "案例分享"
        },
        {
          value: 7,
          name: "其他"
        }
      ],
      statusList: [
        {
          value: null,
          name: "全部"
        },
        {
          value: 0,
          name: "未发布"
        },
        {
          value: 1,
          name: "已发布"
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 1,
      tableData: [],
      dialogFormVisibleEditBox: false,
      dataId: "",
      title: "",
      dialogShowType: 0
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.getAnswerList(this.currentPage, this.pageSize, this.searchFrom);
  },
  methods: {
    updateList() {
      this.dialogFormVisibleEditBox = false;
      this.dataId = "";
      this.getAnswerList(this.currentPage, this.pageSize, this.searchFrom);
      console.log("updateList");
    },
    unshelveShow(val) {
      this.dialogVisibleUnshelve = true;
      this.dataId = val.data_id;
    },
    unshelve(dataId) {
      httpReq({
        url: "/study/operationOut",
        method: "post",
        data: {
          dataId
        }
      })
        .then(res => {
          this.dialogVisibleUnshelve = false;
          this.$message.success(`下架成功`);
          this.getAnswerList(this.currentPage, this.pageSize, this.searchFrom);
        })
        .catch(err => {
          this.dialogVisibleUnshelve = false;
          this.$message.error(`下架失败：${err.msg}`);
        });
    },
    // 获取资料列表
    getAnswerList(page, pageSize, info) {
      httpReq({
        url: "/study/data",
        method: "post",
        data: {
          title: info.title,
          sTime:
            info.publishTime && info.publishTime.length > 0
              ? info.publishTime[0] + " 00:00:00"
              : "",
          eTime:
            info.publishTime && info.publishTime.length > 0
              ? info.publishTime[1] + " 23:59:59"
              : "",
          createName: info.createName,
          updateName: info.updateName,
          type: info.type,
          status: info.status,
          page,
          pageSize
        }
      }).then(res => {
        this.tableData = res.data.data;
        this.pageSize = res.data.rows;
        this.total = res.data.records;
        this.currentPage = res.data.page;
      });
    },
    // 切换每页条数
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.pageSize = val;
      this.getAnswerList(1, this.pageSize, this.searchFrom);
    },
    // 切换页码
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.getAnswerList(val, this.pageSize, this.searchFrom);
    },
    onSubmit() {
      this.getAnswerList(1, this.pageSize, this.searchFrom);
    },
    //重置
    resetForm() {
      this.searchFrom = {
        title: "",
        type: null,
        publishTime: [],
        createName: "",
        updateName: "",
        status: null
      };
      this.getAnswerList(this.currentPage, this.pageSize, this.searchFrom);
    },
    // 点击查看
    lookDetail(row) {
      this.title = "编辑资料配置";
      this.dialogFormVisibleEditBox = true;
      this.dataId = row.data_id;
      this.dialogShowType = 1;
    }
  }
};
</script>
<style lang="less" scoped>
.content {
  text-align: left;
  display: flex;
  box-sizing: border-box;
  flex-direction: column;
  width: 100%;
  height: 100%;
  .box {
    background: #fff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    margin-bottom: 20px;
  }
  .search {
    text-align: left;
    /deep/.el-form {
      .minclass .el-form-item__content {
        width: 120px;
        margin-right: 10px;
        .el-date-editor.el-input {
          width: 120px;
        }
      }
      .minclass2 {
        .el-date-editor--date {
          width: 160px;
          margin-right: 10px;
        }
      }
    }
    .search-btn {
      /deep/.el-button--primary {
        box-sizing: border-box;
        // background: #0c88be;
        // border-color: #0c88be;
      }
    }
  }
  .data {
    flex: 1;
    display: flex;
    flex-direction: column;
    .add-btn {
      text-align: left;
      margin-bottom: 10px;
      /deep/.el-button {
        box-sizing: border-box;
        // background: #0c88be;
      }
    }
    .table-box {
      flex: 1;
      display: flex;
      flex-direction: column;
      .table {
        margin-bottom: 10px;
        flex: 1;
        .el-table {
        }
        .el-table__body-wrapper {
        }
      }
      .el-pagination {
        text-align: right;
      }
    }
  }
}
.edit-content {
  /deep/.el-dialog__body {
    padding-top: 20px;
  }
}
</style>
