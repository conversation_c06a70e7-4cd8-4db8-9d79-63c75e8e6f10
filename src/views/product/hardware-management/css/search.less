/deep/ .searchItem {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    .name {
      margin-right: 12px;
      font-size: 16px;
      color: #3a4762;
      width: 96px;
      text-align: right;
    }
    .el-input__inner,
    .el-range-separator {
      height: 32px;
    }
    .el-range-separator {
      margin-right: 4px;
    }
    .el-input__icon {
      line-height: 32px;
    }
    .el-icon-arrow-up:before {
      content: "";
    }
    .el-cascader {
      line-height: 32px;
      .el-icon-arrow-down {
        transform: rotate(180deg);
      }
      .is-reverse {
        transform: rotate(0);
      }
      .el-icon-arrow-down:before {
        content: "";
      }
      .el-input__icon:after {
        content: "";
      }
    }
    .el-select__caret,
    .el-icon-arrow-down {
      color: #3a4762;
    }
    .el-icon-time {
      display: none;
    }
    .el-range-input {
      width: 110px;
    }
    .el-date-editor--datetimerange {
      overflow: hidden;
    }
    .el-range-editor.el-input__inner {
      padding: 3px 12px;
    }
  }