<template>
  <div class="searchBox">
    <el-row :gutter="20">
      <!-- 仓库所属部门 -->
      <el-col :span="6">
        <div class="searchItem">
          <div class="name">仓库所属部门</div>
          <el-select
            v-model="from.deptId"
            class="searchInput"
            filterable
            style="width: 240px;"
          >
            <el-option label="全部" value=""> </el-option>
            <el-option
              v-for="item in warehouseDepartmentList"
              :key="item.deptId"
              :label="item.deptName"
              :value="item.deptId"
            >
            </el-option>
          </el-select></div
      ></el-col>
      <!-- 部门负责人 -->
      <el-col :span="6">
        <div class="searchItem">
          <div class="name">部门负责人</div>
          <el-select
            v-model="from.deptLeader"
            placeholder="请选择部门负责人"
            class="searchInput"
            filterable
            style="width: 240px;"
          >
            <el-option label="全部" value=""> </el-option>
            <el-option
              v-for="item in deptLeaderList"
              :key="item.warehouseLeader"
              :label="item.warehouseLeader"
              :value="item.warehouseLeader"
            >
            </el-option>
          </el-select></div
      ></el-col>
      <!-- 仓库名称 -->
      <el-col :span="6">
        <div class="searchItem">
          <div class="name">仓库名称</div>
          <el-select
            v-model="from.warehouseName"
            placeholder="请选择仓库名称"
            filterable
            class="searchInput"
            style="width: 240px;"
          >
            <el-option label="全部" value=""> </el-option>
            <el-option
              v-for="item in warehouseNameList"
              :key="item.warehouseId"
              :label="item.warehouseName"
              :value="item.warehouseName"
            >
            </el-option>
          </el-select></div
      ></el-col>
      <!-- 仓库负责人 -->
      <el-col :span="6">
        <div class="searchItem">
          <div class="name">仓库负责人</div>
          <el-select
            v-model="from.warehouseLeader"
            placeholder="请选择仓库负责人"
            filterable
            class="searchInput"
            style="width: 240px;"
          >
            <el-option label="全部" value=""> </el-option>
            <el-option
              v-for="item in warehouseLeaderList"
              :key="item.warehouseLeader"
              :label="item.warehouseLeader"
              :value="item.warehouseLeader"
            >
            </el-option>
          </el-select></div
      ></el-col>
      <!-- 仓库地址 -->
      <el-col :span="6">
        <div class="searchItem">
          <div class="name">仓库地址</div>
          <el-cascader
            v-model="from.warehouseAddress"
            :options="options"
            style="width: 240px;"
            @change="handleChange"
            filterable
            :props="{ checkStrictly: true }"
            clearable
          ></el-cascader></div
      ></el-col>
    </el-row>
    <div class="btns">
      <div class="search common" @click="search()">搜索</div>
      <div class="reset common" @click="reset()">重置</div>
    </div>
  </div>
</template>
<script>
import { httpReq } from "@/http";
export default {
  data() {
    return {
      from: {
        deptId: "",
        deptLeader: "",
        warehouseName: "",
        warehouseLeader: "",
        warehouseAddress: ""
      },
      // 部门负责人
      deptLeaderList: [],
      // 仓库名称
      warehouseNameList: [],
      // 仓库负责人
      warehouseLeaderList: [],
      address: ["province", "city", "hospitalName", "inpatientWard"]
    };
  },
  props: ["warehouseDepartmentList", "options", "updataData"],
  watch: {
    updataData: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.getDeptLeaderList();
          this.getWarehouseNameList();
          this.getWarehouseLeaderList();
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.getDeptLeaderList();
    this.getWarehouseNameList();
    this.getWarehouseLeaderList();
  },
  methods: {
    // 搜素
    search() {
      let arr = ["province", "city", "hospitalName", "inpatientWard"];
      if (this.from.warehouseAddress.length) {
        for (let i = 0; i < this.from.warehouseAddress.length; i++) {
          this.from[arr[i]] = this.from.warehouseAddress[i];
        }
      }
      this.$emit("queryData", this.from, "search");
    },

    // 获取部门负责人
    getDeptLeaderList() {
      httpReq({
        url: "/virtual/warehouse/vague/query/dept/leader",
        method: "post",
        data: {
          keyword: ""
        }
      }).then(res => {
        this.deptLeaderList = res.data;
      });
    },

    // 获取仓库名称
    getWarehouseNameList() {
      httpReq({
        url: "/virtual/warehouse/vague/query/name",
        method: "post",
        data: {
          keyword: ""
        }
      }).then(res => {
        this.warehouseNameList = res.data;
      });
    },

    // 获取仓库负责人
    getWarehouseLeaderList() {
      httpReq({
        url: "/virtual/warehouse/vague/query/leader ",
        method: "post",
        data: {
          keyword: ""
        }
      }).then(res => {
        this.warehouseLeaderList = res.data;
      });
    },

    // 选择地址
    handleChange() {
      let arr = ["province", "city", "hospitalName", "inpatientWard"];
      if (arr.length) {
        for (let i = 0; i < arr.length; i++) {
          this.from[arr[i]] = "";
        }
      }
    },

    // 重置
    reset() {
      this.from = {
        deptId: "",
        deptLeader: "",
        warehouseName: "",
        warehouseLeader: "",
        warehouseAddress: ""
      };
      this.$emit("queryData", this.from, "reset");
    }
  }
};
</script>
<style lang="less" scoped>
.searchBox {
  background: #ffffff;
  border-radius: 4px;
  padding: 16px 24px;
  box-sizing: border-box;
  /deep/ .searchItem {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    .name {
      margin-right: 12px;
      font-size: 16px;
      color: #3a4762;
      width: 96px;
      text-align: right;
    }
    .el-input__inner,
    .el-range-separator {
      height: 32px;
    }
    .el-input__icon {
      line-height: 32px;
    }
    .el-icon-arrow-up:before {
      content: "";
    }
    .el-cascader {
      line-height: 32px;
      .el-icon-arrow-down {
        transform: rotate(180deg);
      }
      .is-reverse {
        transform: rotate(0);
      }
      .el-icon-arrow-down:before {
        content: "";
      }
      .el-input__icon:after {
        content: "";
      }
    }
    .el-select__caret,
    .el-icon-arrow-down {
      color: #3a4762;
    }
    .el-icon-time {
      display: none;
    }
    .el-range-input {
      width: 130px;
    }
    .el-date-editor--datetimerange {
      overflow: hidden;
    }
    .el-range-editor.el-input__inner {
      padding: 3px 12px;
    }
  }
  .btns {
    display: flex;
    justify-content: center;
    margin-top: 16px;
    .common {
      cursor: pointer;
      display: flex;
      justify-content: center;
      font-size: 14px;
      align-items: center;
    }
    .search {
      width: 76px;
      height: 32px;
      background: #2e6be6;
      border-radius: 2px;
      color: #ffffff;
    }
    .reset {
      width: 76px;
      height: 32px;
      border-radius: 2px;
      border: 1px solid #dcdfe6;
      box-sizing: border-box;
      color: #606266;
      margin-left: 16px;
    }
  }
}
</style>
