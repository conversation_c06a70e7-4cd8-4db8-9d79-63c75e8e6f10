<template>
  <div class="drawer">
    <el-drawer
      :visible.sync="drawer"
      direction="rtl"
      class="details-drawer"
      :show-close="false"
      :size="540"
      @close="closeDrawer"
    >
      <div class="drawer-content">
        <div class="title">
          <i class="el-icon-close close-icon" @click="drawer = false"></i
          >库存变更
        </div>
        <div class="content">
          <div class="fill-box">
            <div class="hr"></div>
            选择硬件
          </div>
          <div class="query-box">
            <el-input
              placeholder="请手动输入硬件编号"
              v-model="hardwareNo"
              style="width: 202px;"
            >
            </el-input>
            <div class="search-box" @click="search()">
              <i class="el-icon-search search-icon"></i>
            </div>
          </div>
          <div class="tab-box">
            <div
              class="item-tab-common"
              :class="item.type === isActive ? 'item-tab-active' : 'item-tab'"
              v-for="item in tabList"
              @click="changeTab(item)"
              :key="item.type"
            >
              {{ item.title }}({{ item.value }})
            </div>
          </div>
          <div class="equipment-list">
            <div class="list-header">
              <div
                class="change-common isChecked"
                @click="checkAll()"
                v-show="isCheckAll"
              >
                <div class="flag"></div>
              </div>
              <div
                class="change-box"
                @click="checkAll()"
                v-show="!isCheckAll"
              ></div>
              <div
                class="header-item"
                v-for="(item, index) in equipmentHeaderList"
                :key="index"
              >
                {{ item }}
              </div>
            </div>
            <ul
              class="infinite-list"
              v-if="equipmentList.length"
              v-infinite-scroll="load"
            >
              <li
                v-for="(item, index) in equipmentList"
                :key="index"
                class="infinite-list-item"
              >
                <div
                  class="isChecked change-common"
                  @click="singleChoice(item)"
                  v-if="item.isChecked"
                >
                  <div class="flag"></div>
                </div>
                <div
                  class="change-box change-common"
                  @click="singleChoice(item)"
                  v-else
                ></div>
                <div class="list-item">
                  {{ item.deviceNo }}
                </div>
                <div class="list-item">
                  {{
                    item.deviceType == "HP"
                      ? "掌护血压计"
                      : item.deviceType == "BPG"
                      ? "台式血压计"
                      : item.deviceType == "WATCH"
                      ? "智能手表"
                      : "体重秤"
                  }}
                </div>
              </li>
              <li v-if="noMore" class="no-more">没有更多了</li>
            </ul>
            <div v-if="!equipmentList.length" class="null-data-box">
              <img
                src="@/assets/image/null-data-icon.png"
                class="null-data-icon"
                alt=""
              />
              <div>暂无内容</div>
            </div>
          </div>
          <div class="fill-box" style="margin-top: 24px;">
            <div class="hr"></div>
            变更内容
          </div>
          <div class="from-box">
            <el-form
              :model="ruleForm"
              :rules="rules"
              ref="ruleForm"
              label-width="100px"
              class="demo-ruleForm"
            >
              <el-form-item label="仓库名称" prop="principal">
                <el-input
                  style="width: 364px;"
                  disabled
                  v-model="stateName"
                ></el-input>
              </el-form-item>
              <el-form-item label="变更部门" prop="changeDepartment">
                <el-select
                  style="width: 364px;"
                  v-model="ruleForm.changeDepartment"
                  @change="changeDepartmentEvent"
                  placeholder="请选择变更部门"
                >
                  <el-option
                    v-for="item in warehouseDepartmentList"
                    :key="item.deptId"
                    :label="item.deptName"
                    :value="item.deptId"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="变更仓库" prop="changeWarehouse">
                <el-select
                  style="width: 364px;"
                  v-model="ruleForm.changeWarehouse"
                  @change="changeWarehouse"
                  placeholder="请选择变更仓库"
                >
                  <el-option
                    :label="item.label"
                    :value="item.value"
                    v-for="item in warehouseList"
                    :disabled="item.value == warehouseId"
                    :key="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="变更负责人">
                <el-input
                  style="width: 364px;"
                  disabled
                  v-model="ruleForm.changePrincipal"
                ></el-input>
              </el-form-item>
              <el-form-item label="仓库地址" prop="stashAddress">
                <el-cascader
                  @change="changeStashAddress"
                  v-model="ruleForm.stashAddress"
                  :options="changeOptions"
                  style="width: 364px;"
                  placeholder="请选择仓库地址"
                  filterable
                  clearable
                ></el-cascader>
              </el-form-item>
            </el-form>
          </div>
          <div class="btns">
            <div class="put-storage common" @click="submit('ruleForm')">
              提交变更
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import { httpReq } from "@/http";
export default {
  data() {
    return {
      ruleForm: {
        changeDepartment: "",
        changeWarehouse: "",
        changePrincipal: "",
        stashAddress: ""
      },
      equipmentHeaderList: ["硬件编号", "硬件类型"],
      equipmentList: [],
      isCheckAll: false,
      hardwareNo: "",
      isActive: "",
      warehouseList: [],
      tableData: [],
      rules: {
        changeDepartment: [
          { required: true, message: "请选择变更部门", trigger: "change" }
        ],
        changeWarehouse: [
          { required: true, message: "请选择变更仓库", trigger: "change" }
        ],
        stashAddress: [
          { required: true, message: "请选择仓库地址", trigger: "change" }
        ]
      },
      tabList: [],
      total: 0,
      pageNumber: 1,
      currentWarehousrId: "",
      changeOptions: []
    };
  },
  props: [
    "stateName",
    "drawer",
    "warehouseDepartmentList",
    "warehouseId"
    // "inpatientWard"
  ],
  mounted() {},
  computed: {
    noMore() {
      return this.pageNumber == Math.ceil(this.total / 10);
    }
  },
  watch: {
    drawer: {
      handler(newVal, oldVal) {
        if (newVal) {
          if (this.$refs["ruleForm"]) this.$refs["ruleForm"].resetFields();

          this.getEquipmentList();
          this.getList();
          this.getWarehouseAddressList();
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 获取虚拟仓库列表数据
    getList(deptId) {
      httpReq({
        url: "/virtual/warehouse/page/query",
        method: "post",
        data: { page: 1, pageSize: 200, headQuarter: false, deptId }
      }).then(res => {
        let { contents } = res.data;
        this.tableData = contents;
        let array = [];
        contents.forEach(item => {
          array.push({
            label: item.warehouseName,
            value: item.warehouseId
          });
        });
        this.warehouseList = array;
      });
    },

    // 变更部门
    changeDepartmentEvent(val) {
      this.ruleForm.changeWarehouse = "";
      this.ruleForm.changePrincipal = "";
      this.ruleForm.stashAddress = "";
      this.getList(val);
      this.getWarehouseAddressList(val);
    },

    // 获取仓库地址-此处要过滤掉总部仓库-用于库存变更
    getWarehouseAddressList(deptId) {
      httpReq({
        url: "/virtual/warehouse/query/address",
        method: "post",
        data: {
          headQuarter: false,
          deptId
        }
      }).then(res => {
        let { data } = res;
        this.changeOptions = this.handleAddress(data);
      });
    },

    // 处理仓库地址字段
    handleAddress(data) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        item.value = item.name;
        item.label = item.name;
        if (item.children && item.children.length) {
          this.handleAddress(item.children);
        }
        if (!item.children.length) {
          delete item.children;
        }
        // if (!item.children && item.value === this.inpatientWard) {
        //   item.disabled = true;
        // }
      }
      return data;
    },

    // 获取设备列表
    getEquipmentList() {
      httpReq({
        url: "/virtual/warehouse/page/query/device",
        method: "post",
        data: {
          pageNumber: this.pageNumber,
          pageSize: 10,
          warehouseId: this.warehouseId,
          deviceNo: this.hardwareNo,
          deviceTypeList: this.isActive
            ? [this.isActive].join(",").split(",")
            : []
        }
      }).then(res => {
        let {
          contents,
          watchCount,
          bpgCount = 0,
          hpCount = 0,
          wsCount,
          total
        } = res.data;
        contents.forEach(item => (item.isChecked = false));
        this.equipmentList = [...this.equipmentList, ...contents];

        this.tabList = [
          {
            title: "血压计",
            value: bpgCount + hpCount,
            type: "BPG,HP"
          },
          {
            title: "智能手表",
            value: watchCount,
            type: "WATCH"
          },
          {
            title: "体重秤",
            value: wsCount,
            type: "WS"
          }
        ];
        this.total = total;
        this.isCheckAll = false;
      });
    },

    // 变更仓库
    changeWarehouse(id) {
      this.tableData.forEach(item => {
        let {
          province,
          city,
          hospitalName,
          inpatientWard,
          warehouseLeader,
          warehouseId,
          deptId
        } = item;
        if (warehouseId == id) {
          let arr = [province, city, hospitalName, inpatientWard];
          this.ruleForm.stashAddress = arr;
          this.ruleForm.changePrincipal = warehouseLeader;
          this.ruleForm.changeDepartment = deptId;
        }
      });
    },

    // 选择仓库地址
    changeStashAddress(value) {
      this.tableData.forEach(item => {
        let {
          province,
          city,
          hospitalName,
          inpatientWard,
          warehouseLeader,
          warehouseId,
          deptId
        } = item;
        let arr = [province, city, hospitalName, inpatientWard];
        if (JSON.stringify(arr) == JSON.stringify(value)) {
          this.ruleForm.changePrincipal = warehouseLeader;
          this.ruleForm.changeWarehouse = warehouseId;
          this.ruleForm.changeDepartment = deptId;
        }
      });
    },

    // 输入设备编号查询
    search() {
      this.pageNumber = 1;
      this.equipmentList = [];
      this.getEquipmentList();
    },

    // 全选
    checkAll() {
      this.isCheckAll = !this.isCheckAll;
      this.equipmentList.forEach(item => (item.isChecked = this.isCheckAll));
    },

    // 单选
    singleChoice(item) {
      item.isChecked = !item.isChecked;
      this.isCheckAll = this.equipmentList.every(item => item.isChecked);
    },

    // tab切换
    changeTab(item) {
      this.isActive = this.isActive == item.type ? "" : item.type;
      this.pageNumber = 1;
      this.equipmentList = [];
      this.getEquipmentList();
    },

    // 提交变更
    submit(formName) {
      let arr = this.equipmentList.filter(item => item.isChecked);
      if (!arr.length) {
        return this.$message({
          message: "请选择要变更的硬件设备！",
          type: "warning"
        });
      }

      this.$refs[formName].validate(valid => {
        if (valid) {
          this.$confirm(
            '<i class="el-icon-warning"></i>确定将所选设备变更至新仓库吗?',
            {
              title: "提示!",
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              dangerouslyUseHTMLString: true,
              customClass: "messageTips"
            }
          )
            .then(() => {
              httpReq({
                url: "/virtual/warehouse/change/stock",
                method: "post",
                data: {
                  deviceNoList: arr.map(item => item.deviceNo),
                  targetWarehouseId: this.ruleForm.changeWarehouse
                }
              }).then(res => {
                let { code } = res;
                if (code == 1) {
                  this.$message({
                    type: "success",
                    message: "变更成功!"
                  });
                  this.$emit("closeDrawer", true);
                }
              });
            })
            .catch(() => {});
        } else {
          return false;
        }
      });
    },

    // 滚动到底部
    load() {
      let totlePage = Math.ceil(this.total / 10);
      if (this.pageNumber < totlePage) {
        this.pageNumber++;
        this.getEquipmentList();
      }
    },

    // 关闭抽屉
    closeDrawer() {
      this.isActive = "";
      this.pageNumber = 1;
      this.equipmentList = [];
      this.$emit("closeDrawer");
    }
  }
};
</script>
<style lang="less" scoped>
.drawer {
  /deep/.details-drawer {
    .el-drawer__header {
      padding: 0;
      margin-bottom: 0;
    }
    .el-drawer__body {
      height: 100%;
      .drawer-content {
        height: 100%;
        .title {
          padding: 16px;
          border-bottom: 1px solid #e1e5ed;
          display: flex;
          align-items: center;
          font-weight: bold;
          font-size: 16px;
          color: #3a4762;
          .close-icon {
            font-size: 18px;
            margin-right: 15px;
            cursor: pointer;
          }
        }
        .content {
          padding: 16px 24px;
          .fill-box {
            font-weight: bold;
            font-size: 16px;
            color: #15233f;
            display: flex;
            align-items: center;
            .hr {
              width: 2px;
              height: 12px;
              background: #2e6be6;
              border-radius: 1px;
              margin-right: 6px;
            }
          }
          .query-box {
            display: flex;
            margin-top: 16px;
            align-items: center;
            .el-input__inner {
              height: 32px;
            }
            .search-box {
              width: 38px;
              height: 32px;
              background: #2e6be6;
              border-radius: 2px;
              display: flex;
              justify-content: center;
              align-items: center;
              cursor: pointer;
              .search-icon {
                font-size: 18px;
                color: #fff;
              }
            }
          }
          .tab-box {
            display: flex;
            margin-top: 16px;
            .item-tab-common {
              font-size: 14px;
              width: 118px;
              height: 32px;
              cursor: pointer;
              display: flex;
              justify-content: center;
              align-items: center;
            }
            .item-tab {
              color: #3a4762;
              border: 1px solid #dcdfe6;
            }
            .item-tab-active {
              color: #2e6be6;
              background: #e6eeff;
              border: 1px solid #2e6be6;
            }
            .item-tab-common:first-child {
              border-radius: 2px 0px 0px 2px;
            }
            .item-tab-common:last-child {
              border-radius: 0px 2px 2px 0px;
            }
          }
          .from-box {
            margin-top: 24px;
            .el-form {
              .el-form-item__label,
              .el-form-item__content,
              .el-input__icon {
                line-height: 32px;
              }
              .el-icon-arrow-up:before {
                content: "";
              }
              .el-select__caret,
              .el-icon-arrow-down {
                color: #3a4762;
              }
              .el-input__inner {
                height: 32px;
                line-height: 32px;
              }
              .el-cascader {
                line-height: 32px;
                .el-icon-arrow-down {
                  transform: rotate(180deg);
                }
                .is-reverse {
                  transform: rotate(0);
                }
                .el-icon-arrow-down:before {
                  content: "";
                }
                .el-input__icon:after {
                  content: "";
                }
              }
              .el-form-item__error {
                left: 12px;
              }
            }
          }
          .equipment-list {
            margin-top: 16px;
            padding-bottom: 8px;
            border-bottom: 1px dashed #e1e5ed;
            .change-common {
              border-radius: 2px;
              cursor: pointer;
              width: 16px;
              height: 16px;
            }
            .isChecked {
              background: #2e6be6;
              position: relative;
              .flag {
                border: 1px solid #fff;
                border-left: 0;
                border-top: 0;
                height: 8px;
                left: 6px;
                position: absolute;
                top: 2px;
                transform: rotate(45deg);
                width: 4px;
              }
            }
            .list-header {
              display: flex;
              align-items: center;
              height: 52px;
              background: #f7f8fa;
              box-shadow: 0px 1px 0px 0px #ebedf0;
              padding: 0 16px;
              box-sizing: border-box;
              .change-box {
                width: 16px;
                height: 16px;
                background: #ffffff;
                border-radius: 2px;
                border: 1px solid #2e6be6;
                cursor: pointer;
                box-sizing: border-box;
              }
              .header-item {
                font-weight: bold;
                font-size: 14px;
                color: #15233f;
                text-align: left;
              }
              .header-item:nth-child(3) {
                width: 200px;
                margin-left: 8px;
              }
              .header-item:nth-child(4) {
                margin-left: 8px;
                width: 200px;
              }
            }
            .infinite-list {
              padding: 0;
              margin: 0;
              height: 250px;
              overflow-y: scroll;
              .infinite-list-item {
                display: flex;
                align-items: center;
                height: 52px;
                padding: 0 16px;
                border-bottom: 1px solid #e1e5ed;
                box-sizing: border-box;
                .change-box {
                  background: #ffffff;
                  border: 1px solid #dcdfe6;
                  box-sizing: border-box;
                }
                .list-item {
                  text-align: left;
                  width: 200px;
                  font-size: 14px;
                  color: #3a4762;
                  margin-left: 8px;
                }
              }
              .list-item-active {
                background: #e6eeff;
              }
            }
            .null-data-box {
              margin-top: 32px;
              font-size: 14px;
              color: #575757;
              .null-data-icon {
                width: 80px;
                height: 55px;
              }
            }
            .no-more {
              font-size: 14px;
              color: #575757;
              margin-top: 8px;
            }
          }
          .btns {
            margin-top: 24px;
            display: flex;
            justify-content: flex-end;
            .common {
              width: 76px;
              height: 32px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 14px;
              cursor: pointer;
              border-radius: 2px;
            }
            .put-storage {
              background: #2e6be6;
              color: #ffffff;
              margin-right: 16px;
            }
          }
        }
        .infinite-list::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }
        // 滚动条的轨道的两端按钮，允许通过点击微调小方块的位置。
        .infinite-list::-webkit-scrollbar-button {
          display: none;
        }
        // 滚动条的轨道（里面装有Thumb）
        .infinite-list::-webkit-scrollbar-track {
          background: transparent;
        }
        // 滚动条的轨道（里面装有Thumb）
        .infinite-list::-webkit-scrollbar-track-piece {
          background-color: transparent;
        }
        // 滚动条里面的小方块，能向上向下移动（或往左往右移动，取决于是垂直滚动条还是水平滚动条）
        .infinite-list::-webkit-scrollbar-thumb {
          background: rgba(144, 147, 153, 0.3);
          cursor: pointer;
          border-radius: 4px;
        }
        // 边角，即两个滚动条的交汇处
        .infinite-list::-webkit-scrollbar-corner {
          display: none;
        }
        // 两个滚动条的交汇处上用于通过拖动调整元素大小的小控件
        .infinite-list::-webkit-resizer {
          display: none;
        }
      }
    }
  }
}
</style>
