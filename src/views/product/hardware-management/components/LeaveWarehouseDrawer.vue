<template>
  <div class="index">
    <el-drawer
      :visible.sync="drawer"
      direction="rtl"
      class="details-drawer"
      :show-close="false"
      :size="540"
      @close="close"
    >
      <div class="drawer-content">
        <div class="title">
          <i class="el-icon-close close-icon" @click="drawer = false"></i
          >出库操作
        </div>
        <div class="content">
          <div class="fill-box">
            <div class="hr"></div>
            出库资料填写
            <el-popover
              placement="right"
              trigger="click"
              style="height: 16px;width: 16px;"
              transition="fade-in-linear"
              popper-class="status-instructions"
            >
              <div class="instructions-content">
                各类库存状态操作约束说明： <br />
                1.采购入库：仅支持要货出库、返厂出库 <br />
                2.要货出库：仅支持退货入库、销售出货 <br />
                3.销售出货：仅支持退货入库、设备在途 <br />
                4.退货入库：仅支持返厂出库、要货出库 <br />
                5.返厂出库：仅支持采购入库 <br />
                6.设备在途：仅支持退货入库
              </div>
              <img
                src="@/assets/image/question-circle-outlined.png"
                alt=""
                class="question-circle"
                slot="reference"
              />
            </el-popover>
          </div>
          <div class="tips">
            <img src="@/assets/image/icon-warn.png" alt="" class="icon-warn" />
            同一条出库单可包含多种设备类型
          </div>
          <div class="from-box">
            <el-form
              :model="ruleForm"
              :rules="rules"
              ref="ruleForm"
              label-width="100px"
              class="demo-ruleForm"
            >
              <el-form-item label="出库类型" prop="type">
                <div class="type-style">
                  <el-radio-group
                    v-model="ruleForm.type"
                    @input="changeType"
                    :disabled="drawerType == 2 || addEquipmentList.length"
                  >
                    <el-radio label="ENQUIRY_OUT">要货出库</el-radio>
                    <el-radio label="RETURN_FACTORY_OUT">返厂出库</el-radio>
                  </el-radio-group>
                  <el-popover
                    placement="top"
                    trigger="hover"
                    style="height: 16px;width: 16px;"
                    transition="fade-in-linear"
                  >
                    <div>
                      无新录入设备时可切换出库类型
                    </div>
                    <img
                      src="@/assets/image/question-circle-outlined.png"
                      alt=""
                      style="height: 16px;width: 16px;"
                      class="question-circle-outlined"
                      slot="reference"
                    />
                  </el-popover>
                </div>
              </el-form-item>
              <el-form-item label="出库仓库" prop="stash">
                <el-select
                  style="width: 364px;"
                  :disabled="
                    drawerType == 2 ||
                      (drawerType == 1 && ruleForm.type == 'RETURN_FACTORY_OUT')
                  "
                  v-model="ruleForm.stash"
                  @change="changeWarehouse"
                  placeholder="请选择出库仓库"
                >
                  <el-option
                    v-for="item in stashList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="负责人" prop="principal">
                <el-input
                  style="width: 364px;"
                  disabled
                  v-model="ruleForm.principal"
                ></el-input>
              </el-form-item>
              <el-form-item label="仓库地址" prop="stashAddress">
                <el-cascader
                  v-model="ruleForm.stashAddress"
                  @change="changeStashAddress"
                  :disabled="
                    drawerType == 2 ||
                      (drawerType == 1 && ruleForm.type == 'RETURN_FACTORY_OUT')
                  "
                  :options="options"
                  style="width: 364px;"
                  placeholder="请选择仓库地址"
                  filterable
                  clearable
                ></el-cascader>
              </el-form-item>
              <el-form-item label="备注">
                <el-input
                  style="width: 364px;"
                  type="textarea"
                  maxlength="200"
                  placeholder="请备注设备出库物流信息"
                  show-word-limit
                  :rows="4"
                  v-model="ruleForm.remark"
                  resize="none"
                ></el-input>
              </el-form-item>
              <el-form-item label="硬件编号" prop="hardwareNo">
                <div class="hardwareNumber">
                  <el-input
                    style="width: 312px;"
                    v-model.trim="ruleForm.hardwareNo"
                    clearable
                    placeholder="请录入硬件编号"
                  ></el-input>
                  <div class="enter" @click="enter">录入</div>
                </div>
              </el-form-item>
            </el-form>
          </div>
          <div class="equipment-list">
            <div class="list-header">
              <div
                class="header-item"
                v-for="(item, index) in equipmentHeaderList"
                :key="index"
              >
                {{ item }}
              </div>
            </div>
            <ul class="infinite-list" v-if="getList.length">
              <li
                v-for="(item, index) in getList"
                :key="index"
                class="infinite-list-item"
                :class="{ 'list-item-active': index === 0 && item.isNewAdd }"
              >
                <div class="list-item">
                  {{ item.deviceNo }}
                </div>
                <div class="list-item">
                  {{
                    item.deviceType == "HP"
                      ? "掌护血压计"
                      : item.deviceType == "BPG"
                      ? "台式血压计"
                      : item.deviceType == "WATCH"
                      ? "智能手表"
                      : "体重秤"
                  }}
                </div>
                <div class="delete" @click="deleteEquipment(item, index)">
                  删除
                </div>
              </li>
              <li v-if="getList.length > 3" class="no-more">没有更多了</li>
            </ul>
            <div v-else class="null-data-box">
              <img
                src="@/assets/image/null-data-icon.png"
                class="null-data-icon"
                alt=""
              />
              <div>暂无内容，请在上方录入</div>
            </div>
          </div>
          <div v-if="getList.length" class="equipment-statistics">
            *录入出库设备合计：{{
              getEquipmentNumber.total
            }}台；<br />智能手表：{{
              getEquipmentNumber.watchNo
            }}；台式血压计：{{
              getEquipmentNumber.sphygmomanometerNo
            }}；掌护血压计：{{ getEquipmentNumber.hpNo }}；体重秤：{{
              getEquipmentNumber.scaleNo
            }}
          </div>
          <div class="btns">
            <div
              v-if="addEquipmentList.length"
              class="clear common"
              @click="clear"
            >
              清空
            </div>
            <div class="put-storage common" @click="putStorage('ruleForm')">
              出库
            </div>
          </div>
        </div>
      </div>
    </el-drawer>

    <!-- 二次确认弹窗 -->
    <SecondaryDialog
      :secondaryDialogVisible="secondaryDialogVisible"
      :secondarytype="secondarytype"
      :secondaryForm="secondaryForm"
      @closeDialog="secondaryDialogVisible = false"
      @clearEquipment="clearEquipment"
      @removalEquipment="removalEquipment"
    ></SecondaryDialog>
  </div>
</template>
<script>
import { httpReq } from "@/http";
import { inventoryStatusList } from "../index";
import Debounce from "@/util/util";
import SecondaryDialog from "./SecondaryDialog.vue";
export default {
  data() {
    return {
      rules: {
        type: [
          { required: true, message: "请选择出库类型", trigger: "change" }
        ],
        stash: [
          { required: true, message: "请选择入库仓库", trigger: "change" }
        ],
        principal: [
          { required: true, message: "请输入负责人", trigger: "blur" }
        ],
        stashAddress: [
          { required: true, message: "请选择仓库地址", trigger: "change" }
        ]
      },
      options: [],
      addEquipmentList: [],
      deteleEquipmentList: [],
      equipmentHeaderList: ["硬件编号", "硬件类型", "操作"],
      equipmentList: [],
      stashList: [],
      secondaryDialogVisible: false,
      // secondarytype:1--清空  2--入库
      secondarytype: 1,
      secondaryForm: {
        title: "",
        content: ""
      },
      equipmentTypeList: [
        {
          length: 9,
          type: "BPG"
        },
        {
          length: 14,
          type: "BPG"
        },
        {
          length: 12,
          type: "WS"
        },
        {
          length: 15,
          type: "WATCH"
        },
        {
          length: num => `${num}`.startsWith("86978406") && num.length === 15,
          type: "HP",
          model: "掌护"
        }
      ],
      tableData: [],
      inventoryStatusList
    };
  },
  computed: {
    getEquipmentNumber() {
      let watchArr = this.getList.filter(item => item.deviceType == "WATCH");
      let sphygmomanometerArr = this.getList.filter(
        item => item.deviceType == "BPG"
      );
      let scaleArr = this.getList.filter(item => item.deviceType == "WS");
      const hpArr = this.getList.filter(item => item.deviceType == "HP");

      return {
        total: this.getList.length,
        watchNo: watchArr.length,
        sphygmomanometerNo: sphygmomanometerArr.length,
        scaleNo: scaleArr.length,
        hpNo: hpArr.length
      };
    },

    getList() {
      return [...this.addEquipmentList, ...this.equipmentList];
    }
  },
  mounted() {
    this.de = new Debounce(300);
  },
  components: {
    SecondaryDialog
  },
  props: ["drawer", "currentEquipmentType", "drawerType", "ruleForm"],
  watch: {
    drawer: {
      handler(newVal, oldVal) {
        if (newVal) {
          let flag = this.ruleForm.type == "RETURN_FACTORY_OUT" ? true : false;
          this.getWarehouseList(flag);
          this.getAddressList(flag);
          if (this.$refs["ruleForm"]) this.$refs["ruleForm"].resetFields();

          if (this.drawerType == 2) this.getExitList();
        }
      },
      deep: true
    }
  },
  methods: {
    // 确认出库
    putStorage(formName) {
      if (!this.getList.length) {
        return this.tipMessage("请先录入设备!");
      }
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.secondarytype = 2;
          this.secondaryForm.title = "确认要提交出库信息吗？";
          this.secondaryForm.content = "确定要提交录入的全部硬件采购信息吗？";
          this.secondaryDialogVisible = true;
        } else {
          return false;
        }
      });
    },
    async removalEquipment() {
      await this.de.debounceEnd();
      if (this.drawerType == 1) {
        httpReq({
          url: "/warehouse/exit/save",
          method: "post",
          data: {
            type: this.ruleForm.type,
            warehouseId: this.ruleForm.stash,
            remark: this.ruleForm.remark,
            addDeviceList: this.addEquipmentList.map(item => item.deviceNo)
          }
        })
          .then(res => {
            let { code } = res;
            if (code == 1) {
              this.$message({
                type: "success",
                message: "操作成功!"
              });
              this.$emit("closeEnterWarehouseDrawer", true);
              this.secondaryDialogVisible = false;
            }
          })
          .catch(err => {
            let { code, msg } = err;
            if (code === -121) {
              this.secondaryDialogVisible = false;
              this.tipMessage(`${msg}`);
            }
          });
      } else if (this.drawerType == 2) {
        httpReq({
          url: "/warehouse/exit/update",
          method: "post",
          data: {
            warehouseExitId: this.ruleForm.exitId,
            remark: this.ruleForm.remark,
            addDeviceList: this.addEquipmentList.map(item => item.deviceNo),
            removeDeviceList: this.deteleEquipmentList.map(
              item => item.deviceNo
            )
          }
        })
          .then(res => {
            let { code } = res;
            if (code == 1) {
              this.$message({
                type: "success",
                message: "操作成功!"
              });
              this.$emit("closeEnterWarehouseDrawer", true);
              this.secondaryDialogVisible = false;
            }
          })
          .catch(err => {
            let { code, msg } = err;
            if (code === -121) {
              this.secondaryDialogVisible = false;
              this.tipMessage(`${msg}`);
            }
          });
      }
    },

    // 根据编号长度获取设备类型
    getEquipmentType(number) {
      let type = "";
      this.equipmentTypeList.forEach(item => {
        if (typeof item.length === "function" && item.length(number)) {
          type = item.type;
        } else if (item.length == number.length) {
          type = item.type;
        }
      });

      return type;
    },

    // 录入
    enter() {
      // 校验硬件编号不能为空
      if (!this.ruleForm.hardwareNo) {
        return this.tipMessage("请录入硬件编号!");
      }

      if (this.getList.length == 1000) {
        return this.tipMessage("录入失败，超出录入上限1000台!");
      }
      let deviceNo = [];
      if (this.ruleForm.hardwareNo.includes(";")) {
        deviceNo = this.ruleForm.hardwareNo.split(";").filter(item => item);
      } else {
        if (!this.validHardwareNo(this.ruleForm.hardwareNo)) {
          return this.tipMessage("录入失败，请检查设备类型后重新录入!");
        } else {
          deviceNo = [this.ruleForm.hardwareNo];
        }
      }
      deviceNo = Array.from(new Set(deviceNo));

      // 如果已经存在相同的硬件，不可重复录入
      let isExistArr = [];
      this.getList.forEach(item => {
        deviceNo.forEach(ite => {
          if (item.deviceNo == ite) isExistArr.push(ite);
        });
      });
      if (isExistArr.length) {
        let str = isExistArr.join("、");
        return this.tipMessage(`以下设备：${str}已存在，请勿重复录入!`);
      }

      // 校验硬件编号
      httpReq({
        url: "/hrt/device/check/status",
        method: "post",
        data: {
          operationStatus: this.ruleForm.type,
          deviceNo
        }
      })
        .then(res => {
          let { data, code } = res;
          if (code == 1) {
            // 单个设备
            if (data.length == 1) {
              let item = data[0];
              let currentType = item.inventoryStatus;
              // 当设备状态为空时不允许录入
              if (!currentType) {
                return this.tipMessage("未查询到该设备，请先进行采购入库!");
                // 当设备状态为采购入库、退货入库才能进行录入
              } else if (
                currentType == "PURCHASE_IN" ||
                currentType == "RETURN_IN"
              ) {
                this.addEquipmentList.unshift({
                  deviceType: this.getEquipmentType(item.deviceNo),
                  deviceNo: item.deviceNo,
                  inventoryStatus: item.inventoryStatus,
                  isNewAdd: true
                });
                this.ruleForm.hardwareNo = "";
              } else {
                let content = this.inventoryStatusList.filter(
                  item => item.value == currentType
                )[0].label;
                let message = `硬件处于【${content}】状态，不允许进行【${
                  this.ruleForm.type == "ENQUIRY_OUT" ? "要货出库" : "返厂出库"
                }】操作。`;
                this.tipMessage(message);
              }
            } else {
              // 多个设备
              let failArr = [];
              data.forEach(item => {
                if (!item.checkResult) {
                  failArr.push(item.deviceNo);
                } else {
                  this.addEquipmentList.unshift({
                    deviceType: this.getEquipmentType(item.deviceNo),
                    deviceNo: item.deviceNo,
                    inventoryStatus: item.inventoryStatus,
                    isNewAdd: true
                  });
                  this.ruleForm.hardwareNo = "";
                }
              });
              if (failArr.length) {
                let str = failArr.join(";");
                this.ruleForm.hardwareNo = str;
                return this.tipMessage(`以下设备：${str}录入失败!`);
              }
            }
          }
        })
        .catch(err => {
          let { code, msg } = err;
          if (code == -121) {
            return this.tipMessage(`${msg}`);
          }
        });
    },

    // 选择出库仓库
    changeWarehouse(id) {
      this.tableData.forEach(item => {
        let {
          province,
          city,
          hospitalName,
          inpatientWard,
          warehouseLeader,
          warehouseId
        } = item;
        if (warehouseId == id) {
          let arr = [province, city, hospitalName, inpatientWard];
          this.ruleForm.stashAddress = arr;
          this.ruleForm.principal = warehouseLeader;
        }
      });
    },

    // 获取虚拟仓库列表数据
    getWarehouseList(headQuarter) {
      httpReq({
        url: "/virtual/warehouse/page/query",
        method: "post",
        data: { page: 1, pageSize: 200, headQuarter }
      }).then(res => {
        let { contents } = res.data;
        this.tableData = contents;
        if (headQuarter) {
          let item = contents[0];
          let {
            warehouseLeader,
            warehouseId,
            province,
            city,
            hospitalName,
            inpatientWard
          } = item;
          this.ruleForm.principal = warehouseLeader;
          this.ruleForm.stash = warehouseId;
          this.ruleForm.stashAddress = [
            province,
            city,
            hospitalName,
            inpatientWard
          ];
        }
        let array = [];
        contents.forEach(item => {
          array.push({
            label: item.warehouseName,
            value: item.warehouseId
          });
        });
        this.stashList = array;
      });
    },

    // 选择仓库地址
    changeStashAddress(value) {
      this.tableData.forEach(item => {
        let {
          province,
          city,
          hospitalName,
          inpatientWard,
          warehouseLeader,
          warehouseId
        } = item;
        let arr = [province, city, hospitalName, inpatientWard];
        if (JSON.stringify(arr) == JSON.stringify(value)) {
          this.ruleForm.principal = warehouseLeader;
          this.ruleForm.stash = warehouseId;
        }
      });
    },

    // 获取仓库地址
    getAddressList(headQuarter) {
      httpReq({
        url: "/virtual/warehouse/query/address",
        method: "post",
        data: {
          headQuarter
        }
      }).then(res => {
        let { data } = res;
        this.options = this.handleAddress(data);
      });
    },

    // 处理仓库地址字段
    handleAddress(data) {
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        item.value = item.name;
        item.label = item.name;
        if (item.children && item.children.length) {
          this.handleAddress(item.children);
        }
        if (!item.children.length) {
          delete item.children;
        }
      }
      return data;
    },

    // 校验硬件编号是否由数字和大小写英文组成
    validHardwareNo(str) {
      const reg = /^[0-9A-Za-z]+$/;
      return reg.test(str);
    },

    // 查询已录入硬件信息
    getExitList() {
      httpReq({
        url: "/warehouse/exit/page/query/device/info",
        method: "post",
        data: {
          pageNumber: 1,
          pageSize: 1000,
          exitId: this.ruleForm.exitId
        }
      }).then(res => {
        let { contents } = res.data;
        this.equipmentList = [...this.equipmentList, ...contents];
      });
    },

    // 单选
    changeType(val) {
      let flag = val == "RETURN_FACTORY_OUT" ? true : false;
      this.ruleForm.principal = "";
      this.ruleForm.stash = "";
      this.ruleForm.stashAddress = [];
      this.getWarehouseList(flag);
      this.getAddressList(flag);
    },

    // 删除设备
    deleteEquipment(item, index) {
      // 删除新录入的设备
      if (item.isNewAdd) {
        this.addEquipmentList.splice(index, 1);
      } else {
        // 删除老设备
        if (this.currentEquipmentType != item.inventoryStatus) {
          this.tipMessage("不允许删除该设备!");
        } else {
          this.deteleEquipmentList.push(item);
          this.equipmentList.splice(index - this.addEquipmentList.length, 1);
        }
      }
    },

    // 清空
    clear() {
      this.secondarytype = 1;
      this.secondaryForm.title = "确认要清空信息吗？";
      this.secondaryForm.content = "确定要清空录入的全部硬件数据吗？";
      this.secondaryDialogVisible = true;
    },

    // 清除设备
    clearEquipment() {
      this.addEquipmentList = [];
      this.secondaryDialogVisible = false;
    },

    // 提示信息
    tipMessage(message) {
      return this.$message({
        message,
        type: "warning"
      });
    },

    // 关闭抽屉
    close() {
      this.addEquipmentList = [];
      this.deteleEquipmentList = [];
      this.equipmentList = [];
      this.$emit("closeEnterWarehouseDrawer");
    }
  }
};
</script>
<style lang="less" scoped>
.index {
  /deep/.details-drawer {
    .el-drawer__header {
      padding: 0;
      margin-bottom: 0;
    }
    .el-drawer__body {
      height: 100%;
      .drawer-content {
        height: 100%;
        .title {
          padding: 16px;
          border-bottom: 1px solid #e1e5ed;
          display: flex;
          align-items: center;
          font-weight: bold;
          font-size: 16px;
          color: #3a4762;
          .close-icon {
            font-size: 18px;
            margin-right: 15px;
            cursor: pointer;
          }
        }
        .content {
          padding: 16px 24px;
          .fill-box {
            font-weight: bold;
            font-size: 16px;
            color: #15233f;
            display: flex;
            align-items: center;
            .hr {
              width: 2px;
              height: 12px;
              background: #2e6be6;
              border-radius: 1px;
              margin-right: 6px;
            }
            .question-circle {
              width: 16px;
              height: 16px;
              margin-left: 4px;
              cursor: pointer;
            }
          }
          .tips {
            padding: 12px 17px;
            background: #e6eeff;
            border-radius: 2px;
            border: 1px solid #2e6be6;
            margin-top: 16px;
            display: flex;
            font-size: 14px;
            color: #15233f;
            align-items: center;
            .icon-warn {
              width: 14px;
              height: 14px;
              margin-right: 9px;
            }
          }
          .from-box {
            margin-top: 24px;
            .el-form {
              .el-form-item__label,
              .el-form-item__content,
              .el-input__icon {
                line-height: 32px;
              }
              .el-icon-arrow-up:before {
                content: "";
              }
              .el-select__caret,
              .el-icon-arrow-down {
                color: #3a4762;
              }
              .el-input__inner {
                height: 32px;
                line-height: 32px;
              }
              .el-cascader {
                line-height: 32px;
                .el-icon-arrow-down {
                  transform: rotate(180deg);
                }
                .is-reverse {
                  transform: rotate(0);
                }
                .el-icon-arrow-down:before {
                  content: "";
                }
                .el-input__icon:after {
                  content: "";
                }
              }
              .el-radio-group {
                margin-left: -180px;
                .el-radio__inner {
                  width: 16px;
                  height: 16px;
                }
                .el-radio__label {
                  font-size: 14px;
                  color: #323233;
                }
                .el-radio__inner::after {
                  width: 8px;
                  height: 8px;
                  background: #2e6be6;
                }
                .el-radio__input.is-checked .el-radio__inner {
                  background: #fff;
                }
              }
              .el-form-item__error {
                left: 12px;
              }
              .type-style {
                margin-left: 30px;
                .question-circle-outlined {
                  cursor: pointer;
                  margin-bottom: -3px;
                  margin-left: 4px;
                }
              }
              .hardwareNumber {
                display: flex;
                margin-left: 6px;
                .enter {
                  font-size: 14px;
                  color: #ffffff;
                  width: 52px;
                  height: 32px;
                  background: #2e6be6;
                  border-radius: 2px;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  cursor: pointer;
                }
              }
            }
          }
          .equipment-list {
            margin-top: 24px;
            .list-header {
              display: flex;
              align-items: center;
              height: 52px;
              background: #f7f8fa;
              box-shadow: 0px 1px 0px 0px #ebedf0;
              padding: 0 16px;
              box-sizing: border-box;
              justify-content: space-between;
              .header-item {
                font-weight: bold;
                font-size: 14px;
                color: #15233f;
                text-align: left;
              }
              .header-item:first-child {
                width: 200px;
              }
              .header-item:nth-child(2) {
                width: 200px;
              }
            }
            .infinite-list {
              padding: 0;
              margin: 0;
              height: 210px;
              overflow-y: scroll;
              .infinite-list-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 52px;
                padding: 0 16px;
                border-bottom: 1px solid #e1e5ed;
                box-sizing: border-box;
                .list-item {
                  text-align: left;
                  width: 200px;
                  font-size: 14px;
                  color: #3a4762;
                }
                .delete {
                  font-size: 14px;
                  color: #e63746;
                  cursor: pointer;
                }
              }
              .list-item-active {
                background: #e6eeff;
              }
              .no-more {
                font-size: 14px;
                color: #575757;
                margin-top: 8px;
              }
            }
            .null-data-box {
              margin-top: 32px;
              font-size: 14px;
              color: #575757;
              .null-data-icon {
                width: 80px;
                height: 55px;
              }
            }
          }
          .equipment-statistics {
            font-size: 14px;
            color: #3a4762;
            margin-top: 15px;
            text-align: left;
          }
          .btns {
            margin-top: 24px;
            display: flex;
            justify-content: flex-end;
            .common {
              width: 76px;
              height: 32px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 14px;
              cursor: pointer;
              border-radius: 2px;
            }
            .clear {
              border: 1px solid #dcdfe6;
              color: #606266;
              box-sizing: border-box;
            }
            .put-storage {
              background: #2e6be6;
              color: #ffffff;
              margin-left: 16px;
            }
          }
        }

        .infinite-list::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }
        // 滚动条的轨道的两端按钮，允许通过点击微调小方块的位置。
        .infinite-list::-webkit-scrollbar-button {
          display: none;
        }
        // 滚动条的轨道（里面装有Thumb）
        .infinite-list::-webkit-scrollbar-track {
          background: transparent;
        }
        // 滚动条的轨道（里面装有Thumb）
        .infinite-list::-webkit-scrollbar-track-piece {
          background-color: transparent;
        }
        // 滚动条里面的小方块，能向上向下移动（或往左往右移动，取决于是垂直滚动条还是水平滚动条）
        .infinite-list::-webkit-scrollbar-thumb {
          background: rgba(144, 147, 153, 0.3);
          cursor: pointer;
          border-radius: 4px;
        }
        // 边角，即两个滚动条的交汇处
        .infinite-list::-webkit-scrollbar-corner {
          display: none;
        }
        // 两个滚动条的交汇处上用于通过拖动调整元素大小的小控件
        .infinite-list::-webkit-resizer {
          display: none;
        }
      }
    }
  }
}
</style>
