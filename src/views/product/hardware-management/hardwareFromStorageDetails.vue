<template>
  <div class="index">
    <div class="searchBox">
      <el-row :gutter="20">
        <!-- 硬件编号 -->
        <el-col :span="6">
          <div class="searchItem">
            <div class="name">硬件编号</div>
            <el-input
              v-model="from.deviceNo"
              placeholder="请输入硬件编号"
              style="width: 240px;"
            ></el-input></div
        ></el-col>
        <!-- 硬件类型 -->
        <el-col :span="6">
          <div class="searchItem">
            <div class="name">硬件类型</div>
            <MultiDeviceSearch v-model="from.deviceTypeList" />
            </div
            </div
        ></el-col>
        <el-col :span="4">
          <div class="btns">
            <div class="search common" @click="search()">搜索</div>
            <div class="reset common" @click="reset()">重置</div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="main">
      <div class="export-box">
        <div class="export" @click="exportData()">导出</div>
      </div>
      <div class="tableBox">
        <el-table
          :data="tableData"
          height="500"
          style="width: 100%"
          :header-cell-style="{
            color: '#15233F',
            background: '#F7F8FA',
            'font-weight': 'bold',
            'font-size': '14px'
          }"
        >
          <el-table-column align="center" label="序号" width="70" fixed>
            <template slot-scope="scope">
              &nbsp;{{
                page === 1
                  ? prefixInteger((page - 1) * pageSize + scope.$index + 1, 2)
                  : (page - 1) * pageSize + scope.$index + 1
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="exitNo"
            label="出库单编号"
            width="150"
          ></el-table-column>
          <el-table-column label="出库类型">
            <template slot-scope="scope">
              <div>
                {{ scope.row.type == "ENQUIRY_OUT" ? "要货出库" : "返厂出库" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="硬件类型" width="120">
            <template slot-scope="scope">
              <div>
                {{
                  scope.row.deviceType == "HP"
                    ? "掌护血压计"
                    : scope.row.deviceType == "BPG"
                    ? "台式血压计"
                    : scope.row.deviceType == "WATCH"
                    ? "智能手表"
                    : "体重秤"
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="deviceNo" label="硬件编号"></el-table-column>
          <el-table-column prop="warehouseName" label="关联仓库名称">
          </el-table-column>
          <el-table-column
            prop="warehouseLeader"
            label="负责人"
          ></el-table-column>
          <el-table-column
            prop="province"
            label="仓库所属省份"
            width="120"
          ></el-table-column>
          <el-table-column
            prop="city"
            label="仓库所属城市"
            width="120"
          ></el-table-column>
          <el-table-column
            prop="hospitalName"
            label="仓库所属医院"
            width="120"
          ></el-table-column>
          <el-table-column
            prop="inpatientWard"
            label="仓库所属病区"
            width="120"
          ></el-table-column>
          <el-table-column label="备注" width="180">
            <template slot-scope="scope">
              <el-tooltip
                class="item"
                effect="dark"
                :content="scope.row.remark"
                placement="top-start"
              >
                <div class="remarks-box">
                  {{ scope.row.remark }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>

    <!-- 导出 -->
    <Export
      :exportFileName="exportFileName"
      :total="total"
      :dialogVisible="dialogVisible"
      @closeExportDialog="dialogVisible = false"
      @exportSure="exportSure"
    ></Export>
  </div>
</template>
<script>
import Export from "./components/Export.vue";
import MultiDeviceSearch from "./components/MultiDeviceSearch.vue";
import { prefixInteger } from "@/util/util";
import { httpReq } from "@/http";
import { getInventoryStatus, getDetectionStatus } from "./index";
export default {
  data() {
    return {
      prefixInteger,
      getInventoryStatus,
      getDetectionStatus,
      tableData: [],
      page: 1,
      total: 0,
      pageSize: 10,
      dialogVisible: false,
      exportFileName: "",

      from: { deviceNo: "", deviceTypeList: [null] },
    };
  },
  components: {
    Export,
    MultiDeviceSearch
  },
  mounted() {
    this.getDataList();
  },
  methods: {
    // 获取列表数据
    getDataList() {
      httpReq({
        url: "/warehouse/exit/page/query/detail",
        method: "post",
        data: {
          ...this.from,
          deviceTypeList: this.from.deviceTypeList
            .slice(-1)
            .join(",")
            .split(",")
            .filter(it => it),
          exitId: this.$route.query.exitId,
          pageNumber: this.page,
          pageSize: this.pageSize
        }
      }).then(res => {
        let { contents, total } = res.data;
        this.tableData = contents;
        this.total = total;
      });
    },

    // 导出
    exportData() {
      if (this.total === 0) {
        this.$message({
          message: "导出失败，当前筛选下没有数据",
          type: "warning"
        });
      } else {
        this.exportFileName = `硬件出库单（${this.$route.query.exitNo}）`;
        this.dialogVisible = true;
      }
    },

    // 导出确定
    exportSure(exportFileName) {
      if (!exportFileName) {
        this.$message({
          message: "请填写导出文件名称！",
          type: "warning"
        });
      } else {
        let params = {
          ...this.from,
          exitId: this.$route.query.exitId,
          pageNumber: this.page,
          pageSize: this.pageSize,
          fileName: exportFileName
        };
        let dataArr = [];
        for (let i in params) {
          dataArr.push(`${i}=${params[i] || ""}`);
        }
        let url = `${
          process.env.VUE_APP_baseUrl
        }warehouse/exit/export/detail?${dataArr.join("&")}`;
        this.dowloadFiles(url);
        this.$message({
          message: "导出成功！",
          type: "success"
        });
        this.dialogVisible = false;
      }
    },

    //当前页面下载
    dowloadFiles(url) {
      const link = document.createElement("a");

      link.setAttribute("download", "xxx.xlsx");

      link.setAttribute("href", url);

      link.style.display = "none";

      document.body.appendChild(link);

      link.click();

      link.remove();
    },

    // 分页
    handleSizeChange(val) {
      this.pageSize = val;
      this.getDataList();
    },
    handleCurrentChange(val) {
      this.page = val;
      this.getDataList();
    },

    // 搜索
    search() {
      this.getDataList();
    },

    // 重置
    reset() {
      this.page = 1;
      this.pageSize = 10;
      this.from = { deviceNo: "", deviceType: "" };
      this.getDataList();
    }
  }
};
</script>
<style lang="less" scoped>
@import "./css/export-dialog.less";
.index {
  padding: 16px;
  box-sizing: border-box;
  flex: 1;
  .searchBox {
    background: #ffffff;
    border-radius: 4px;
    padding: 16px 24px;
    box-sizing: border-box;
    /deep/ .searchItem {
      display: flex;
      align-items: center;
      margin-bottom: 24px;
      .name {
        margin-right: 12px;
        font-size: 16px;
        color: #3a4762;
        width: 96px;
        text-align: right;
      }
      .el-input__inner,
      .el-range-separator {
        height: 32px;
      }
      .el-input__icon {
        line-height: 32px;
      }
      .el-icon-arrow-up:before {
        content: "";
      }
      .el-select__caret {
        color: #3a4762;
      }
    }
    .btns {
      display: flex;
      justify-content: center;
      .common {
        cursor: pointer;
        display: flex;
        justify-content: center;
        font-size: 14px;
        align-items: center;
      }
      .search {
        width: 76px;
        height: 32px;
        background: #2e6be6;
        border-radius: 2px;
        color: #ffffff;
      }
      .reset {
        width: 76px;
        height: 32px;
        border-radius: 2px;
        border: 1px solid #dcdfe6;
        box-sizing: border-box;
        color: #606266;
        margin-left: 16px;
      }
    }
  }
  .main {
    margin-top: 16px;
    padding: 16px 24px;
    background: #ffffff;
    .export-box {
      display: flex;
      justify-content: flex-end;
      margin-top: 12px;
      .export {
        width: 76px;
        height: 32px;
        background: #2e6be6;
        border-radius: 2px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        font-size: 14px;
        color: #ffffff;
      }
    }
    .tableBox {
      margin-top: 16px;
      .remarks-box {
        width: 180px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .pagination {
      margin-top: 30px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
.tableBox::-webkit-scrollbar {
  /* 隐藏默认的滚动条 */
  -webkit-appearance: none;
}
.tableBox::-webkit-scrollbar:vertical {
  /* 设置垂直滚动条宽度 */
  width: 0;
}
</style>
