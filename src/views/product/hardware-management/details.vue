<template>
  <div class="details">
    <div class="top">
      <div class="leftBox">
        <div class="imgBox"><img src="@/assets/image/hemopiezometer.png" alt=""></div>
        <div class="equipmentName">{{equipmentName}}</div>
      </div>
      <div class="rightBox">
        <div class="rightItem" v-for="(item,i) in detailsMsg" :key="i">
          <div class="name">{{ item.title }}</div>
          <div class="value">{{ item.value }}</div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="tabBox">
        <div class="tabItem" v-for="item in tabList" :key="item.id" @click="changeData(item.id)">
          <div :class="item.id==tabFlag?'itemNameActive':'itemName'">{{item.name}}</div>
          <div :class="item.id==tabFlag?'hrActive':'hr'"></div>
        </div>
      </div>
      <div class="tableBox">
        <el-table
          :data="tableData"
          style="width: 100%"
          height="528"
        >
          <el-table-column
            v-if="tabFlag==1"
            label="操作">
            <template slot-scope="scope">
              <div>{{changeOperation(scope.row.operation_type) }}</div>
            </template>
          </el-table-column>
          <el-table-column
            v-else
            prop="name"
            label="测试人">
          </el-table-column>

          <el-table-column
            v-if="tabFlag==1"
            label="操作时间">
            <template slot-scope="scope">
              <div>{{timeMode(scope.row.operation_time,'.').datestr }}</div>
            </template>
          </el-table-column>
          <el-table-column
            v-else
            prop="test_time"
            label="操作时间">
          </el-table-column>

          <el-table-column
            :label="tabFlag==1?'操作流向':'测试结果'"
            >
            <template slot-scope="scope">
              <div>{{ tabFlag==1?scope.row.describe:scope.row.result }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="备注">
            <template slot-scope="scope">
              <div>{{ tabFlag==1?scope.row.remark:scope.row.test_remark }}</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
  </div>
</template>
<script>
import { httpReq } from "@/http";
import {timeMode} from  '@/util/util.js'

export default{
  data(){
    return{
      equipmentName:'爱奥乐',
      detailsMsg:[
        {
          title:'厂商',
          value:'爱奥乐',
        },
        {
          title:'SN号',
          value:'22k01988899',
        },
        {
          title:'出库状态',
          value:'已出库',
        },
        {
          title:'设备状态',
          value:'已出库',
        },
        {
          title:'绑定状态',
          value:'已绑定',
        },
        {
          title:'绑定患者',
          value:'某某某',
        },
      ],
      tabList:[
        {
          id:1,
          name:'设备流向',
        },
        {
          id:2,
          name:'测试记录',
        }
      ],
      tabFlag:1,   // 1--设备流向   2--测试记录

      tableData: [],

      currentPage:1,
      total:0,
      pageSize:10,
      timeMode,
    }
  },
  created() {
    this.getEquipmentData()
    this.querydetail()
  },
  computed:{
    changeOperation(){
      return function (val){
        let obj = val==0?'入库':val==1?'退货入库':val==2?'出库':val==3?'退货出库':val==4?'移交':val==5?'绑定':'解绑'
        return obj
      }
    }
  },
  methods:{
    changeData(id){
      this.tabFlag=id
      this.currentPage=1
      this.pageSize=10
      if(id==1){
        this.getEquipmentData()
      }else {
        this.queryTestRecord()
      }
    },

    // 每一页展示数据条数改变
    handleSizeChange(val) {
      this.pageSize=val
      this.getEquipmentData()
    },

    // 页数变化
    handleCurrentChange(val) {
      this.currentPage=val
      this.getEquipmentData()
    },

    //  获取设备流向数据
    getEquipmentData(){
      httpReq({
        url: "/hardware/flowHistory",
        method: "post",
        data: {
          hardwareId: this.$route.query.id,
          page:this.currentPage,
          pageSize:this.pageSize
        },
      })
        .then((res) => {
          this.total=res.data.records
          this.tableData=res.data.data
        })
        .catch((err) => {
        });
    },

    //  查看测试记录
    queryTestRecord(){
      httpReq({
        url: "/hardware/testHistory",
        method: "post",
        data: {
          hardwareId: this.$route.query.id,
          page:this.currentPage,
          pageSize:this.pageSize
        },
      })
        .then((res) => {
          this.total=res.data.records
          this.tableData=res.data.data
        })
        .catch((err) => {
        });
    },

    //  查看血压计详情
    querydetail(){
      httpReq({
        url: "/hardware/detail",
        method: "post",
        data: {
          hardwareId: this.$route.query.id,
        },
      })
        .then((res) => {
          console.log(res,'------------')
          let {data}=res
          this.equipmentName=data.type==1?'爱奥乐':'脉搏波'
         this.detailsMsg=[
            {
              title:'厂商',
              value:data.type==1?'爱奥乐':'脉搏波',
            },
            {
              title:'SN号',
              value:data.so_no,
            },
            {
              title:'出库状态',
              value:data.inventory_status==0?'入库':data.inventory_status==1?'退货入库':data.inventory_status==2?'出库':'退货出库',
            },
            {
              title:'设备状态',
              value:data.status==0?'正常':data.status==1?'异常 ':'待测试',
            },
            {
              title:'绑定状态',
              value:data.binding_status==1?'未绑定':'已绑定',
            },
            {
              title:'绑定患者',
              value:data.user_name||'--',
            },
          ]
        })
        .catch((err) => {
        });
    },
  }
}
</script>
<style lang="less" scoped>
.details{
  text-align: left;
  padding: 16px;
  box-sizing: border-box;
  .top{
    display: flex;
    align-items: center;
    padding: 24px;
    background: #fff;
    justify-content: space-between;
    .leftBox{
      display: flex;
      align-items: center;
      .imgBox{
        width: 50px;
        height: 50px;
        img{
          width: 50px;
        }
      }
      .equipmentName{
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #000000;
        margin-left: 28px;
      }
    }
    .rightBox{
      display: flex;
      .rightItem{
        display: flex;
        margin-left: 100px;
        .name{
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          color: #999999;
        }
        .value{
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          color: #303133;
          margin-left: 16px;
        }
      }
    }
  }
  .content{
    background: #fff;
    margin-top: 11px;
    .tabBox{
      display: flex;
      height: 60px;
      border-bottom: 1px solid #EBEEF5;
      box-sizing: border-box;
      align-items: flex-end;
      padding-left: 20px;
      .tabItem{
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 110px;
        cursor: pointer;
        .itemName{
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #111111;
        }
        .itemNameActive{
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #0C88BE;
        }
        .hr{
          width: 32px;
          height: 4px;
          background: transparent;
          border-radius: 2px;
          margin-top: 12px;
        }
        .hrActive{
          width: 32px;
          height: 4px;
          background: #0C88BE;
          border-radius: 2px;
          margin-top: 12px;
        }
      }
    }
    .tableBox{
      padding: 16px;
      box-sizing: border-box;
    }
  }
  .pagination{
    margin-top: 30px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
