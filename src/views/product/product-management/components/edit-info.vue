<!-- 添加/修改服务包信息 -->
<template>
  <div class="edit-box">
    <el-form
      size="mini"
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="demo-ruleForm"
      :disabled="dialogShowType == 1"
    >
      <el-form-item label="服务包类型" class="minclass" prop="type">
        <el-select v-model="ruleForm.type" placeholder="请选择服务包类型">
          <el-option label="科研设备押金" :value="11"></el-option>
          <el-option label="2400服务包" :value="1"></el-option>
          <el-option label="365服务包" :value="5"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="服务包名称" prop="name" class="minclass">
        <el-input v-model="ruleForm.name"></el-input>
      </el-form-item>
      <el-form-item label="服务包价格" prop="price">
        <el-input type="number" v-model="ruleForm.price">
          <template slot="append">元</template>
        </el-input>
      </el-form-item>
      <el-form-item label="针对人群" prop="targetCustomer">
        <el-input v-model="ruleForm.targetCustomer"></el-input>
      </el-form-item>
      <el-form-item label="服务包内容" prop="content">
        <!-- <div
          v-html="ruleForm.content"
          class="service-content"
          v-if="!showEditor && ruleForm.isAdd == false"
          @click="showEditor = true"
          title="点击进行编辑"
        ></div> -->
        <WangEditor
          :content="ruleForm.content"
          :id="id"
          @editorchange="editorchange"
        ></WangEditor>
      </el-form-item>
    </el-form>
    <div class="sub-btn" v-if="dialogShowType == 1">
      <el-button size="mini" @click="resetForm('ruleForm')">关闭</el-button>
    </div>
    <div class="sub-btn" v-else>
      <el-button size="mini" @click="resetForm('ruleForm')">取消</el-button>
      <el-button size="mini" type="primary" @click="submitForm('ruleForm')"
      >保存
      </el-button
      >
    </div>
  </div>
</template>

<script>
import { httpReq } from '@/http'
import WangEditor from './wang-editor.vue'

export default {
  name: '',
  props: {
    id: { require: true },
    dialogShowType: { require: true },
  },
  data () {
    return {
      ruleForm: {
        name: '',
        type: '',
        price: '',
        targetCustomer: '',
        content: '',
      },
      changeContent: '',
      rules: {
        name: [
          { required: true, message: '请输入服务包名称', trigger: 'blur' },
        ],
        type: [
          { required: true, message: '请选择服务包类型', trigger: 'change' },
        ],
        price: [
          { required: true, message: '请填写服务包价格' },
        ],
        targetCustomer: [
          { required: true, message: '请输入针对人群', trigger: 'blur' },
        ],
        content: [
          { required: true, message: '请填写服务包内容', trigger: 'blur' },
        ],
      },
    }
  },

  components: {
    WangEditor,
  },

  computed: {},

  mounted () {
    if (this.id) {
      this.getHospitalInfo(this.id)
    }
  },

  methods: {
    submitForm (formName) {
      this.ruleForm.content = this.changeContent
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.updateInfo(this.ruleForm)
        } else {
          return false
        }
      })
    },
    resetForm (formName) {
      this.$refs[formName].resetFields()
      this.$parent.$parent.dialogFormVisibleEditBox = false
    },
    //添加/修改信息
    updateInfo (info) {
      httpReq({
        url: '/product/productEdit',
        method: 'post',
        data: {
          productId: this.id ? this.id : '',
          price: info.price,
          productName: info.name,
          productType: info.type,
          productDesc: info.content,
          targetCustomer: info.targetCustomer,
        },
      })
        .then((res) => {
          this.$message.success(`${this.id ? '修改' : '新增'}成功`)
          // 修改或新增后通知父组件更新数据
          this.$emit('updateList')
        })
        .catch((err) => {
          this.$message.error(`失败：${err.msg}`)
        })
    },
    // 获取医生信息
    getHospitalInfo (productId) {
      httpReq({
        url: '/product/getProductDetails',
        method: 'post',
        data: {
          productId,
        },
      }).then((res) => {
        this.ruleForm = {
          name: res.data.product_name,
          type: res.data.product_type,
          price: res.data.price,
          targetCustomer: res.data.target_customer,
          content: res.data.product_desc,
        }
        this.changeContent = res.data.product_desc
      })
    },
    // 编辑器内容改变
    editorchange (val) {
      this.changeContent = val
    },
  },
}
</script>
<style lang='less' scoped>
.edit-box {
  /deep/ .el-form {
    .minclass .el-form-item__content {
      width: 260px;
      margin-right: 10px;
    }

    .el-input {
      .el-input__inner::-webkit-outer-spin-button,
      input::-webkit-inner-spin-button {
        -webkit-appearance: none;
      }

      .el-input__inner[type="‘number’"] {
        -moz-appearance: textfield;
      }
    }
  }

  .sub-btn {
    padding: 10px 0;
    text-align: center;
  }
}
</style>
