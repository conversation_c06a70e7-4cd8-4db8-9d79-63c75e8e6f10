import Vue from 'vue'

export let state = Vue.observable({
  //菜单元数据
  menuList: [],
  // layout组件菜单部分渲染数据，默认包含全部路由， 子路由下的name必须包含allMenuList中父菜单的的routerType，用于匹配面包屑和其他逻辑，否则不显示
  allMenuList: [
    {
      title: '客户',
      routerType: 'Client',
      iconClass: 'el-icon-menu',
      // iconUrl: require("../assets/image/menu-icon-client.png"),
      children: [
        {
          title: '合作医院',
          children: [
            {
              title: '医院管理',
              routerUrl: '/index/client-hospital-management',
            },
            { title: '医院客户', routerUrl: '/index/client-hospital-client' },
            {
              title: '客户合作协议',
              routerUrl: '/index/client-labor-agreement',
            },
          ],
        },
        {
          title: '工作室',
          children: [
            { title: '工作室管理', routerUrl: '/index/client-hospital-studio' },
            { title: '工作室账户', routerUrl: '/index/client-studio-account' },
          ],
        },
      ],
    },
    {
      title: '用户',
      routerType: 'Patient',
      iconClass: 'el-icon-user-solid',
      // iconUrl: require("../assets/image/menu-icon-patient.png"),
      children: [{ title: '患者管理', routerUrl: '/index/patient' }],
    },
    {
      title: '公司',
      routerType: 'Company',
      iconClass: 'el-icon-office-building',
      // iconUrl: require("../assets/image/menu-icon-company.png"),
      children: [
        { title: '公司架构', routerUrl: '/index/company-okr' },
        { title: '公司账户', routerUrl: '/index/company-account' },
        { title: '执业备案', routerUrl: '/index/company-review' },
        { title: '员工绩效', routerUrl: '/index/company-stuff-performance' },
        { title: '绩效表上传', routerUrl: '/index/employee-performance' },
        { title: '工资表上传', routerUrl: '/index/employee-wages' },
        { title: '培训资料上传', routerUrl: '/index/training-materials' },
      ],
    },
    {
      title: '产品',
      routerType: 'Product',
      iconClass: 'el-icon-files',
      // iconUrl: require("../assets/image/menu-icon-product.png"),
      children: [
        { title: '服务包管理', routerUrl: '/index/product-management' },
        {
          title: '硬件管理',
          children: [
            {
              title: '硬件列表',
              routerUrl: '/index/HardwareList',
            },
            {
              title: '硬件入库单',
              routerUrl: '/index/HardwarePutInStorageList',
            },
            {
              title: '硬件出库单',
              routerUrl: '/index/hardwareFromStorageList',
            },
            {
              title: '虚拟仓库',
              routerUrl: '/index/virtualWarehouseList',
            },
          ],
        },
      ],
    },
    {
      title: '市场',
      routerType: 'Market',
      iconClass: 'el-icon-files',
      iconUrl: require('../assets/image/menu-icon-market.png'),
      children: [
        {
          title: '医院开发年指标',
          routerUrl: '/index/market-development-year-indicator',
        },
        {
          title: '医院开发月指标',
          routerUrl: '/index/market-development-month-indicator',
        },
        { title: '市场工作计划', routerUrl: '/index/work-plan' },
        { title: '客户拜访记录', routerUrl: '/index/customer-visit-record' },
      ],
    },
    {
      title: '销售',
      routerType: 'sop',
      iconClass: 'el-icon-news',
      iconUrl: require('../assets/image/menu-icon-sop.png'),
      children: [
        {
          title: '销售指标',
          routerUrl: '/index/sopTaskIndicator',
        },
        {
          title: '销售工作计划',
          routerUrl: '/index/sopWorkPlan',
        },
        {
          title: '电子台账',
          routerUrl: '/index/sopElectronicLedger',
        },
      ],
    },
    {
      title: '医学',
      routerType: 'Medicine',
      iconClass: 'el-icon-menu',
      iconUrl: require('../assets/image/menu-icon-medicine.png'),
      children: [
        {
          title: '药品管理',
          children: [
            {
              title: '药品目录',
              routerUrl: '/index/DrugLibrary',
            },
            {
              title: '药品分类',
              routerUrl: '/index/LibraryDrugCategoryManagement',
            },
            {
              title: '配伍规则',
              routerUrl: '/index/LibraryDrugEffectsManagement',
            },
          ],
        },
        {
          title: '咨询问答',
          children: [
            {
              title: '问答目录',
              routerUrl: '/index/ScriptProblemManageLibrary',
            },
            {
              title: '问答分类',
              routerUrl: '/index/ProblemclassificateManageLibrary',
            },
            {
              title: '用户登记回答',
              routerUrl: '/index/UnknownproblemManageLibrary',
            },
          ],
        },
        {
          title: '常用话术',
          routerUrl: '/index/ScriptsManageLibrary',
        },
      ],
    },
    {
      title: '科研',
      routerType: 'Scientific',
      iconClass: 'el-icon-user-solid',
      iconUrl: require('../assets/image/menu-icon-scientific.png'),
      children: [
        // { title: "科研项目管理", routerUrl: "/index/scientific" },
        {
          title: '科研项目管理',
          routerUrl: '/index/project-management',
        },
        { title: '新建项目', routerUrl: '/index/add-new-project' },
        { title: '查看项目', routerUrl: '/index/check-project' },
      ],
    },
    {
      title: '运营',
      routerType: 'Operation',
      iconClass: 'el-icon-menu',
      iconUrl: require('../assets/image/menu-icon-operation.png'),
      children: [
        {
          title: '内容配置',
          children: [
            { title: '术后管理', routerUrl: '/index/operationManagement' },
            { title: '患教中心', routerUrl: '/index/OperationRehabilitation' },
            { title: '学习中心', routerUrl: '/index/OperationconStudyCenter' },
          ],
        },
        {
          title: '问卷管理',
          children: [
            {
              title: '问卷目录',
              routerUrl: '/index/QuestionnaireLibrary',
            },
            {
              title: '问卷分类',
              routerUrl: '/index/LibraryquestionaireManagement',
            },
            {
              title: '问卷题目',
              routerUrl: '/index/LibrarytopicManagement',
            },
          ],
        },
        {
          title: '用户意见',
          routerUrl: '/index/OperationconfigurationFeedBack',
        },
        {
          title: '会议记录',
          routerUrl: '/index/OperationMeetingRecords',
        },
      ],
    },
    {
      title: '财务',
      routerType: 'Finance',
      iconClass: 'el-icon-menu',
      iconUrl: require('../assets/image/menu-icon-finance.png'),
      children: [
        { title: '订单记录', routerUrl: '/index/order' },
        {
          title: '医学顾问费',
          children: [
            { title: '工作室顾问费明细', routerUrl: '/index/studio-detail' },
            { title: '专家顾问费明细', routerUrl: '/index/doctor-detail' },
            { title: '顾问费发放汇总', routerUrl: '/index/release-summary' },
          ],
        },
      ],
    },
    {
      title: '系统',
      routerType: 'System',
      iconClass: 'el-icon-s-tools',
      iconUrl: require('../assets/image/menu-icon-system.png'),
      children: [
        { title: '角色管理', routerUrl: '/index/roles-management' },
        { title: '系统配置', routerUrl: '/index/LoginSettings' },
      ],
    },
  ],
  //权限菜单列表
  permissionList: [],
  /**
                                                                                     该路由表里如果是如果不是菜单路由,则需要标记它属于哪个菜单路由 如:parentPath:['/index/roles-management']
                                                                                     */
  // vueRoute路由信息
  routeList: [
    /**
     * 客户
     * */
    {
      path: '/index/client-hospital-management',
      name: 'ClientHospitalManagement',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/client/partner-hospital/hospital-management/hospital-management'
        ),
      meta: { title: '医院管理' },
    },
    {
      path: '/index/client-hospital-details',
      name: 'ClientHospitalDetail',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/client/partner-hospital/hospital-management/hospital-details'
        ),
      meta: {
        title: '医院详情',
        parentPath: ['/index/client-hospital-management'],
      },
    },
    {
      path: '/index/client-hospital-client',
      name: 'ClientHospital',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/client/partner-hospital/hospital-client/hospital-client'
        ),
      meta: { title: '医院客户' },
    },
    {
      path: '/index/client-labor-agreement',
      name: 'ClientLaborAgreement',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/client/partner-hospital/labor-agreement/laborservice-agreement'
        ),
      meta: { title: '劳务协议' },
    },
    {
      path: '/index/client-hospital-studio',
      name: 'ClientHospitalStudio',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/client/studio/studio-management/studio-list'
        ),
      meta: { title: '工作室管理' },
    },
    {
      path: '/index/client-hospital-studio-details',
      name: 'ClientHospitalDetailStudioDetail',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/client/studio/studio-management/studio-details'
        ),
      meta: {
        title: '工作室详情',
        parentPath: ['/index/client-hospital-studio'],
      },
    },
    {
      path: '/index/client-studio-account',
      name: 'ClientStudioAccount',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/client/studio/studio-account/studio-account'
        ),
      meta: { title: '工作室账户' },
    },

    /**
     * 用户
     * */
    {
      path: '/index/patient',
      name: 'Patient',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/patient/patient-management/patient-list'
        ),
      meta: { title: '患者管理' },
    },
    /**
     * 公司
     * */
    {
      path: '/index/company-okr',
      name: 'CompanyOkr',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/company/company-okr/company-okr'
        ),
      meta: { title: '公司架构' },
    },
    {
      path: '/index/company-okr-detail',
      name: 'CompanyOkrDetails',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/company/company-okr/company-details'
        ),
      meta: { title: '架构详情', parentPath: ['/index/company-okr'] },
    },
    {
      path: '/index/company-account',
      name: 'CompanyAccountManage',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/company/company-account/companyAccount'
        ),
      meta: { title: '公司账户' },
    },
    {
      path: '/index/companyDetail',
      name: 'CompanyDetails',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/company/company-account/companyDetail'
        ),
      meta: { title: '账户详情', parentPath: ['/index/company-account'] },
    },
    {
      path: '/index/company-review',
      name: 'CompanyRecordsReview',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/company/records-review/recordsReview'
        ),
      meta: { title: '执业备案' },
    },
    {
      path: '/index/company-stuff-performance',
      name: 'CompanyPerformanceManagement',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/company/performance-management/performance-management'
        ),
      meta: { title: '绩效管理' },
    },
    {
      path: '/index/company-stuff-performance-details',
      name: 'CompanyPerformanceManagementDetails',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/company/performance-management/performance-details'
        ),
      meta: {
        title: '绩效详情',
        parentPath: ['/index/company-stuff-performance'],
      },
    },
    /**
     * 产品
     * */
    // 硬件列表
    {
      path: '/index/HardwareList',
      name: 'ProductHardwareList',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/product/hardware-management/hardwareList'
        ),
      meta: { title: '硬件列表' },
    },
    // 硬件入库单
    {
      path: '/index/HardwarePutInStorageList',
      name: 'ProductHardwarePutInStorageList',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/product/hardware-management/hardwarePutInStorageList'
        ),
      meta: { title: '硬件入库单' },
    },
    // 硬件入库单-详情
    {
      path: '/index/HardwarePutInStorageListDetails',
      name: 'ProductHardwarePutInStorageListDetails',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/product/hardware-management/hardwarePutInStorageDetails'
        ),
      meta: {
        title: '硬件入库单详情',
        parentPath: ['/index/HardwarePutInStorageList'],
      },
    },
    // 硬件出库单
    {
      path: '/index/hardwareFromStorageList',
      name: 'ProductHardwareFromStorageList',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/product/hardware-management/hardwareFromStorageList'
        ),
      meta: { title: '硬件出库单' },
    },
    // 硬件出库单-详情
    {
      path: '/index/HardwareFromStorageDetails',
      name: 'ProductHardwareFromStorageList',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/product/hardware-management/hardwareFromStorageDetails'
        ),
      meta: {
        title: '硬件出库单详情',
        parentPath: ['/index/hardwareFromStorageList'],
      },
    },
    // 虚拟仓库
    {
      path: '/index/virtualWarehouseList',
      name: 'ProductHardwareVirtualWarehouseList',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/product/hardware-management/virtualWarehouseList'
        ),
      meta: { title: '虚拟仓库' },
    },
    // 血压计详情
    {
      path: '/index/HardwareManagementDetails',
      name: 'ProductHardwareDetails',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/product/hardware-management/details'
        ),
      // views/product/hardware-management
      meta: { title: '血压计详情', parentPath: ['/index/HardwareManagement'] },
    },
    {
      path: '/index/employee-performance',
      name: 'CompanyEmployeePerformance',
      component: () => import('@/views/company/employee-performance/index.vue'),
      meta: { title: '绩效表上传' },
    },
    {
      path: '/index/employee-wages',
      name: 'CompanyEmployeeWages',
      component: () => import('@/views/company/employee-wages/index.vue'),
      meta: { title: '工资表上传' },
    },
    {
      path: '/index/training-materials',
      name: 'CompanyTrainingMaterials',
      component: () => import('@/views/company/training-materials/index.vue'),
      meta: { title: '培训资料上传' },
    },
    {
      path: '/index/product-management',
      name: 'ProductService',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/product/product-management/product-list'
        ),
      meta: { title: '服务包管理' },
    },
    {
      path: '/index/project-management',
      name: 'Scientific-project-management',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/system/project-management/project-management'
        ),
      meta: { title: '项目管理' },
    },
    {
      path: '/index/add-new-project',
      name: 'Scientific-AddNewProject',
      component: () => import('@/views/system/project-management/add-new-project/add-new-project'),
      meta: {
        title: '新建项目',
        parentPath: ['/index/project-management'],
      },
    },
    {
      path: '/index/check-project',
      name: 'Scientific-CheckProject',
      component: () => import('@/views/system/project-management/check-project/check-project'),
      meta: {
        title: '查看项目',
        parentPath: ['/index/project-management'],
      },
    },
    /**
     * 市场
     * */
    {
      path: '/index/market-development-year-indicator',
      name: 'MarketYearIndicator',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/market/development-year-indicator/development-year-indicator'
        ),
      meta: { title: '医院开发年指标' },
    },
    {
      path: '/index/market-development-month-indicator',
      name: 'MarketMonthIndicator',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/market/development-month-indicator/development-month-indicator'
        ),
      meta: { title: '医院开发月指标' },
    },
    {
      path: '/index/customer-visit-record',
      name: 'MarketCustomerVisitRecord',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/market/customer-visit-record/customer-visit-record'
        ),
      meta: { title: '客户拜访记录' },
    },
    {
      path: '/index/work-plan',
      name: 'MarketWorkPlan',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/market/work-plan/work-plan'
        ),
      meta: { title: '工作计划' },
    },

    /**
     * 销售
     * */
    {
      path: '/index/sopTaskIndicator',
      name: 'sopTaskIndicator',
      component: () => import('@/views/sale/sop-management/taskIndicator.vue'),
      meta: { title: '销售指标' },
    },
    {
      path: '/index/sopWorkPlan',
      name: 'sopWorkPlan',
      component: () => import('@/views/sale/sop-management/workPlan.vue'),
      meta: { title: '销售工作计划' },
    },
    {
      path: '/index/sopElectronicLedger',
      name: 'sopElectronicLedger',
      component: () => import('@/views/sale/sop-management/electronicLedger.vue'),
      meta: { title: '电子台账' },
    },
    /**
     * 医学
     * */
    {
      path: '/index/DrugLibrary',
      name: 'MedicineDrugLibrary', //这个地方要2级路由哦
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/medicine/drug-storage/drug-list'
        ),
      meta: { title: '药品库' }, //这个地方要2级路由哦
    },
    {
      path: '/index/LibraryDrugCategoryManagement',
      name: 'MedicineLibraryDrugCategoryManagement', //这个地方要2级路由哦
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/medicine/drug-storage/drag-category'
        ),
      meta: { title: '药品分类' },
    },
    {
      path: '/index/DrugLibraryCateDetails',
      name: 'MedicineDrugLibraryCateDetails',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/medicine/drug-storage/drug-catedetails'
        ),
      meta: {
        title: '药品分类详情',
        parentPath: ['/index/LibraryDrugCategoryManagement'],
      },
    },
    {
      path: '/index/LibraryDrugEffectsManagement',
      name: 'MedicineLibraryDrugEffectsManagement',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/medicine/drug-storage/drugeffets-management'
        ),
      meta: { title: '作用管理' },
    },
    {
      path: '/index/DrugLibraryDetails',
      name: 'MedicineDrugLibraryDetails',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/medicine/drug-storage/drug-details'
        ),
      meta: { title: '药品详情', parentPath: ['/index/DrugLibrary'] },
    },
    {
      path: '/index/DrugLibraryAdd',
      name: 'MedicineDrugLibraryAdd',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/medicine/drug-storage/drug-add'
        ),
      meta: { title: '新增药品', parentPath: ['/index/DrugLibrary'] },
    },

    {
      path: '/index/ScriptProblemManageLibrary',
      name: 'MedicineScriptProblemManageLibrary',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/medicine/scriptproblem-management/problem-management/problem-management'
        ),
      meta: { title: '问题管理' },
    },
    {
      path: '/index/ProblemclassificateManageLibrary',
      name: 'MedicineProblemclassificateManageLibrary',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/medicine/scriptproblem-management/problemclassificate/problemclassificate'
        ),
      meta: { title: '问题分类管理' },
    },
    {
      path: '/index/ClassificationTopicManagementManageLibrary',
      name: 'MedicineClassificationTopicManagementManageLibrary',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/medicine/scriptproblem-management/problemclassificate/classificationTopicManagement'
        ),
      meta: {
        title: '分类问题管理',
        parentPath: ['/index/ProblemclassificateManageLibrary'],
      },
    },
    {
      path: '/index/UnknownproblemManageLibrary',
      name: 'MedicineUnknownproblemManageLibrary',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/medicine/scriptproblem-management/unknownproblem-management/unknownproblem-management'
        ),
      meta: { title: '未知问题' },
    },
    {
      path: '/index/ScriptsManageLibrary',
      name: 'MedicineScriptsManageLibrary',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/medicine/scriptproblem-management/scripts-management/scripts-management'
        ),
      meta: { title: '话术管理' },
    },
    {
      path: '/index/AddScriptProblemManageLibrary',
      name: 'MedicineAddScriptProblemManageLibrary',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/medicine/scriptproblem-management/problem-management/question-adding'
        ),
      meta: {
        title: '问题管理',
        parentPath: [
          '/index/ScriptProblemManageLibrary',
          '/index/ClassificationTopicManagementManageLibrary',
          '/index/ProblemclassificateManageLibrary',
          '/index/UnknownproblemManageLibrary',
        ],
      },
    },
    /**
     * 科研
     * */
    {
      path: '/index/scientific',
      name: 'ScientificHospitalScientific',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/scientific'
        ),
      meta: { title: '科研项目管理' },
    },
    /**
     * 运营
     * */
    {
      path: '/index/operationManagement',
      name: 'PostOperationManagement',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/operation/operational-configuration/postoperative-management/operation-management'
        ),
      meta: { title: '术后管理' },
    },
    {
      path: '/index/OperationRehabilitation',
      name: 'OperationRehabilitationCenter',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/operation/operational-configuration/rehabilitation-center/rehabilitation-center'
        ),
      meta: { title: '患教中心' },
    },
    {
      path: '/index/OperationconfigurationFeedBack',
      name: 'OperationconfigurationFeedBack',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/operation/user-feedback/feedback'
        ),
      meta: { title: '意见反馈' },
    },
    {
      path: '/index/OperationMeetingRecords',
      name: 'OperationMeetingRecords',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/operation/meeting-records/meeting-records'
        ),
      meta: { title: '会议记录' },
    },
    {
      path: '/index/OperationEditbox',
      name: 'OperationEditbox',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/operation/operational-configuration/rehabilitation-center/editbox'
        ),
      meta: {
        title: '患教中心',
        parentPath: ['/index/OperationRehabilitation'],
      },
    },
    {
      path: '/index/OperationconStudyCenter',
      name: 'OperationconStudyCenter',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/operation/operational-configuration/study-center/index'
        ),
      meta: {
        title: '学习中心',
      },
    },
    {
      path: '/index/QuestionnaireLibrary',
      name: 'OperationQuestionnaireLibrary',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/operation/questionnaire-library/class-management/sort-managment'
        ),
      meta: { title: '分类管理' },
    },
    {
      path: '/index/LibraryIssueManagement',
      name: 'OperationLibraryIssueManagement',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/operation/questionnaire-library/class-management/issue-management'
        ),
      meta: { title: '题目管理', parentPath: ['/index/QuestionnaireLibrary'] },
    },
    {
      path: '/index/LibraryAddIssue',
      name: 'OperationLibraryAddIssue',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/operation/questionnaire-library/class-management/add-issue'
        ),
      meta: { title: '新增问题', parentPath: ['/index/LibrarytopicManagement'] },
    },
    {
      path: '/index/LibraryIssueDetail',
      name: 'OperationLibraryIssueDetail',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/operation/questionnaire-library/class-management/issue-detail'
        ),
      meta: {
        title: '问题详情',
        parentPath: ['/index/LibrarytopicManagement', '/index/LibraryIssueManagement'],
      },
    },
    {
      path: '/index/LibraryquestionaireManagement',
      name: 'OperationLibraryquestionaireManagement',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/operation/questionnaire-library/questionnaire-management/questionnaire-management'
        ),
      meta: { title: '问卷管理' },
    },
    {
      path: '/index/LibraryquestionaireManagementDetails',
      name: 'OperationLibraryquestionaireManagementDetails',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/operation/questionnaire-library/questionnaire-management/questionnaire-details'
        ),
      meta: {
        title: '问卷详情',
        parentPath: ['/index/LibraryquestionaireManagement'],
      },
    },
    {
      path: '/index/LibraryquestionaireManagementPreview',
      name: 'OperationLibraryquestionaireManagementPreview',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/operation/questionnaire-library/questionnaire-management/questionnaire-preview'
        ),
      meta: {
        title: '问卷预览',
        parentPath: ['/index/LibraryquestionaireManagement'],
      },
    },
    {
      path: '/index/LibraryquestionaireManagementEdit',
      name: 'OperationLibraryquestionaireManagementEdit',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/operation/questionnaire-library/questionnaire-management/edit-questionnaire'
        ),
      meta: {
        title: '编辑问卷',
        parentPath: ['/index/LibraryquestionaireManagement'],
      },
    },
    {
      path: '/index/LibrarytopicManagement',
      name: 'OperationLibrarytopicManagement',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/operation/questionnaire-library/topic-management/topic-management'
        ),
      meta: { title: '题目管理' },
    },
    {
      path: '/index/LibraryquestionaireManagementAdd',
      name: 'OperationLibraryquestionaireManagementAdd',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/operation/questionnaire-library/questionnaire-management/add-questionnaire'
        ),
      meta: {
        title: '问卷管理',
        parentPath: ['/index/LibraryquestionaireManagement'],
      },
    },
    /**
     * 财务
     * */
    {
      path: '/index/studio-detail',
      name: 'FinanceStudioDetail',
      component: () => import('@/views/finance/medical-consultant-fees/studio-detail/index'),
      meta: { title: '工作室明细' },
    },
    {
      path: '/index/studio-detail-details',
      name: 'FinanceStudioDetails',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/finance/medical-consultant-fees/studio-detail/details'
        ),
      meta: { title: '详情', parentPath: ['/index/studio-detail'] },
    },
    {
      path: '/index/doctor-detail',
      name: 'FinanceDoctorDetail',
      component: () => import('@/views/finance/medical-consultant-fees/doctor-details/index'),
      meta: { title: '医生明细' },
    },
    {
      path: '/index/doctor-details-details',
      name: 'FinanceDoctorDetails',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/finance/medical-consultant-fees/doctor-details/details'
        ),
      meta: { title: '详情', parentPath: ['/index/doctor-detail'] },
    },
    {
      path: '/index/release-summary',
      name: 'FinanceReleaseSummary',
      component: () => import('@/views/finance/medical-consultant-fees/release-summary/index'),
      meta: { title: '发放汇总' },
    },
    {
      path: '/index/order',
      name: 'FinanceOrder',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/finance/order-list/order-list'
        ),
      meta: { title: '订单记录' },
    },
    /**
     * 系统
     * */
    {
      path: '/index/LoginSettings',
      name: 'SystemLoginSettings',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/system/settings-management/multiterminal-login/multiterminal-login'
        ),
      meta: { title: '登录设置' },
    },
    {
      path: '/index/roles-management',
      name: 'SystemRoles-Management',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/system/roles-management/roles-management'
        ),
      meta: { title: '角色管理' },
    },
    {
      path: '/index/add-roles',
      name: 'SystemAdd-roles',
      component: () =>
        import(
          /* webpackChunkName: "uploadPatientRecord" */
          '@/views/system/roles-management/add-roles'
        ),
      meta: { title: '新增角色', parentPath: ['/index/roles-management'] },
    },

    //KOL任务管理
  ],
  // routeList:[],
  whiteRouteList: ['/', '/login'],
})
export let mutations = {
  //根据当登录返回的权限内容处理一份真实的菜单,在登录成功的时候调用
  //接收权限菜单并打标识
  getPermissionList(list) {
    state.permissionList = list
    let copyMenuList = JSON.parse(JSON.stringify(state.allMenuList))
    //最后一层做标记
    this.setNewMenuList(copyMenuList)
    let times = this.getMaxFloor(copyMenuList)
    //根据树的深度处理父级节点,有多少级处理多少次
    for (let i = 0; i < times - 1; i++) {
      this.dealParentPermissionNode(copyMenuList)
    }
    //判断父级节点是否有权限展示
    this.deleteMenuItem(copyMenuList)
    copyMenuList = this.removeNodeInTree(copyMenuList)
    state.menuList = copyMenuList
    //维护真实的路由列表
    this.getRealRouteList(list)
  },
  //判断当前节点是不是权限允许节点
  isPermissionNode(url) {
    let urls = state.permissionList.map((item) => item.path)
    return urls.includes(url)
  },
  //遍历原始数据
  setNewMenuList(list) {
    list.forEach((item) => {
      if (item.children && item.children.length) {
        item['permission'] = true
        this.setNewMenuList(item.children)
      } else {
        if (this.isPermissionNode(item.routerUrl)) {
          item['permission'] = true
        } else {
          item['permission'] = false
        }
      }
    })
  },
  //删除不相关项
  deleteMenuItem(list) {
    list.forEach((item, index) => {
      if (item.children && item.children.length) {
        if (!item.children.some((_item) => _item.permission && _item.permission === true)) {
          item['isPermission'] = false
        } else {
          item['isPermission'] = true
        }
        this.deleteMenuItem(item.children)
      }
    })
  },
  //根据子级节点处理父级节点的权限
  dealParentPermissionNode(list) {
    if (list) {
      list.forEach((item) => {
        if (item.children && item.children.length) {
          if (!item.children.some((_item) => _item.permission && _item.permission === true)) {
            item['permission'] = false
          } else {
            item['permission'] = true
          }
        }
        this.dealParentPermissionNode(item.children)
      })
    }
  },
  removeNodeInTree(list) {
    // 通过id从数组（树结构）中移除元素
    return list.filter((item) => item.isPermission)
  },
  getMaxFloor(treeData) {
    let floor = 0
    let v = this
    let max = 0

    function each(data, floor) {
      data.forEach((e) => {
        e.floor = floor
        if (floor > max) {
          max = floor
        }
        if (e.children && e.children.length > 0) {
          each(e.children, floor + 1)
        }
      })
    }

    each(treeData, 1)
    return max
  },
  //维护真实的路由列表
  getRealRouteList(list) {
    let realRouteList = []
    list.forEach((item) => {
      state.routeList.forEach((route) => {
        //维护菜单路由
        if (route.path === item.path) {
          realRouteList.push(route)
        }
        //维护下面菜单的嵌套路由
        if (route.meta.parentPath && route.meta.parentPath.includes(item.path)) {
          realRouteList.push(route)
        }
      })
    })
    state.routeList = this.deduplicationPath(realRouteList)
  },
  isCurrentRouteReal(path) {
    let routes = state.routeList.map((item) => item.path)
    return routes.includes(path)
  },
  isWhiteList(path) {
    return state.whiteRouteList.includes(path)
  },
  deduplicationPath(tempArr) {
    let result = []
    let obj = {}
    for (let i = 0; i < tempArr.length; i++) {
      if (!obj[tempArr[i].path]) {
        result.push(tempArr[i])
        obj[tempArr[i].path] = true
      }
    }
    return result
  },
  getFirstDefalutPath() {
    let realMenuListPathList = []
    state.menuList[0].children.forEach((item) => {
      if (item.routerUrl) {
        if (item.permission === true) {
          realMenuListPathList.push(item.routerUrl)
        }
      } else {
        item.children.forEach((_item) => {
          if (_item.permission === true) {
            realMenuListPathList.push(_item.routerUrl)
          }
        })
      }
    })
    return realMenuListPathList[0]
  },
}
