let hosOrgMixin ={
  data(){
    return{
      chartW: 0,
      chartH: 0,
      deg: 0,
      top: 20,
      left: 0,
      scale: "scale(1)",
      size: 0,
      nodes:[],
    }
  },
  created() {

  },
  mounted() {
    // 兼容火狐浏览器
    this.mousewheelevt = /Firefox/i.test(navigator.userAgent) ? "DOMMouseScroll" :"mousewheel";
    this.$nextTick(()=>{
      let _dom = document.getElementsByClassName("org-content")
      _dom[0].addEventListener(this.mousewheelevt,this.wheelHandle,{passive:false})
    })
  },
  methods: {
    /**
     * 鼠标滚动 实现放大缩小
     */
    wheelHandle(e) {
      const ev = e || window.event; // 兼容性处理 => 火狐浏览器判断滚轮的方向是属性 detail，谷歌和ie浏览器判断滚轮滚动的方向是属性 wheelDelta
      ev.preventDefault();
      // dir = -dir; // dir > 0 => 表示的滚轮是向上滚动，否则是向下滚动 => 范围 (-120 ~ 120)
      const dir = ev.detail ? ev.detail * -120 : ev.wheelDelta;
      //滚动的数值 / 2000 => 表示滚动的比例，用此比例作为图片缩放的比例
      this.imgScaleHandle(dir / 6000);
    },
    onmousedownHandle(e) {
      const that = this;
      this.$refs.maskBox.onmousemove = function (el) {
        const ev = el || window.event; // 阻止默认事件
        ev.preventDefault();
        that.left += ev.movementX;
        that.top += ev.movementY;
      };
      this.$refs.maskBox.onmouseup = function () {
        // 鼠标抬起时将操作区域的鼠标按下和抬起事件置为null 并初始化
        that.$refs.maskBox.onmousemove = null;
        that.$refs.maskBox.onmouseup = null;
      };
      if (e.preventDefault) {
        e.preventDefault();
      } else {
        return false;
      }
    },
    imgScaleHandle(zoom) {
      this.size += zoom;
      if (this.size < -0.8) {
        this.size = -0.8;
      }
      this.scale = `scale(${1 + this.size}) rotateZ(${this.deg}deg)`;
    },
    parseTreeJson(array) {
      this.nodes =[]
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        // 判断element.children是对象
        if (element.children && element.children.length>0) {
          this.parseTreeJson(element.children)
        } else {
          // 判断是否为子节点
          // if (!(element.children || typeof (element.children) == 'array')) {
          //   // 获得符合的 node
          //   this.nodes.push(element);
          // }
          this.nodes.push(element);
        }
      }
    },
    countTreeNodes(tree, childrenKey = 'children') {
      if (!Array.isArray(tree)) {
        throw new Error('输入的数据必须是数组');
      }

      let count = 0;

      function traverse(nodes) {
        for (const node of nodes) {
          count += 1;
          if (Array.isArray(node[childrenKey])) {
            traverse(node[childrenKey]);
          }
        }
      }

      traverse(tree);

      return count;
    },
    getMaxFloor (treeData) {
      let floor = 0
      let v = this
      let max = 0
      function each (data, floor) {
        data.forEach(e => {
          e.floor = floor
          if (floor > max) {
            max = floor
          }
          if (e.children && e.children.length > 0) {
            each(e.children, floor + 1)
          }
        })
      }
      each(treeData,1)
      return max
    }

  },
}

export default hosOrgMixin;

