<template>
    <div class="boundassistant">
         <el-dialog title="绑定血压计" :visible.sync="isShow" center width="35%" class="">
            
            <el-form :model="rulesForm" :rules="rules"  label-width="100px"  ref="rulesForm">
                <el-form-item label="血压计编号" prop="soNo">
                    <el-input v-model="rulesForm.soNo" placeholder="请输入血压计编号">
                    </el-input>
                </el-form-item>
                <!-- <el-form-item label="血压计类型" prop="type" >
                     <el-select v-model="rulesForm.type" placeholder="请选择血压计类型">
                    <el-option label="现金" :value="1"></el-option>
                    <el-option label="微信" :value="2"></el-option>
                    <el-option label="支付宝" :value="3"></el-option>
                </el-select>
                </el-form-item> -->
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="$emit('closeBloodMark',false)" size="medium">取 消</el-button>
                <el-button type="primary" @click="subBlood('rulesForm')" size="medium">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
export default {
    props:{
        showMark:{required:true},
        patientid:{required:true},
        sono:{required:true}

    },
    data(){
        return{
            isShow:false,
             rulesForm:{
                soNo:'',
            },
            rules:{
                 soNo: [
                    { required: true, message: '请输入血压计编号', trigger: 'blur' },
                ],
            }
        }
    },
     watch:{
        showMark:function(val){
            console.log(val)
            this.isShow=val
        },
        isShow:function(val){
            if(val==false){
                this.$emit('closeBloodMark',false)
            }
        },
        sono:function(val){
            this.rulesForm.soNo=val
        }
    },
    methods:{
        //点击提交血压编号
        subBlood(formName){
               this.$refs[formName].validate((valid) => {
                   console.log(this.rulesForm)
            if (valid) {
                console.log('submit!');
                this.boundBpg(this.patientid,this.rulesForm.soNo,1)
               
            } else {
                console.log('error submit!!');
                return false;
            }
            });
        },
        //绑定血压计
        boundBpg(userId,soNo,type){
            this.axios.post('users/bindUserAndDevice',{
                userId,
                soNo,
                type
            }).then(res=>{
                this.$message.success('绑定血压计成功！')
                //通知刷新列表
                this.$emit('updateList',true)
                this.$emit('closeBloodMark',false)//关闭弹框
            })
        }
    }
}
</script>
<style lang="less" scoped>
    .boundassistant{

    }
</style>