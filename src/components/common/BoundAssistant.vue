<template>
    <div class="boundassistant">
         <el-dialog title="绑定医助" :visible.sync="isShow" center width="35%" class="renewal">
            
            <el-form :model="rulesForm" :rules="rules"  label-width="100px"  ref="renewalForm">
                <el-form-item label="绑定医助" prop="assistantid">
                <el-select v-model="rulesForm.assistantid" placeholder="请选择要绑定的医助">
                            <el-option v-for="(item,index) in allAssistant" :key="index"
                                :label="item.name" :value="item.assistant_id"></el-option>
                        </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="$emit('closeAssistantMark',false)"  size="medium">取 消</el-button>
                <el-button type="primary" @click="subRenewal('renewalForm')"  size="medium">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
export default {
    props:{
        showMark:{required:true},
        patientid:{required:true},
        assistantid:{required:true},
    },
    data(){
        return{
            isShow:false,
             rulesForm:{
                assistantid:'',
            },
            rules:{
                 assistantid: [
                    { required: true, message: '请选择要绑定的医助', trigger: 'blur' },
                ],
            },
            allAssistant:[]
        }
    },
    watch:{
        showMark:function(val){
            console.log(val)
            this.isShow=val
            if(val==false){
                this.rulesForm.assistantid=this.assistantid
            }
        },
        isShow:function(val){
            if(val==false){
                this.$emit('closeAssistantMark',false)
            }
        },
        assistantid:function(val){
            this.rulesForm.assistantid=val
        }
    },
    created(){
        this.getAllAssistant()
    },
    methods:{
        //点击修改医助
        subRenewal(formName){
               this.$refs[formName].validate((valid) => {
                   console.log(this.renewalForm)
            if (valid) {
                console.log('submit!');
                this.updateAssistant(this.patientid,this.rulesForm.assistantid)
               
            } else {
                console.log('error submit!!');
                return false;
            }
            });
        },
        //修改医助
        updateAssistant(userId,assistantId){
            this.axios.post('users/updateBindUserAndAssistant',{
                userId,
                assistantId
            }).then(res=>{
                // console.log(res)
                this.$message.success('绑定医助成功！')
                //通知刷新列表
                this.$emit('updateList',true)
                this.$emit('closeAssistantMark',false)//关闭弹框
            })
        },
         //获取所有医助
         getAllAssistant(){
             this.axios.get('assistants/getAll/AssistantNameAndId',{
                 params:{}
             }).then(res=>{
                 console.log(res)
                 this.allAssistant=res
             })
         }
    }
}
</script>
<style lang="less" scoped>
    .boundassistant{

    }
</style>