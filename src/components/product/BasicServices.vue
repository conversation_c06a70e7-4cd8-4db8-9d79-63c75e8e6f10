<template>
    <div class="basicservices-wrapper">
        <!-- <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
            <el-tab-pane label="基础服务列表" name="first">基础服务管理</el-tab-pane>
            <el-tab-pane label="新增基础服务" name="servicepack">新增基础服务</el-tab-pane>
        </el-tabs> -->
        <p class="til">基础服务管理&emsp;&emsp;<el-button type='primary' size='small' @click="dialogFormVisible = true">添加基础服务</el-button></p>
        <el-divider></el-divider>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="基础服务列表" name="basicelist">
            <el-table
                size="mini"
                :data="basicServiceList"
                style="width: 100%">
                <el-table-column
                label="ID"
                align='center'
                width='120'
                prop="serviceId">
                </el-table-column>
                <el-table-column
                label="基础服务名称"
                align='center'
                prop="serviceName">
                </el-table-column>
                 <!-- <el-table-column
                label="类型"
                prop="type">
                </el-table-column> -->
                <!-- <el-table-column
                align="right">
                <template slot="header" slot-scope="scope">
                </template>
                <template slot-scope="scope">
                    <el-button
                    size="mini"
                    @click="handleEdit(scope.$index, scope.row)">Edit</el-button>
                    <el-button
                    size="mini"
                    type="danger"
                    @click="handleDelete(scope.$index, scope.row)">Delete</el-button>
                </template>
                </el-table-column> -->
            </el-table>
          </el-tab-pane>
         </el-tabs>
         <!-- 添加基础服务弹框 -->
        <el-dialog title="添加基础服务" :visible.sync="dialogFormVisible" center>
            <el-form :model="ruleForm" :rules="rules"  ref="ruleForm">
                <el-form-item label="基础服务名称" prop="serviceName" :rules="rules.serviceName" label-width="120px">
                <el-input v-model="ruleForm.serviceName" autocomplete="off"></el-input>
                </el-form-item>
                 <!-- <el-radio-group v-model="ruleForm.gender">
                    <el-radio label="男" value="1"></el-radio>
                    <el-radio label="女" value="2"></el-radio>
                </el-radio-group>
                </el-form-item>
                 <el-form-item label="电话" prop="phone" :rules="rules.phone"  :label-width="formLabelWidth">
                    <el-input v-model.number="ruleForm.phone" autocomplete="off"></el-input>
                </el-form-item> -->
                <!-- <el-form-item>
                    <el-button @click="dialogFormVisible = false">取消</el-button>
                    <el-button type="primary" @click="addService('ruleForm')">新增</el-button>
                </el-form-item> -->
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogFormVisible = false">取 消</el-button>
                <el-button type="primary" @click="addBasiceService('ruleForm')">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
export default {
    name:'BasicServices',
    data() {
      return {
        activeName: 'basicelist',
        basicServiceList:[],
        dialogFormVisible:false,
          ruleForm: {
                serviceName: ''
                },
            rules: {
                serviceName: [
                   { required: true, message: '请输入医助姓名', trigger: 'blur' },
                ]
            },
      };
    },
    created(){
      this.getBasicServices()
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event);
      },
       handleEdit(index, row) {
        console.log(index, row);
      },
      handleDelete(index, row) {
        console.log(index, row);
      },
      //获取所有基础服务
      getBasicServices(){
        this.axios.get('services',{
          params:{
            page:1
          }
        }).then(res=>{
          console.log(res)
          this.basicServiceList=res
        }).catch(err=>{
          console.log(err)
        })
      },
      //新增基础服务
      addBasiceService(formName){
        this.$refs[formName].validate((valid) => {
          if (valid) {
                console.log('submit!');
                  this.axios({
                    method:'post',
                    url:'services',
                    custom:'noqs',
                    headers:{
                      "Content-Type": "application/json;"
                    },
                    data:this.ruleForm
                  }).then(res=>{
                    console.log(res)
                    this.$message.success('添加成功')
                    this.dialogFormVisible = false
                    this.getBasicServices()
                  }).catch(err=>{
                    console.log(err)
                  })
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      }
    }
}
</script>
<style lang="less" scoped>
.basicservices-wrapper{
     .til{
        text-align: left;
        font-size: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 50px;

    }
}
</style>