<template>
    <div class="servicepack-wrapper">
       <p class="til">服务包管理&emsp;&emsp;<el-button type='primary' size='small' @click="activeName='second',editableTabsValue='second',showUpdate=false">添加服务包</el-button></p>
        <el-divider></el-divider>
          <el-tabs v-model="activeName"  @tab-click="handleClick" @tab-remove="removeTab">
               <el-tab-pane label="服务包管理" name="first">
                <el-table
                    size="mini"
                    :data="servicePackList.filter(servicePackList => !searchKey || servicePackList.productName.toLowerCase().includes(searchKey.toLowerCase()))"
                    style="width: 100%">
                    <el-table-column
                    label="ID"
                    width='120'
                    align='center'
                    prop="productId">
                    </el-table-column>
                    <el-table-column
                    align='center'
                    label="服务包名称"
                    prop="productName">
                    </el-table-column>
                     <el-table-column
                    label="价格"
                    prop="price">
                    </el-table-column>
                     <el-table-column
                    label="状态"
                    prop="status">
                     <template slot-scope="scope">
                        <el-tag :type="scope.row.status==1?'success':'info'" size="mini">{{scope.row.status==1?'上架中':'已下架'}}</el-tag>
                         <!-- <el-tag type="info">基础服务3</el-tag> -->
                    </template>
                    </el-table-column>
                    <el-table-column
                    width='140'
                    align="center">
                    <template slot="header">
                        <!-- <el-input
                        v-model="searchKey"
                        size="mini"
                        placeholder="输入关键字搜索"/> -->
                        操作
                    </template>
                    <template slot-scope="scope">
                        <el-button
                        size="small"
                        type="text"
                        @click="serviceDetail(scope.$index, scope.row)">详情</el-button>
                        <el-button
                        size="small"
                        type="text"
                        @click="handleDelete(scope.$index, scope.row)">{{scope.row.status==1?'下架':'上架'}}</el-button>
                    </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                background
                :page-size="10"
                layout="prev, pager, next"
                :current-page="currPage"
                @current-change="changePage"
                :page-count="totalPage">
                </el-pagination>
               </el-tab-pane>
                <el-tab-pane label="添加服务包" name="second" class="add-service">
                 <ServiceEdite :ruleForm='ruleFormAddService' @cancelAdd='cancelAdd' v-if="!showUpdate" @updateList="updateList"></ServiceEdite>
                </el-tab-pane>

                <el-tab-pane v-for="(item, index) in editableTabs" :key="index"
                    :closable="item.closable"
                    :label="item.title"
                    :name="item.name" 
                >
                <div class="update-service" v-if="showUpdate">
                 <ServiceEdite :ruleForm='ruleFormUpdateAervice' @showUpdateChange='showUpdateChange' @updateList="updateList"></ServiceEdite>
                </div>
                </el-tab-pane>
         </el-tabs>
         
    </div>
</template>
<script>
import ServiceEdite from '../common/ServiceEdite'
export default {
    name:'ServicePack',
    components:{
        ServiceEdite
    },
    data() {
      
      return {
        currPage:1,
        totalPage:1,
        basicServiceList:[],
        activeName: 'first',
        searchKey: '',
        servicePackList:[],
        editableTabsValue: '',
        editableTabs: [],
        showUpdate:false,
        ruleFormAddService: {
            packageId:'',
            productDesc:'',//服务包名称
            price:'',
            productId:'',
            productName:'',//服务包名称
            productType:'',//服务包类型
            status:'',
            targetCustomer:'',//针对人群
            content:'',//服务包内容
            serviceId:[],//绑定的基础服务
            isAdd:true,
            basicServiceList:[]
        },
        ruleFormUpdateAervice: {},
        updateStatusInfo:{},
      };
    },
    created(){
      this.getProducts(this.currPage)
      this.getBasicServices()
    },
    methods: {
         //获取所有基础服务
      getBasicServices(){
        this.axios.get('services',{
          params:{
            page:1
          }
        }).then(res=>{
          console.log(res)
          this.basicServiceList=res
          this.ruleFormAddService.basicServiceList=this.basicServiceList
        }).catch(err=>{
          console.log(err)
        })
      },
      //点击标签
      handleClick(tab, event) {
        // console.log(tab, event);
        this.editableTabsValue=tab.name
        if(tab.name=='second'){
         this.showUpdate=false
        }
        if(tab.name=='serviceDetail'){
          this.showUpdate=true
        }
      },
       //页码改变
      changePage(val){
          // console.log(val)
          this.currPage=val
          this.getProducts(val)
      },
      //查看服务包详情
       serviceDetail(index, row) {
        console.log(index, row);
        this.getServiceDetail(row.productId)
      },
      //下架按钮
      handleDelete(index, row) {
        console.log(index, row);
        this.updateStatusInfo=row
        this.$confirm(`确定要${this.updateStatusInfo.status==1?'下架':'上架'}该服务包吗？`)
            .then(_ => {
                this.updateStatus()
            })
            .catch(_ => {});
      },
      //修改上架状态
      updateStatus(){
        var _this = this
        this.axios.patch('products/updateStatus',{
          productId:this.updateStatusInfo.productId,
          status:this.updateStatusInfo.status==1?0:1
        }).then(res=>{
          console.log(res)
          this.getProducts(this.currPage)
          this.$message.success(`${_this.updateStatusInfo.status==1?'下架':'上架'}成功！`)
        }).catch(err=>{
          console.log(err)
        })
      },
      //删除标签
       removeTab(targetName) {
        let tabs = this.editableTabs;
        console.log(targetName)
        let activeName = this.editableTabsValue;
        if (activeName === targetName) {
          tabs.forEach((tab, index) => {
            if (tab.name === targetName) {
              let nextTab = tabs[index + 1] || tabs[index - 1];
              if (nextTab) {
                activeName = nextTab.name;
              }
            }
          });
        }
        
        this.editableTabsValue = activeName;
        this.editableTabs = tabs.filter(tab => tab.name !== targetName);
        this.activeName='first'
      },
      //关闭详情页
      showUpdateChange(val){
        // console.log(val)
        this.showUpdate=val
        this.cancelAdd()
      },
      //取消增加
      cancelAdd(val){
        let newTabName = 'first';
        this.editableTabsValue = newTabName;
        this.activeName = newTabName
      },
      //获取服务包列表
      getProducts(page){
        this.axios.get('products',{
           params:{
            page
          }
        }).then(res=>{
          console.log(res)
          this.servicePackList=res.list
          this.totalPage=res.pages
        }).catch(err=>{
          console.log(err)
        })
      },
      //子组件通知更新列表
      updateList(val){
        if(val==1){
          this.getProducts(this.currPage)
        }else{
           let newTabName = 'first';
          this.editableTabsValue = newTabName;
          this.activeName = newTabName
          this.getProducts(1)

        }
      },
      //获取服务包详情
      getServiceDetail(productId){
        this.axios.get(`products/${productId}`,{
          params:{
            productId:productId
          }
        }).then(res=>{
          console.log(res)
          this.ruleFormUpdateAervice={...res.package,...res.product,serviceId:res.serviceId,basicServiceList:this.basicServiceList,isAdd:false}
         let newTabName = 'serviceDetail';
        if(this.editableTabs.length==0){
          this.editableTabs.push({
            title: '服务包详情',
            name: newTabName,
            closable:true,
            content: '服务包详情'
          });
        }
        this.editableTabsValue = newTabName;
        this.activeName = newTabName
        this.showUpdate=true

        }).catch(err=>{
          console.log(err)
        })
      }
     
    }
}
</script>
<style lang="less" scoped>
.servicepack-wrapper{
   .til{
        text-align: left;
        font-size: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 50px;

    }
    .el-table{
      margin-bottom: 15px;
    }
  .service-detail{
    text-align: left;
    .content{
      h4 span{
        font-weight: normal;
      }
    }
  }
  .update-service,.add-service{
    text-align: left;
    // width: 600px;
    /deep/.el-form{
      .el-textarea__inner{
        min-height: 200px!important;
      }
    }
  }
    
}
</style>