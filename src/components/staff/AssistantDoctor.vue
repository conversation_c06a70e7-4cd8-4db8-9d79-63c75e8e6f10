<template>
    <div class="assistantdoctor-wrapper">
        <p class="til">医助管理&emsp;&emsp;<el-button type='primary' size='small' @click="num=1,dialogFormVisible = true,markTitle='新增医助', ruleForm={name: '',phone: '',gender:null}">添加医助</el-button></p>
        <el-divider></el-divider>
        <el-tabs v-model="activeName"  @tab-click="handleClick">
              <el-tab-pane label="医助列表" name="assistantlist" class="assistantdoctor-list">
                  <!-- .filter(data => !keyName || data.name.toLowerCase().includes(keyName.toLowerCase())) -->
                <el-table
                    size="mini"
                    :data="assistantData"
                    style="width: 100%">
                    <el-table-column
                    align="center"
                    width="120"
                    label="ID"
                    prop="assistantId">
                    </el-table-column>
                    <el-table-column
                    align="center"
                    label="医助姓名"
                    prop="name">
                    </el-table-column>
                    <el-table-column
                    align="center"
                    label="加入时间">
                    <template slot-scope="scope">
                        <span v-if="scope.row.createTime">
                            {{timeMode(scope.row.createTime).dateMin}}
                        </span>
                    </template>
                    </el-table-column>
                    <el-table-column
                    align="center"
                    label="电话"
                    prop="phone">
                    </el-table-column>
                    <!-- <el-table-column
                    align="center"
                    label="群聊ID"
                    prop="imAccid">
                    </el-table-column> -->
                    <el-table-column
                    align="right">
                    <template slot="header" slot-scope="scope">
                        <el-input
                        size="mini"
                        v-model="keyName"
                        @change="getAssistants(1)"
                        placeholder="输入关键字搜索"/>
                    </template>
                    <template slot-scope="scope">
                        <!-- <el-button
                        size="medium"
                        type="primary" icon="el-icon-edit" circle
                        @click="handleEdit(scope.$index, scope.row)"></el-button> -->
                        <el-button
                        size="mini"
                        type="primary" icon="el-icon-edit" circle
                        @click="handleEdit(scope.$index, scope.row)"></el-button>
                    </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                background
                :page-size="10"
                layout="prev, pager, next"
                :current-page="currPage"
                @current-change="changePage"
                :page-count="totalPage">
                </el-pagination>
              </el-tab-pane>
        </el-tabs>
        <!-- 医助信息弹框 -->
        <el-dialog :title="markTitle" :visible.sync="dialogFormVisible" center>
            <el-form :model="ruleForm" :rules="rules"  ref="ruleForm">
                <el-form-item label="医助姓名" prop="name" :rules="rules.name" :label-width="formLabelWidth">
                <el-input v-model="ruleForm.name" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="医助性别" prop="gender" :rules="rules.gender" :label-width="formLabelWidth">
                <!-- <el-select v-model="ruleForm.gender" placeholder="请选择性别">
                    <el-option label="男" value="男"></el-option>
                    <el-option label="女" value="女"></el-option>
                </el-select> -->
                 <el-radio-group v-model="ruleForm.gender">
                    <el-radio :label="1">男</el-radio>
                    <el-radio :label="2">女</el-radio>
                </el-radio-group>
                </el-form-item>
                 <el-form-item label="电话" prop="phone" :rules="rules.phone"  :label-width="formLabelWidth">
                    <el-input v-model.number="ruleForm.phone" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="登录密码" prop="phone" :rules="rules.password"  :label-width="formLabelWidth" v-if="markTitle=='新增医助'">
                    <el-input v-model="ruleForm.password" autocomplete="off"></el-input>
                </el-form-item>
                <!-- <el-form-item>
                    <el-button @click="dialogFormVisible = false">取消</el-button>
                    <el-button type="primary" @click="addService('ruleForm')">新增</el-button>
                </el-form-item> -->
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogFormVisible = false">取 消</el-button>
                <el-button type="primary" @click="addService('ruleForm')">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
export default {
    name:'AssistantDoctor',
    data(){
        return{
            currPage:1,
            totalPage:1,
            keyName:'',
            assistantData:[],
            dialogFormVisible: false,
            formLabelWidth: '120px',
             ruleForm: {
                name: '',
                phone: '',
                gender:null,
                password:''
                },
            rules: {
                name: [
                   { required: true, message: '请输入医助姓名', trigger: 'blur' },
                ],
                phone: [
                    { required: true, message: '请输入医助电话', trigger: 'blur' },
                ],
                password: [
                    { required: true, message: '请输入医助电话', trigger: 'blur' },
                ],
                gender:[
                    { required: true, message: '请选择医助性别', trigger: 'blur' },
                ]
            },
             markTitle:'新增医助',
             activeName:'assistantlist',
             num:null,
             curAssistantId:''
        }
    },
    created(){
        this.getAssistants(this.currPage)
    },
    methods:{
        //修改医助信息
        handleEdit(index, row) {
            console.log(index, row);
            this.num=null
            this.curAssistantId=row.assistantId
            this.dialogFormVisible = true
            this.markTitle='修改医助信息'
            this.ruleForm={
                name:row.name,
                phone:row.phone,
                gender:row.gender
            }
        },
        //点击tabs标签
        handleClick(tab, event) {
            console.log(tab); 
        },
        //页码改变
        changePage(val){
            // console.log(val)
            this.currPage=val
            this.getAssistants(val)
        },
        //获取医助列表
        getAssistants(page){
            this.axios.get('assistants',{
                params:{
                    page,
                    key:this.keyName
                }
            }).then(res=>{
                console.log(res)
                this.assistantData=res.list
                this.totalPage=res.pages
            }).catch(err=>{
                console.log(err)
            })
        },
        //医助信息提交
        addService(formName) {
            console.log( this.$refs[formName])
            this.$refs[formName].validate((valid) => {
            console.log(this.ruleForm)
            if (valid) {
                    console.log('submit!');
                    if(this.num==1){
                        //新增
                        this.addAssistant()
                    }else{
                        //修改
                        this.updataAssistant(this.curAssistantId)
                    }
                    this.dialogFormVisible = false
                    this. ruleForm={
                        name: '',
                        phone: '',
                        gender:null,
                        password:''
                    }
            } else {
                console.log('error submit!!');
                return false;
            }
        });
      },
        //修改医助
        updataAssistant(assistantId){
            // console.log({...this.ruleForm,assistantId})
            this.axios({
                method:'patch',
                url:'assistants',
                custom:'noqs',
                headers:{
                    "Content-Type": "application/json;"
                },
                data:{...this.ruleForm,assistantId}
            }).then(res=>{
                console.log(res)
                this.$message.success('修改成功！')
                this.changePage(this.currPage)
            })
        },
        //新增医助
        addAssistant(){
             this.axios({
                method:'post',
                url:'assistants',
                custom:'noqs',
                headers:{
                    "Content-Type": "application/json;"
                },
                data:this.ruleForm
            }).then(res=>{
                console.log(res)
                this.$message.success('新增成功！')
                this.currPage=1
                this.changePage(this.currPage)
            })
        },
        //时间处理
         timeMode(time){
            //  console.log(time)
            let dates = new Date(time)
            let year = String(dates.getFullYear())
            let month =String((dates.getMonth() +1)) < 10 ? '0'+String((dates.getMonth() +1)) : String((dates.getMonth() +1))
            let date = String(dates.getDate()) < 10 ? '0'+String(dates.getDate()) : String(dates.getDate())
            let hour = String(dates.getHours()) < 10 ? '0'+String(dates.getHours()) : String(dates.getHours())
            let minutes = String(dates.getMinutes()) < 10 ? '0'+String(dates.getMinutes()) : String(dates.getMinutes())
            let seconds= String(dates.getSeconds()) < 10 ? '0'+String(dates.getSeconds()) : String(dates.getSeconds())
            let datestr = year + '-' + month +  '-' + date 
            let getStartTime =  year + '-' + month +  '-' + date + ' ' + '00:00'
            let getEndTime =  year + '-' + month +  '-' + date + ' ' + '24:59'
            let dateMin= year + '-' + month +  '-' + date + ' ' + hour + ':' + minutes + ':' + seconds
            return {
                datestr,
                getStartTime,
                getEndTime,
                dateMin
            }
         },
    }
}
</script>
<style lang="less" scoped>
.assistantdoctor-wrapper{
    .til{
        text-align: left;
        font-size: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 50px;
        // margin-bottom: 0;

    }
    /deep/.el-tabs{
        // flex:1;
        //  display: flex;
        // flex-direction: column;
        .el-tabs__content{
            // flex:1;
            .assistantdoctor-list{
                // height: 100%;
                // display: flex;
                // flex-direction: column;
                .el-table{
                    // flex:1;
                    margin-bottom: 15px;
                }
                .el-table::before{
                    height: 0;
                }
            }
        }
    }
    .deletebtn{
        // margin-right: 40px;
        // margin-left:30px;
    }
    
}
</style>