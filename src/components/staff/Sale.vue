<template>
    <div class="sale-wrapper">
        <p class="til">销售管理&emsp;&emsp;<el-button type='primary' size='small' @click="num=1,dialogFormVisible = true,markTitle='新增销售',ruleForm={sellerName: '',sellerPhone: '',gender:null,role:'',quota:''}">添加销售</el-button></p>
        <el-divider></el-divider>
        <el-tabs v-model="activeName"  @tab-click="handleClick">
              <el-tab-pane label="销售列表" name="salelist">
                <el-table
                    size="mini"
                    :data="sellerData"
                    style="width: 100%">
                    <el-table-column
                    align="center"
                    label="ID"
                    width='120'
                    prop="sellerId">
                    </el-table-column>
                    <el-table-column
                    label="销售姓名"
                    align="center"
                    prop="sellerName">
                    </el-table-column>
                    <!-- <el-table-column
                    align="center"
                    label="加入时间"
                    prop="id">
                    </el-table-column> -->
                    <el-table-column
                    align="center"
                    label="电话"
                    prop="sellerPhone">
                    </el-table-column>
                    <el-table-column
                    align="center"
                    label="职位"
                    prop="role">
                    </el-table-column>
                    <el-table-column
                    align="center"
                    label="指标"
                    prop="quota">
                    </el-table-column>
                    <el-table-column
                    align="right">
                    <template slot="header" slot-scope="scope">
                        <el-input
                        v-model="searchKey"
                        size="mini"
                        @change="getSellers(1)"
                        placeholder="输入关键字搜索"/>
                    </template>
                    <template slot-scope="scope">
                        <el-button
                        size="mini"
                        type="primary" icon="el-icon-edit" circle
                        @click="handleEdit(scope.$index, scope.row)"></el-button>
                        <!-- <el-button
                        class="deletebtn"
                        size="small"
                        type="danger" icon="el-icon-delete" circle
                        @click="handleDelete(scope.$index, scope.row)"></el-button> -->
                    </template>
                    </el-table-column>
                </el-table>
                 <el-pagination
                background
                :page-size="10"
                layout="prev, pager, next"
                :current-page="currPage"
                @current-change="changePage"
                :page-count="totalPage">
                </el-pagination>
              </el-tab-pane>
        </el-tabs>
        <!-- 销售信息弹框 -->
        <el-dialog :title="markTitle" :visible.sync="dialogFormVisible" center>
            <el-form :model="ruleForm" :rules="rules"  ref="ruleForm">
                <el-form-item label="销售姓名" prop="sellerName" :rules="rules.sellerName" :label-width="formLabelWidth">
                <el-input v-model="ruleForm.sellerName" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="销售性别" prop="gender" :rules="rules.gender" :label-width="formLabelWidth">
                 <el-radio-group v-model="ruleForm.gender">
                    <el-radio :label="1">男</el-radio>
                    <el-radio :label="2">女</el-radio>
                </el-radio-group>
                </el-form-item>
                <el-form-item label="销售职位" prop="role" :rules="rules.role"  :label-width="formLabelWidth">
                    <el-select v-model="ruleForm.role" placeholder="请选择职位">
                        <el-option label="实习生" value="实习生"></el-option>
                        <el-option label="组长" value="组长"></el-option>
                        <el-option label="经理" value="销售经理"></el-option>
                        <el-option label="健康顾问" value="健康顾问"></el-option>
                    </el-select>
                </el-form-item>
                 <el-form-item label="电话" prop="sellerPhone" :rules="rules.sellerPhone"  :label-width="formLabelWidth">
                    <el-input v-model.number="ruleForm.sellerPhone" autocomplete="off"></el-input>
                </el-form-item>
                <!-- <el-form-item label="销售职位" prop="role" :rules="rules.role" :label-width="formLabelWidth">
                <el-input v-model="ruleForm.role" autocomplete="off"></el-input>
                </el-form-item> -->
                <el-form-item label="销售指标" prop="quota" :rules="rules.quota" :label-width="formLabelWidth">
                <el-input v-model="ruleForm.quota" autocomplete="off"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogFormVisible = false">取 消</el-button>
                <el-button type="primary" @click="addService('ruleForm')">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
export default {
    name:'Sale',
    data(){
        return{
            currPage:1,
            totalPage:1,
            searchKey:'',
            sellerData:[],
            dialogFormVisible: false,
            formLabelWidth: '120px',
             ruleForm: {
                sellerName: '',
                sellerPhone: '',
                gender:null,
                role:'',
                quota:''
            },
            rules: {
                sellerName: [
                   { required: true, message: '请输入销售姓名', trigger: 'blur' },
                ],
                sellerPhone: [
                    { required: true, message: '请输入销售电话', trigger: 'blur' },
                ],
                gender:[
                    { required: true, message: '请选择销售性别', trigger: 'blur' },
                ],
                 role: [
                   { required: true, message: '请输入销售职位', trigger: 'blur' },
                ],
                 quota: [
                   { required: true, message: '请输入指标', trigger: 'blur' },
                ],
            },
             markTitle:'新增销售',
             activeName:'salelist',
             num:null,
             curSellerId:''
        }
    },
    created(){
        this.getSellers(this.currPage)
    },
    methods:{
        handleEdit(index, row) {
            console.log(index, row);
            this.dialogFormVisible = true
            this.curSellerId=row.sellerId
            this.num=null
            this.markTitle='修改销售信息'
            this.ruleForm={
                sellerName:row.sellerName,
                sellerPhone:row.sellerPhone,
                gender:row.gender,
                role:row.role,
                quota:row.quota
            }
        },
        handleDelete(index, row) {
            console.log(index, row);
            this.$confirm('确认删除？')
            .then(_ => {
                done();
            })
            .catch(_ => {});
        },
        handleClose(done) {
      },
       //页码改变
        changePage(val){
            // console.log(val)
            this.currPage=val
            this.getSellers(val)
        },
        //点击tabs标签
        handleClick(tab, event) {
            console.log(tab);   
        },
        //获取销售列表
        getSellers(page){
            this.axios.get('sellers',{
                params:{
                    page,
                    key:this.searchKey
                }
            }).then(res=>{
                console.log(res)
                this.sellerData=res.list
                this.totalPage=res.pages

            })
        },
    // 提交销售信息
     addService(formName) {
        console.log( this.$refs[formName])
        this.$refs[formName].validate((valid) => {
          console.log(this.ruleForm)
          if (valid) {
                console.log('submit!');
                 if(this.num==1){
                        //新增
                        this.addSeller()
                    }else{
                        //修改
                        this.updataSeller(this.curSellerId)
                    }
                this.dialogFormVisible = false
                this.ruleForm={
                    sellerName: '',
                    sellerPhone: '',
                    gender:null,
                    role:'',
                    quota:''
                }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      //新增销售
      addSeller(){
          this.axios({
              method:'post',
              custom:'noqs',
              headers:{
                  "Content-Type":"application/json"
              },
              url:'sellers',
              data:this.ruleForm
          }).then(res=>{
            //   console.log(res)
             this.$message.success('添加成功！')
             this.currPage=1
            this.changePage(this.currPage)
          })
      },
      //修改销售
      updataSeller(sellerId){
          this.axios({
              method:'patch',
              custom:'noqs',
                headers:{
                    "Content-Type": "application/json;"
                },
              url:'sellers',
              data:{...this.ruleForm,sellerId}
          }).then(res=>{
            //   console.log(res)
              this.$message.success('修改成功！')
                this.changePage(this.currPage)
          })
      },
    }
}
</script>
<style lang="less" scoped>
.sale-wrapper{
    .til{
        text-align: left;
        font-size: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 50px;

    }
    /deep/.el-table{
        margin-bottom: 15px;
        .el-table__row{
            &:nth-of-type(2n){
                // background:#eee;
            }
        }
    }
    .deletebtn{
        // margin-right: 40px;
        // margin-left:30px;
    }
    
}
</style>