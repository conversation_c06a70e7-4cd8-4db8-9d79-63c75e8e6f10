<template>
    <div class="two-wrapper">
        <p class="til">2400会员管理&emsp;&emsp;</p>
        <el-divider></el-divider>
         <el-tabs v-model="activeName"  @tab-click="handleClick" @tab-remove="removeTab">
              <el-tab-pane label="2400会员列表" name="twolist">
                <el-table
                    size="mini"
                    :data="twoVipData"
                    style="width: 100%">
                    <el-table-column
                    align="center"
                    width="80"
                    label="ID"
                    prop="user_id">
                    </el-table-column>
                    <el-table-column
                    width="100"
                    align="center"
                    label="会员姓名"
                    prop="name">
                    </el-table-column>
                    <el-table-column
                    align="center"
                    label="血压计编号"
                    prop="so_no">
                    </el-table-column>
                    <el-table-column
                    align="center"
                    label="医助"
                    prop="assistant_name">
                    </el-table-column>
                    <el-table-column
                    align="center"
                    label="电话"
                    prop="phone_no">
                    </el-table-column>
                    <el-table-column
                    align="center"
                    label="加入时间">
                     <template slot-scope="scope">
                        <span v-if="scope.row.create_time">
                            {{timeMode(scope.row.create_time).dateMin}}
                        </span>
                    </template>
                    </el-table-column>
                    <el-table-column
                    align="center"
                    label="到期时间">
                     <template slot-scope="scope">
                        <span v-if="scope.row.invalid_time">
                            {{timeMode(scope.row.invalid_time).dateMin}}
                        </span>
                    </template>
                    </el-table-column>
                    <el-table-column
                    width="260"
                    align="right">
                    <template slot="header" slot-scope="scope">
                        <el-input
                        v-model="searchKey"
                        size="mini"
                        @change="getTwoVip(1)"
                        placeholder="输入关键字搜索"/>
                    </template>
                    <template slot-scope="scope">
                        <el-button
                        size="small"
                        type="text"
                        @click="handleEdit(scope.$index, scope.row)">详情</el-button>
                        <el-button
                        size="small"
                        type="text"
                        :disabled="scope.row.so_no?true:false"
                        @click="boundBlood(scope.$index, scope.row)">绑定血压计</el-button>
                        <el-button
                        size="small"
                        type="text"
                        @click="boundAssistant(scope.$index, scope.row)">绑定医助</el-button>
                        <el-button
                        class="deletebtn"
                        type="text"
                        size="small"
                        @click="handleDelete(scope.$index, scope.row)">手动到期</el-button>
                    </template>
                    </el-table-column>
                </el-table>
                 <el-pagination
                background
                :page-size="10"
                layout="prev, pager, next"
                :current-page="currPage"
                @current-change="changePage"
                :page-count="totalPage">
                </el-pagination>
              </el-tab-pane>
              <el-tab-pane v-for="(item, index) in editableTabs" :key="index"
                    :closable="item.closable"
                    :label="item.title"
                    :name="item.name" 
                >
                <PatientDetail :patientId='patientId'  @backList="backList"></PatientDetail>
              </el-tab-pane>
         </el-tabs>
         <!-- 绑定医助弹框 -->
         <BoundAssistant :patientid="patientid" :assistantid="assistantid" :showMark="showBoundAssistant" @closeAssistantMark='showBoundAssistant=false' @updateList="getTwoVip(currPage)"></BoundAssistant>
         <!-- 绑定血压计编号弹框 --> 
         <BoundBlood :patientid="patientid" :sono="sono" :showMark="showBoundBlood" @closeBloodMark="showBoundBlood=false" @updateList="getTwoVip(currPage)"></BoundBlood>
        <!-- 手动到期弹框 -->
    </div>
</template>
<script>
import PatientDetail from './PatientDetail'
import BoundAssistant from '../common/BoundAssistant'
import BoundBlood from '../common/BoundBlood'
export default {
    name:'TwoVip',
    components:{
        PatientDetail,
        BoundAssistant,
        BoundBlood
    },
    data(){
        return{
             currPage:1,
            totalPage:1,
            searchKey:'',
            twoVipData:[],
            activeName:'twolist',
            editableTabsValue:'',
            editableTabs:[],
            patientId:{},
            patientid:'',
            assistantid:'',
            showBoundAssistant:false,
            showBoundBlood:false,
            sono:''

        }
    },
    created(){
        this.getTwoVip(this.currPage)
    },
    methods:{
        //获取2400会员列表
        getTwoVip(page){
            this.axios.get('users/findAll/UserAndProductType',{
                params:{
                    page,
                    productType:1,
                    key:this.searchKey
                }
            }).then(res=>{
                console.log(res)
                this.totalPage=res.pages
                this.twoVipData=res.list

            })
        },
        //点击查看医生详情
         handleEdit(index, row) {
            console.log(index, row);
            console.log(row)
            this.patientId={
                id:row.user_id,
                type:1
            }
            // this.$router.push('/doctordetail')
            let newTabName = 'twodetail';
            if(this.editableTabs.length==0){
                this.editableTabs.push({
                    title: '会员详情',
                    name: newTabName,
                    closable:true,
                    content: '会员详情'
                });
            }
            this.editableTabsValue = newTabName;
            this.activeName = newTabName
            
        },
        //点击绑定医助
        boundAssistant(index, row) {
            console.log(index, row);
            this.patientid=row.user_id
            this.assistantid=row.assistant_id
            this.showBoundAssistant = true
        },
        //点击绑定血压计
        boundBlood(index, row){
            this.patientid=row.user_id
            this.sono=row.so_no
            this.showBoundBlood = true
        },
        //点击手动到期
        handleDelete(index, row) {
            console.log(index, row);
            this.patientid=row.user_id
             this.$confirm(`确定要结束该会员目前享有的会员服务吗？`)
            .then(_ => {
                this.updateStatus(this.patientid)
            })
            .catch(_ => {});

        },
        //结束会员
        updateStatus(userId){
            this.axios.post('users/manuallyCancel',{
                userId
            }).then(res=>{
                this.$message.success('结束该患者的会员服务成功！')
                //刷新列表
                this.getTwoVip(this.currPage)
            })
        },
         //点击标签
        handleClick(tab, event) {
            console.log(tab);    
            if(tab.name=='twodetail'){
                // this.emptyInfo()
            }
        },
        removeTab(targetName) {
            let tabs = this.editableTabs;
            console.log(targetName)
            let activeName = this.editableTabsValue;
            if (activeName === targetName) {
                tabs.forEach((tab, index) => {
                    if (tab.name === targetName) {
                    let nextTab = tabs[index + 1] || tabs[index - 1];
                    if (nextTab) {
                        activeName = nextTab.name;
                    }
                    }
                });
            }
            this.editableTabsValue = activeName;
            this.editableTabs = tabs.filter(tab => tab.name !== targetName);
            this.activeName='twolist'
        },
          //页码改变
        changePage(val){
            // console.log(val)
            this.currPage=val
            this.getTwoVip(val)
        },
         //返回列表
        backList(val){
            // console.log(val)
            this.removeTab(this.editableTabsValue)
        },
        //this吉安处理
         timeMode(time){
            //  console.log(time)
            let dates = new Date(time)
            let year = String(dates.getFullYear())
            let month =String((dates.getMonth() +1)) < 10 ? '0'+String((dates.getMonth() +1)) : String((dates.getMonth() +1))
            let date = String(dates.getDate()) < 10 ? '0'+String(dates.getDate()) : String(dates.getDate())
            let hour = String(dates.getHours()) < 10 ? '0'+String(dates.getHours()) : String(dates.getHours())
            let minutes = String(dates.getMinutes()) < 10 ? '0'+String(dates.getMinutes()) : String(dates.getMinutes())
            let seconds= String(dates.getSeconds()) < 10 ? '0'+String(dates.getSeconds()) : String(dates.getSeconds())
            let datestr = year + '-' + month +  '-' + date 
            let getStartTime =  year + '-' + month +  '-' + date + ' ' + '00:00'
            let getEndTime =  year + '-' + month +  '-' + date + ' ' + '24:59'
            let dateMin= year + '-' + month +  '-' + date + ' ' + hour + ':' + minutes + ':' + seconds
            return {
                datestr,
                getStartTime,
                getEndTime,
                dateMin
            }
         },
    }
}
</script>
<style lang="less" scoped>
.two-wrapper{
    .til{
        text-align: left;
        font-size: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 50px;
    }
     .el-table{
        margin-bottom: 15px;
    }
}
</style>