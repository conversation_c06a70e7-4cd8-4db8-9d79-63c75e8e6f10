<template>
    <div class="allpatient-wrapper">
        <p class="til">非会员管理&emsp;&emsp;</p>
        <el-divider></el-divider>
         <el-tabs v-model="activeName"  @tab-click="handleClick" @tab-remove="removeTab">
              <el-tab-pane label="患者列表" name="noviplist">
                <el-table
                    size="mini"
                    :data="noVipData"
                    style="width: 100%">
                    <el-table-column
                    align="center"
                    width="80"
                    label="ID"
                    prop="user_id">
                    </el-table-column>
                    <el-table-column
                    width="100"
                    align="center"
                    label="患者姓名"
                    prop="name">
                    </el-table-column>
                    <!-- <el-table-column
                    align="center"
                    label="血压计"
                    prop="user_id">
                    </el-table-column>
                    <el-table-column
                    align="center"
                    label="医助"
                    prop="user_id">
                    </el-table-column> -->
                    <!-- <el-table-column
                    align="center"
                    label="加入时间"
                    prop="id">
                    </el-table-column> -->
                    <el-table-column
                    align="center"
                    label="电话"
                    prop="phone_no">
                    </el-table-column>
                    <el-table-column
                    align="center"
                    label="加入时间">
                     <template slot-scope="scope">
                        <span v-if="scope.row.create_time">
                            {{timeMode(scope.row.create_time).dateMin}}
                        </span>
                    </template>
                    </el-table-column>
                    <el-table-column
                    width="250"
                    align="right">
                    <template slot="header" slot-scope="scope">
                        <el-input
                        v-model="searchKey"
                        size="mini"
                        @change="getNoVip(1)"
                        placeholder="输入关键字搜索"/>
                    </template>
                    <template slot-scope="scope">
                        <el-button
                        size="small"
                        type="text"
                        @click="handleEdit(scope.$index, scope.row)">详情</el-button>
                        <el-button
                        class="deletebtn"
                        type="text"
                        size="small"
                        @click="buy(scope.$index, scope.row)">购买服务包</el-button>
                    </template>
                    </el-table-column>
                </el-table>
                 <el-pagination
                background
                :page-size="10"
                layout="prev, pager, next"
                :current-page="currPage"
                @current-change="changePage"
                :page-count="totalPage">
                </el-pagination>
              </el-tab-pane>
              <el-tab-pane v-for="(item, index) in editableTabs" :key="index"
                    :closable="item.closable"
                    :label="item.title"
                    :name="item.name" 
                >
                <PatientDetail :patientId='patientId' @backList="backList"></PatientDetail>
              </el-tab-pane>
         </el-tabs>
         <!-- 购买服务包弹框 -->
         <BuyProduct :showMark='showBuyMark' :patientid='patientid' @closeBuyMark="showBuyMark=false" @updateList='getNoVip(currPage)'></BuyProduct>
    </div>
</template>
<script>
import PatientDetail from './PatientDetail'
import BuyProduct from '../common/BuyProduct'

export default {
    name:'ThreeVip',
    components:{
        PatientDetail,
        BuyProduct
    },
    data(){
        return{
             currPage:1,
            totalPage:1,
            searchKey:'',
            noVipData:[],
            activeName:'noviplist',
            editableTabsValue:'',
            editableTabs:[],
            patientId:{},
            patientid:'',
            showBuyMark:false

        }
    },
    created(){
        this.getNoVip(this.currPage)
    },
    methods:{
        //获取非会员
          getNoVip(page){
            this.axios.get('users/findAll/UserAndProductType',{
                params:{
                    page,
                    productType:0,
                    key:this.searchKey
                }
            }).then(res=>{
                console.log(res)
                this.noVipData=res.list
                this.totalPage=res.pages

            })
        },
        //点击查看医生详情
         handleEdit(index, row) {
            console.log(index, row);
            console.log(row)
            this.patientId={
                id:row.user_id,
                type:0
            }
            // this.$router.push('/doctordetail')
            let newTabName = 'patientdetail';
            if(this.editableTabs.length==0){
                this.editableTabs.push({
                    title: '患者详情',
                    name: newTabName,
                    closable:true,
                    content: '患者详情'
                });
            }
            this.editableTabsValue = newTabName;
            this.activeName = newTabName
            
        },
        //点击删除图标
        buy(index, row) {
            console.log(index, row);
            this.patientid=row.user_id
            this.showBuyMark=true
        },
         //点击标签
        handleClick(tab, event) {
            console.log(tab);    
            if(tab.name=='patientdetail'){
                // this.emptyInfo()
            }
        },
        removeTab(targetName) {
            let tabs = this.editableTabs;
            console.log(targetName)
            let activeName = this.editableTabsValue;
            if (activeName === targetName) {
                tabs.forEach((tab, index) => {
                    if (tab.name === targetName) {
                    let nextTab = tabs[index + 1] || tabs[index - 1];
                    if (nextTab) {
                        activeName = nextTab.name;
                    }
                    }
                });
            }
            this.editableTabsValue = activeName;
            this.editableTabs = tabs.filter(tab => tab.name !== targetName);
            this.activeName='noviplist'
        },
          //页码改变
        changePage(val){
            // console.log(val)
            this.currPage=val
            this.getNoVip(val)
        },
         //返回列表
        backList(val){
            // console.log(val)
            this.removeTab(this.editableTabsValue)
        },
        //this吉安处理
         timeMode(time){
            //  console.log(time)
            let dates = new Date(time)
            let year = String(dates.getFullYear())
            let month =String((dates.getMonth() +1)) < 10 ? '0'+String((dates.getMonth() +1)) : String((dates.getMonth() +1))
            let date = String(dates.getDate()) < 10 ? '0'+String(dates.getDate()) : String(dates.getDate())
            let hour = String(dates.getHours()) < 10 ? '0'+String(dates.getHours()) : String(dates.getHours())
            let minutes = String(dates.getMinutes()) < 10 ? '0'+String(dates.getMinutes()) : String(dates.getMinutes())
            let seconds= String(dates.getSeconds()) < 10 ? '0'+String(dates.getSeconds()) : String(dates.getSeconds())
            let datestr = year + '-' + month +  '-' + date 
            let getStartTime =  year + '-' + month +  '-' + date + ' ' + '00:00'
            let getEndTime =  year + '-' + month +  '-' + date + ' ' + '24:59'
            let dateMin= year + '-' + month +  '-' + date + ' ' + hour + ':' + minutes + ':' + seconds
            return {
                datestr,
                getStartTime,
                getEndTime,
                dateMin
            }
         },
    }
}
</script>
<style lang="less" scoped>
.allpatient-wrapper{
    .til{
        text-align: left;
        font-size: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 50px;
    }
    .el-table{
        margin-bottom: 15px;
    }
}
</style>