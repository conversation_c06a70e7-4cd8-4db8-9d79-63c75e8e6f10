<template>
    <div class="doctordetail-wrapper">
        <!-- <el-page-header @back="goBack" content="详情页面">
        </el-page-header> -->
        <div class="detail-form">
           <el-form :model="doctorInfo" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
               <div class="detail-from-item">
                <!-- <div> -->
                    <el-form-item label="头像">
                        <div class="block"><el-avatar shape="square" :size="100" :src="doctorInfo.avatar"></el-avatar>
                        <el-button type="text" size="mini" @click="uploadPic">
                            &emsp;{{doctorInfo.avatar?'更换头像':'上传头像'}}</el-button>
                            <input type="file" ref="evfile" @change="zh_uploadFile_change" style="display:none">
                            </div>

                    </el-form-item>
                <!-- </div>
                <div> -->
                    <el-form-item label="姓名" prop="name">
                        <el-input v-model="doctorInfo.name"></el-input>
                    </el-form-item>
                     <el-form-item label="医生ID" v-if="doctorId!=null">
                        <el-input v-model="doctorInfo.doctorId" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="工作组" prop="groupId">
                        <el-select v-model="doctorInfo.groupId" :disabled='doctorId!=null' filterable placeholder="请选择工作组">
                             <el-option v-for="(item,index) in allGroup" :key="index"
                             :label="item.group_name" :value="item.group_id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="医院" prop="hospitalId">
                        <el-select v-model="doctorInfo.hospitalId" filterable placeholder="请选择医院">
                            <el-option v-for="(item,index) in allHospital" :key="index"
                             :label="item.name" :value="item.hospitalId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="科室" prop="departmentId">
                        <el-select v-model="doctorInfo.departmentId" filterable placeholder="请选择科室">
                            <el-option v-for="(item,index) in allDepartment" :key="index"
                             :label="item.name" :value="item.departmentId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="职位" prop="position">
                        <el-input v-model="doctorInfo.position"></el-input>
                    </el-form-item>
                <!-- </div>
                <div> -->
                    <el-form-item label="身份证号" prop="cardNo">
                        <el-input v-model="doctorInfo.cardNo"></el-input>
                    </el-form-item>
                    <el-form-item label="电话" prop="phone">
                        <el-input v-model.number="doctorInfo.phone"></el-input>
                    </el-form-item>

                <!-- </div>
                <div> -->

                <!-- </div> -->

               </div>
                <el-form-item label="简介" prop="remarks">
                    <el-input type="textarea" v-model="doctorInfo.remarks"></el-input>
                </el-form-item>
                <el-form-item label="专业擅长" prop="speciality">
                    <el-input type="textarea" v-model="doctorInfo.speciality"></el-input>
                </el-form-item>
            <el-form-item v-if="doctorId!=null">
                <el-button @click="closeUpdate">返回</el-button>
                <el-button type="primary" @click="updateService('ruleForm',1)">修改</el-button>
            </el-form-item>
             <el-form-item v-else>
                <el-button @click="closeAdd">取消</el-button>
                <el-button type="primary" @click="updateService('ruleForm',null)">新增</el-button>
            </el-form-item>
            </el-form>
        </div>
    </div>
</template>
<script>
//七牛上传插件
import * as qiniu from 'qiniu-js';
export default {
    name:'DoctorDetail',
    props:{
        doctorId:{required:true}
    },
    data(){
        return{
            allGroup:[],
            rules: {
                name: [
                    { required: true, message: '请输入医生姓名', trigger: 'blur' },
                ],
                phone:[
                    { required: true, message: '请输入医生电话', trigger: 'blur' },
                ],
                cardNo: [
                    { required: true, message: '请输入医生身份证号', trigger: 'blur' }
                ],
                 groupId:[
                     { required: true, message: '请选择工作组', trigger: 'blur' }
                ],
                  hospitalId:[
                     { required: true, message: '请选择医院', trigger: 'blur' }
                ],
                  departmentId:[
                     { required: true, message: '请选择科室', trigger: 'blur' }
                ],
                position:[
                     { required: true, message: '请输入职称', trigger: 'blur' }
                ],
                speciality:[
                     { required: true, message: '请输入专业擅长', trigger: 'blur' }
                ],
                  remarks:[
                     { required: true, message: '请输入个人简介', trigger: 'blur' }
                ],
                // type: [
                //     { type: 'array', required: true, message: '请至少选择一个基础服务绑定', trigger: 'change' }
                // ],
            },
            allDepartment:[],
            allHospital:[],
            doctorInfo:{},
            emptyDoctorInfo:{
                avatar:'',
                cardNo: "",
                groupId:null,
                departmentId:null,
                hospitalId: null,
                name: "",
                phone: null,
                position: "",
                remarks: "",
                speciality: ""
            },
            qiNiuToken:''
        }
    },
    watch:{
        doctorId:function(val){
            console.log(val)
            if(val!==null){
                this.getDoctorInfo(val)
            }else{
                this.doctorInfo=this.emptyDoctorInfo//51303019980701271x
            }
        },
    },
    created(){
        this.getAllDepartment()
        this.getUploadToken()
        this.getAllGroup()
        console.log(this.doctorId)
        if(this.doctorId!==null){
            this.getDoctorInfo(this.doctorId)
        }else{
            this.doctorInfo=this.emptyDoctorInfo
        }
    },
    methods:{
        updateService(formName,num) {
            // console.log( this.$refs[formName])
            this.$refs[formName].validate((valid) => {
                // console.log(this.doctorInfo)
            if (valid) {
                console.log('submit!');
            if(num==1){
                //修改
                this.updateDoctorInfo()
            }else{
                //新增
                this.addDoctorInfo()
            }
            } else {
                console.log('error submit!!');
                return false;
            }
            });
        },
         //获取当前医生信息
        getDoctorInfo(id){
            this.axios.get(`doctors/${id}`,{
                params:{}
            }).then(res=>{
                console.log(res)
                this.doctorInfo={
                    doctorId:res.doctor_id,
                    groupId:res.group_id,
                    phone:res.phone,
                    name:res.name,
                    cardNo:res.card_no,
                    departmentId:res.department_id,
                    hospitalId:res.hospital_id,
                    remarks:res.remarks,
                    speciality:res.speciality,
                    avatar:res.avatar,
                    position:res.position,
                }
            })
        },
        //添加医生
        addDoctorInfo(){
            if(this.doctorInfo.avatar){
                this.axios({
                   method:'post',
                   custom:'noqs',
                   url:'doctors',
                   headers:{
                       "Content-Type":"application/json;"
                   },
                   data:this.doctorInfo
                //    {
                //        phone:this.doctorInfo.phone,
                //        name:this.doctorInfo.name,
                //        cardNo:this.doctorInfo.cardNo,
                //        departmentId:this.doctorInfo.departmentId,
                //        hospitalId:this.doctorInfo.hospitalId,
                //        remarks:this.doctorInfo.remarks,
                //        speciality:this.doctorInfo.speciality,
                //        avatar:this.doctorInfo.avatar,
                //        position:this.doctorInfo.position,
                //        groupId:this.doctorInfo.groupId
                //    }
                }).then(res=>{
                   console.log(res)
                   this.$message.success('添加成功！')
                   this.doctorInfo=this.emptyDoctorInfo
                   this.$refs['ruleForm'].resetFields();
                   this.$emit('updateList',2)
               }).catch(err=>{
                   console.log(err)
               })
            }else{
                this.$message.error('请上传头像')
            }
        },
        //修改医生基本信息
        updateDoctorInfo(){
            this.axios({
                method:'patch',
                custom:'noqs',
                url:'doctors',
                headers:{
                    "Content-Type":"application/json;"
                },
                data:this.doctorInfo
            }).then(res=>{
                console.log(res)
                this.$message.success('修改成功！')
                this.$emit('updateList',1)
            })
        },
        //返回列表
        closeUpdate(){
            this.$emit('backList',1)
        },
        //取消新增
        closeAdd(){
             this.$refs['ruleForm'].resetFields();
            this.doctorInfo=this.emptyDoctorInfo
            this.$emit('backList',2)
        },
        //获取所有科室和医院
        getAllDepartment(){
            this.axios.get('doctorGroups/getDepartment',{
                params:{}
            }).then(res=>{
                console.log(res)
                this.allDepartment=res

            })
            this.axios.get('doctorGroups/getHospital',{
                params:{}
            }).then(res=>{
                console.log(res)
                this.allHospital=res
            })
        },
        //获取所有工作室
        getAllGroup(){
            this.axios.get('doctorGroups/getAll/doctorGroupNameAndIds',{
                params:{}
            }).then(res=>{
                console.log(res)
                this.allGroup = res
            })
        },
        //获取七牛云凭证
        getUploadToken(){
            this.axios.get('admins/getUploadToken',{
                params:{}
            }).then(res=>{
                console.log(res)
                this.qiNiuToken=res
            })
        },
        //选择上传文件
        uploadPic(){
            console.log(this.$refs.evfile)
            this.$refs.evfile.click();
        },
        //头像上传
        zh_uploadFile_change(evfile){
          console.log(evfile.target.files[0])
            var loading
             var uptoken = this.qiNiuToken
            var file = evfile.target.files[0] //Blob 对象，上传的文件
          console.log(file,'打印file')
            var key = new Date().getTime()+file.name  // 上传后文件资源名以设置的 key 为主，如果 key 为 null 或者 undefined，则文件资源名会以 hash 值作为资源名。
            let config = {
            useCdnDomain: true,   //表示是否使用 cdn 加速域名，为布尔值，true 表示使用，默认为 false。
            region: qiniu.region.z2     // 根据具体提示修改上传地区,当为 null 或 undefined 时，自动分析上传域名区域
            };
            let putExtra = {
            fname: "",  //文件原文件名
            params: {}, //用来放置自定义变量
            mimeType: null  //用来限制上传文件类型，为 null 时表示不对文件类型限制；限制类型放到数组里： ["image/png", "image/jpeg", "image/gif"]
            };
            var observable = qiniu.upload(file, key, uptoken, putExtra, config);
            observable.subscribe({
            next: (result) => {
            // 主要用来展示进度
                // console.log(result)
                  loading= this.$loading({
                    lock: true,
                    text: '上传中',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });
            },
            error: (errResult) => {
            // 失败报错信息
                loading.close()
                    this.$message.error('上传头像图片失败，请重新上传')
                console.log(errResult)
            },
            complete: (result) => {
            // 接收成功后返回的信息
                loading.close()
                console.log(result)
                this.doctorInfo.avatar='http://image.scheartmed.com/'+result.key
              console.log(this.doctorInfo.avatar,'==================头像地址')
            }
        })
        }
    }
}
</script>
<style lang="less" scoped>
    .doctordetail-wrapper{
        .detail-form{
            // width: 70%;
            margin-left: 50px;
            margin-top:30px;

            /deep/.el-form{
                .detail-from-item{
                    display:flex;
                    flex-wrap: wrap;
                    align-items: center;
                }
                // .el-form-item{
                //     margin-bottom: 30px;
                //     text-align: left;
                //     .el-textarea__inner{
                //         min-height: 100px!important;
                //     }
                // }
                 .el-form-item{
                     min-width: 446px;
                    // width: 360px;
                    margin-right: 20px;
                    text-align: left;
                    margin-bottom: 30px;
                    .el-form-item__content{
                        .el-input__inner{
                            width: 290px;
                        }
                    }
                    .el-textarea__inner{
                        min-height: 100px!important;
                    }
                    .el-checkbox-group{
                        display: flex;
                        align-items: center;
                    }
                }
            }
        }

    }
</style>
