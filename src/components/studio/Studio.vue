<template>
    <div class="studio-wrapper">
        <p class="til">工作室管理&emsp;&emsp;<el-button type='primary' size='small' @click="activeName='addstudio',editableTabsValue='addstudio',emptyInfo()">添加工作室</el-button></p>
        <el-divider></el-divider>
        <el-tabs v-model="activeName"  @tab-click="handleClick" @tab-remove="removeTab">
             <el-tab-pane label="工作室列表" name="studiolist">
                <el-table
                    size="mini"
                    :data="doctorGroupsData"
                    style="width: 100%">
                    <el-table-column
                    align="center"
                    width="100"
                    label="工作室ID"
                    prop="group_id">
                    </el-table-column>
                    <el-table-column
                    align="center"
                    label="工作室名称"
                    prop="group_name">
                    </el-table-column>
                    <el-table-column
                    align="center"
                    label="区域"
                    prop="region_name">
                    </el-table-column>
                    <el-table-column
                    align="center"
                    label="医院"
                    prop="hospital_name">
                    </el-table-column>
                    <el-table-column
                    align="center"
                    label="科室"
                    prop="department_name">
                    </el-table-column>
                    <!-- <el-table-column
                    align="center"
                    label="电话"
                    prop="phone">
                    </el-table-column> -->
                     <el-table-column
                    align="center"
                    label="医助"
                    prop="assistant_name">
                    </el-table-column>
                     <el-table-column
                        align="center"
                        label="状态"
                        width='100'
                        prop="status">
                         <template slot-scope="scope">
                            <el-tag :type="scope.row.status==1?'success':'info'" size="mini">{{scope.row.status==1?'显示':'不显示'}}</el-tag>
                        </template>
                        </el-table-column>
                    <el-table-column
                    align="right">
                    <template slot="header" slot-scope="scope">
                        <el-input
                        v-model="searchKey"
                        size="mini"
                        @change="getDoctorGroups(1)"
                        placeholder="输入关键字搜索"/>
                    </template>
                    <template slot-scope="scope">
                         <el-button
                        size="small"
                        type="text"
                        @click="handleDelete(scope.$index, scope.row)">标记为{{scope.row.status==1?'不显示':'显示'}}</el-button>
                        <el-button
                        size="mini"
                        type="primary" icon="el-icon-edit" circle
                        @click="handleEdit(scope.$index, scope.row)"></el-button>
                    </template>
                    </el-table-column>

                </el-table>
                   <el-pagination
                    background
                    :page-size="10"
                    layout="prev, pager, next"
                    :current-page="currPage"
                    @current-change="changePage"
                    :page-count="totalPage">
                    </el-pagination>
             </el-tab-pane>
            <el-tab-pane label="添加工作室" name="addstudio" class="add-service">
                <StudioDetail :studioInfo="studioInfo" :doctorGroupsId='null' @backList="backList" @updateList="updateList"></StudioDetail>
            </el-tab-pane>
              <el-tab-pane v-for="(item, index) in editableTabs" :key="index"
                    :closable="item.closable"
                    :label="item.title"
                    :name="item.name" 
                >
                 <StudioDetail :studioInfo="studioInfo" :doctorGroupsId='doctorGroupsId'  @backList="backList" @updateList="updateList"></StudioDetail>
              </el-tab-pane>
        </el-tabs>
    </div>
</template>
<script>
import StudioDetail from './StudioDetail'
export default {
    name:'Doctor',
    components:{
        StudioDetail
    },
    data(){
        return{
            currPage:1,
            totalPage:1,
            searchKey:'',
            doctorGroupsData:[],
            editableTabsValue: '',
            editableTabs: [],
            activeName: 'studiolist',
            studioInfo:{
                id:'',
                name:'',
                // phone:'',
                asstantDoctor:'',
                card:'',
                hospital:'',
                department:'',
                position:'',
                introduction:'',
                speciality:'',
                avator:'',
                region:'',
                date:'',
                isAdd:true,
                type:[],
                doctor:[]
            },
            doctorGroupsId:'',
            updateStatusInfo:{}
        }
    },
    created(){
        this.getDoctorGroups(this.currPage)
    },
    methods:{
        //点击查看工作室详情
         handleEdit(index, row) {
            console.log(index, row);
            console.log(row)
            let newTabName = 'studiodetail';
            if(this.editableTabs.length==0){
                this.editableTabs.push({
                    title: '工作室详情',
                    name: newTabName,
                    closable:true,
                    content: '工作室详情'
                });
            }
            this.editableTabsValue = newTabName;
            this.activeName = newTabName
            this.doctorGroupsId=row.group_id
            this.studioInfo={
                id:'',
                name:'',
                // phone:'',
                asstantDoctor:'',
                card:'',
                hospital:'',
                department:'',
                position:'',
                introduction:'',
                speciality:'',
                avator:'',
                region:'',
                date:'',
                isAdd:false,
                type:[],
                doctor:[]
            }
            
        },
         //更改状态
        handleDelete(index, row) {
            console.log(index, row);
            this.updateStatusInfo=row
            this.$confirm(`确定要将状态改为${this.updateStatusInfo.status==1?'不显示':'显示'}吗？`)
                .then(_ => {
                    this.updateStatus()
                })
                .catch(_ => {});
        },
         //修改工作室状态
        updateStatus(){
            this.axios.patch('doctorGroups/updateStatus',{
            groupId:this.updateStatusInfo.group_id,
            status:this.updateStatusInfo.status==1?0:1
            }).then(res=>{
                this.getDoctorGroups(this.currPage)
                this.$message.success(`已更改为${this.updateStatusInfo.status==1?'不显示':'显示'}！`)
            })
        },
        //点击标签
        handleClick(tab, event) {
            console.log(tab);    
            if(tab.name=='addstudio'){
                this.emptyInfo()
            }
        },
        //清空工作室信息
        emptyInfo(){
             this.studioInfo={
                    id:'',
                    name:'',
                    // phone:'',
                    asstantDoctor:'',
                    card:'',
                    hospital:'',
                    department:'',
                    position:'',
                    introduction:'',
                    speciality:'',
                    avator:'',
                    region:'',
                    date:'',
                    isAdd:true,
                    type:[],
                    doctor:[]
                }
        },
        //移除工作室详情标签
        removeTab(targetName) {
            let tabs = this.editableTabs;
            console.log(targetName)
            let activeName = this.editableTabsValue;
            if (activeName === targetName) {
            tabs.forEach((tab, index) => {
                if (tab.name === targetName) {
                let nextTab = tabs[index + 1] || tabs[index - 1];
                if (nextTab) {
                    activeName = nextTab.name;
                }
                }
            });
        }
        
        this.editableTabsValue = activeName;
        this.editableTabs = tabs.filter(tab => tab.name !== targetName);
        this.activeName='studiolist'
      },
      //返回工作室列表
      backList(val){
          if(val==1){
              //修改处返回
              this.removeTab(this.editableTabsValue)
          }else{
              //新增处返回
             this.activeName='studiolist' 
          }
      },
        //更新列表
      updateList(val){
          if(val==1){
              this.getDoctorGroups(this.currPage)
              this.removeTab(this.editableTabsValue)
          }else{
              this.getDoctorGroups(1)
            this.activeName='studiolist'
          }
      },
      //取消删除
       handleClose(done) {
            this.$confirm('确认关闭？')
            .then(_ => {
                done();
            })
            .catch(_ => {});
      },
      //页码改变
        changePage(val){
            // console.log(val)
            this.currPage=val
            this.getDoctorGroups(val)
        },
      //获取工作室列表
      getDoctorGroups(page){
          this.axios.get('doctorGroups',{
              params:{
                  page,
                  key:this.searchKey
              }
          }).then(res=>{
              console.log(res)
              this.doctorGroupsData=res.list
              this.totalPage=res.pages
          })
      }
    }
}
</script>
<style lang="less" scoped>
.studio-wrapper{
      .til{
        text-align: left;
        font-size: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 50px;
    }
    .el-table{
        margin-bottom: 15px;
    }
}
</style>