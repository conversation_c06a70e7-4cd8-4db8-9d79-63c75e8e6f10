<template>
  <div class="upload">
    <div class="img-box">
      <div
        v-for="(ite, i) in selfList"
        :key="showImg(ite) + i"
        class="upload-img-item"
        @click="preview(i, selfList, ite)"
      >
        <ShowImage :path="showImg(ite)" />
        <img
          v-if="isShowDeleteBtn && !ite.inProgress"
          src="@/assets/images/meetingManagement/detele-file.png"
          alt="del-btn"
          class="del"
          @click.stop="deleteImg(i)"
        />
        <span v-if="showOcrStatus" class="status">{{
          getCurPhotoType(ite)
        }}</span>
      </div>
      <div v-if="showUploadBtn" class="add-file" @click="addFile">
        <slot>
          <img
            src="@/assets/images/meetingManagement/add-file.png"
            alt="add-file"
            class="default-add-img"
          />
        </slot>
      </div>
    </div>
    <input
      ref="UploadFileRef"
      type="file"
      class="hidden"
      :multiple="maxFiles > 1"
      @change="uploadFileChange"
    />
    <PreviewFile
      v-if="isShowPreviewFile"
      :file-url="fileName"
      @close-preview="isShowPreviewFile = false"
    />
    <OcrPreview
      v-model:show="showOcrPreview"
      :images="ocrImageList"
      :cur-index="curOcrIndex"
      @confirm="handleOcrConfirm"
    />
  </div>
</template>
<script setup lang="ts">
import ShowImage from './ShowImage.vue';
import * as qiniu from 'qiniu-js';
import { OCR_STATUS, QINIU_UPLOAD_CONFIG } from '@/constant';
import PreviewFile from './PreviewFile.vue';
import OcrPreview from './OcrPreview.vue';
import {
  isArray,
  every,
  includes,
  isString,
  isPlainObject,
  find,
} from 'lodash-es';
import callWx from '@/utils/wx';
import { to } from '@/utils/util';
import { getUploadToken } from '@/api/workPlan';
import { showSuccessToast } from 'vant';
interface IUploadProps {
  list: string[] | { mediaId: string; fileName: string }[];
  uploadType?: string[];
  isShowUploadBtn?: boolean;
  isShowDeleteBtn?: boolean;
  maxFiles?: number;
  useOcrPreview?: boolean;
  showOcrStatus?: boolean;
}

// 配置常量
const imageTypeList = ['png', 'jpeg', 'jpg'];
const IMAGE_TYPES = ['.png', '.jpeg', '.jpg'];
const phoneType = getMobileOperatingSystem();

const props = withDefaults(defineProps<IUploadProps>(), {
  list: () => [],
  uploadType: () => ['png', 'jpeg', 'jpg'],
  isShowUploadBtn: true,
  isShowDeleteBtn: true,
  maxFiles: undefined, // 限制文件最大可上传数量，默认不限制
});
const emits = defineEmits(['update:list', 'move', 'delete']);

// 状态变量
const selfList = ref<any[]>([]);
const fileName = ref('');
const isShowPreviewFile = ref(false);
// 上传文件
const UploadFileRef = shallowRef<any>(null);

const ocrImageList = ref<string[]>([]);
const showOcrPreview = ref(false);
const curOcrIndex = ref(0);

const showUploadBtn = computed(() => {
  const { isShowUploadBtn, maxFiles } = props;
  return (
    isShowUploadBtn && (maxFiles ? maxFiles > selfList.value.length : true)
  );
});

const filePath = computed(() => {
  return import.meta.env.MODE !== 'production'
    ? QINIU_UPLOAD_CONFIG.urlQU
    : QINIU_UPLOAD_CONFIG.url;
});

const showImg = computed(() => (item: any) => {
  const path = isPlainObject(item) ? item.localId || item.url : item;
  return path || '';
});

const getCurPhotoType = (item: any) => {
  const { ocrStatus, resultStatus, photoType } = item;
  let curType = 999;
  if (!ocrStatus && !resultStatus && !photoType) {
    curType = 999;
  } else if (ocrStatus === 0) {
    curType = 0;
  } else if (ocrStatus === 2) {
    curType = 999;
  } else if (resultStatus === 0) {
    curType = 1;
  } else if (Number.isInteger(photoType)) {
    curType = photoType + 1;
  }
  return OCR_STATUS[curType as keyof typeof OCR_STATUS] ?? '未识别';
};
const handleOcrConfirm = (data: any) => {
  emits('move', data);
};

// 获取手机操作系统
function getMobileOperatingSystem() {
  const userAgent =
    navigator.userAgent || navigator.vendor || (window as any).opera;
  if (/android/i.test(userAgent)) return 'Android';
  if (/iPad|iPhone|iPod/.test(userAgent) && !(window as any).MSStream)
    return 'IOS';
}

/** 数组b 是否保函数组a */
const arraysHaveSameElements = (a: string[], b: string[]) =>
  every(a, t => includes(b, t));

/** 同步数据 */
const updateList = (data: any[]) => {
  selfList.value = data;
  emits(
    'update:list',
    data.map(item => {
      const { mediaId, fileName, localId } = item;
      if (fileName) return { mediaId, fileName, localId };
      return item;
    }) || []
  );
};

// 删除图片
const deleteImg = (index: number) => {
  const item = selfList.value[index];
  const data = selfList.value.filter((_, idx) => idx !== index);
  emits('delete', item);
  updateList(data);
};

// 上传图片
const uploadPic = async (imgList: string[]) => {
  showLoadingToast({
    message: 'Uploading...',
    forbidClick: true,
    loadingType: 'spinner',
    duration: 0,
  });

  let currentTimestamp = Date.now();
  const temporaryList = [...selfList.value];

  for (const localId of imgList) {
    const [uploadErr, uploadRes] = await to(callWx('uploadImage', { localId }));
    if (uploadErr) {
      showToast(`上传图片失败: ${uploadErr._errMsg}`);
      return;
    }

    const timestamp = String(currentTimestamp++);
    const info = {
      mediaId: uploadRes.serverId,
      fileName: `${timestamp}.tp`,
      localId,
    };

    if (phoneType === 'IOS') {
      await new Promise(resolve => {
        (window as any).wx.getLocalImgData({
          localId: localId,
          success: ({ localData }: { localData: string }) => {
            info.localId = localData;
            temporaryList.push(info);
            resolve(1);
          },
        });
      });
    } else {
      temporaryList.push(info);
    }
  }
  updateList(temporaryList);
  showToast('图片添加成功!');
};

const uploadFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (!target?.files) return;

  const files = Array.from(target.files);
  const { maxFiles, uploadType } = props;
  const totalFiles = selfList.value.length + files.length;

  if (maxFiles && totalFiles > maxFiles) {
    return showToast(`最多只能上传 ${maxFiles} 个文件`);
  }

  showLoadingToast({
    message: '上传中...',
    forbidClick: true,
    loadingType: 'spinner',
    duration: 0,
  });

  const formatTypes = uploadType.map(item => `.${item}`);
  const textTypeStr = uploadType.join('、');
  const uptoken = JSON.parse(
    sessionStorage.getItem('qiNiuToken') || '{}'
  )?.token;
  const temporaryList = [...selfList.value];

  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const fileType = file.name.slice(file.name.lastIndexOf('.'));
    if (!formatTypes.includes(fileType)) {
      return showToast(`文件类型错误，仅支持 ${textTypeStr}格式`);
    }

    const key = `${Date.now()}${file.name}`;
    const config = {
      useCdnDomain: true, //表示是否使用 cdn 加速域名，为布尔值，true 表示使用，默认为 false。
      region: qiniu.region.z2, // 根据具体提示修改上传地区,当为 null 或 undefined 时，自动分析上传域名区域
    };
    const putExtra = {
      fname: '', //文件原文件名
      params: {}, //用来放置自定义变量
      // mimeType: null, //用来限制上传文件类型，为 null 时表示不对文件类型限制；限制类型放到数组里： ["image/png", "image/jpeg", "image/gif"]
    };
    const observable = qiniu.upload(file, key, uptoken, putExtra, config);

    observable.subscribe({
      next: () => {},
      error: () => {
        showToast('上传失败,请稍后重试！');
        target.value = '';
        getUploadTokenData();
      },
      complete: (result: { key: string | string[] }) => {
        const path = filePath.value + result.key;
        temporaryList.push(path);
        target.value = '';

        if (i === files.length - 1) {
          showSuccessToast('上传成功!');
          updateList(temporaryList);
        }
      },
    });
  }
};

// 选择上传附件方式（文件或图片）
const addFile = () => {
  const { uploadType } = props;
  if (arraysHaveSameElements(uploadType, imageTypeList)) {
    (window as any).wx.chooseImage({
      success: async (res: { errMsg: string; localIds: string[] }) => {
        await uploadPic(res.localIds);
      },
      fail: (res: { errMsg: string | undefined }) => {
        showToast(res.errMsg);
      },
    });
  } else {
    UploadFileRef.value?.click();
  }
};

// 处理文件（图片）
const formatFileList = (list: any[]) => {
  const sList = selfList.value;
  return list.map(item => {
    const { fileName } = item;
    const oriItem = (fileName && find(sList, { fileName })) || null;
    return oriItem || item;
  });
};

watch(
  () => props.list,
  value => {
    if (!value) return;
    const fileList = isArray(value) ? value : [value];
    const hasStrItem = fileList.some(item => isString(item));
    if (hasStrItem && arraysHaveSameElements(props.uploadType, imageTypeList)) {
      return updateList(
        fileList.map(item => (isString(item) ? { url: item } : item))
      );
    }
    selfList.value = formatFileList(fileList);
  },
  { immediate: true, deep: true }
);

const preview = (index: number, imageList: any[], ite: any) => {
  const { uploadType } = props;
  if (arraysHaveSameElements(uploadType, imageTypeList)) {
    if (props.useOcrPreview && !ite.inProgress) {
      const images = imageList
        .filter(v => !v.inProgress)
        .map(item => showImg.value(item));
      const _index = images.findIndex(v => v === showImg.value(ite));
      ocrImageList.value = images;
      showOcrPreview.value = true;
      curOcrIndex.value = _index;
    } else {
      const images = imageList
        .filter(v => {
          if (props.useOcrPreview) {
            return !!v.inProgress;
          }
          return true;
        })
        .map(item => showImg.value(item));
      const _index = images.findIndex(v => v === showImg.value(ite));
      showImagePreview({
        images,
        startPosition: _index,
      });
    }
  } else {
    const containsImageType = IMAGE_TYPES.some(type => ite.includes(type));

    if (containsImageType) {
      const images = imageList.filter(item => {
        const extension = item.slice(item.lastIndexOf('.'));
        return IMAGE_TYPES.includes(extension);
      });

      const file = imageList.filter((item, i) => {
        const extension = item.slice(item.lastIndexOf('.'));
        return !IMAGE_TYPES.includes(extension) && i < index;
      });
      index -= file.length;
      if (props.useOcrPreview && !ite.inProgress) {
        const curImages = images
          .filter(v => !v.inProgress)
          .map(item => showImg.value(item));
        const _index = curImages.findIndex(v => v === showImg.value(ite));
        ocrImageList.value = curImages;
        showOcrPreview.value = true;
        curOcrIndex.value = _index;
      } else {
        const curImages = images
          .filter(v => {
            if (props.useOcrPreview) {
              return !!v.inProgress;
            }
            return true;
          })
          .map(item => showImg.value(item));
        const _index = curImages.findIndex(v => v === showImg.value(ite));
        showImagePreview({
          images: curImages,
          startPosition: _index,
        });
      }
    } else {
      goPath(ite);
    }
  }
};

const goPath = (fileUrl: any) => {
  fileName.value = fileUrl;
  isShowPreviewFile.value = true;
};

// 上传token;
const getUploadTokenData = async () => {
  try {
    const res = await getUploadToken();
    sessionStorage.setItem(
      'qiNiuToken',
      JSON.stringify({ token: res.data, timestamp: Date.now() })
    );
  } catch (e) {
    // console.error('获取token 失败！', e);
  }
};

// 校验上传文件 token 是否失效。如果失效需要重新获取
const handleTokenInvalid = () => {
  const qiNiuToken = sessionStorage.getItem('qiNiuToken');
  if (
    !qiNiuToken ||
    Date.now() - JSON.parse(qiNiuToken).timestamp >
      QINIU_UPLOAD_CONFIG.expiresInTime
  ) {
    getUploadTokenData();
  }
};

onMounted(() => {
  handleTokenInvalid();
});
</script>
<style scoped lang="less">
.upload {
  .img-box {
    display: flex;
    flex-wrap: wrap;
    .upload-img-item {
      margin: 12px 11px 12px 0;
      position: relative;
      width: 160px;
      height: 160px;

      &:nth-child(4n + 1) {
        margin-left: 0;
      }

      .del {
        width: 36px;
        height: 36px;
        position: absolute;
        top: 0;
        right: 0;
      }
      .status {
        background: #111111;
        color: #fff;
        font-size: 20px;
        padding: 0 4px;
        line-height: 36px;
        height: 36px;
        position: absolute;
        top: 0px;
        left: 0px;
      }
    }
    .add-file {
      margin: 12px 0;

      .default-add-img {
        width: 160px;
        height: 160px;
        border-radius: 8px;
      }
    }
  }
}
</style>
