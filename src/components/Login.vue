<template>
  <div class="login-wrapper">
    <div class="login-box">
      <el-form
        :model="ruleForm"
        status-icon
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="账号" prop="username" placeholder="请输入账号">
          <el-input v-model="ruleForm.username"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="pass" placeholder="请输入密码">
          <el-input
            type="password" show-password
            v-model="ruleForm.pass"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="submitForm('ruleForm')"
            @keyup.enter.native="loginEnter"
            >登录</el-button
          >
          <el-button @click="resetForm('ruleForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import crypto from '@/common/AES'
import { httpReq } from "@/http";
export default {
  name: "Login",
  data() {
    var checkUserName = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("账号不能为空"));
      } else {
        callback();
      }
    };
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入密码"));
      } else {
        if (this.ruleForm.checkPass !== "") {
          this.$refs.ruleForm.validateField("checkPass");
        }
        callback();
      }
    };
    return {
      ruleForm: {
        pass: "",
        username: "",
      },
      rules: {
        username: [{ validator: checkUserName, trigger: "blur" }],
        pass: [{ validator: validatePass, trigger: "blur" }],
      },
    };
  },
  created() {
    document.onkeydown = (e) => {
      if (window.event === undefined) {
        var key = e.keyCode;
      } else {
        var key = window.event.keyCode;
      }
      if (key === 13) {
        this.submitForm("ruleForm");
      }
    };
  },
  mounted() {
    // console.log(httpReq)
    this.aes();
  },
  methods: {
    aes() {
    //   let a = "APaK1gQJK9SbMLwXqbqlSpxpwYeUVGd1AcLd56sklaIjBxYWMiCdgBfvMrOZuAUC";
      let a = 'W+XPRqsEsVpE6VkzeD50NAVWY8z1BIeY+jbx42jWVPE1oGfDblw7Lkh7k6LlX38eLUgAx7veI1TUhOmVAajxNe3DtTMl5lL3SSZ9wzN43JGUiAldeqBzvcjoMpuofAck'
      let b = crypto.decrypt(a);
      console.log(a, "加密");
      console.log(b, "解密");
    },
    //登录
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "登录中",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          // this.axios.post('admins/doLogin',{
          //         adminName : this.ruleForm.username,
          //         password : this.ruleForm.pass
          // }).then(res=>{
          //     loading.close();
          //     this.$message({
          //         message:  `欢迎登录哈瑞特管理系统！`,
          //         type: 'success'
          //     })
          //     window.sessionStorage.setItem('userName',this.ruleForm.username)
          //     window.sessionStorage.setItem('token',res.token)
          //     this.$router.push('/basicservice')
          // }).catch(err=>{
          //     loading.close();
          // })
          httpReq({
            url: "/admins/doLogin",
            method: "post",
            custom: "noqs",
            headers: {
              "Content-Type": "application/json;charset=UTF-8",
            },
            data: {
              accountNumber: this.ruleForm.username,
              password: this.ruleForm.pass,
            },
          })
            .then((res) => {
              console.log(res);
                   this.$message({
                  message:  `欢迎登录哈瑞特管理系统！`,
                  type: 'success'
              })
              window.sessionStorage.setItem('userName',res.data.name)
              window.sessionStorage.setItem('permission',res.data.permission)
              window.sessionStorage.setItem('token',res.token)
              this.$router.push('/basicservice')
            })
            .catch((err) => {
              console.log(err);
              this.$message.error(`登录失败：${err.msg}`)
            });
            loading.close();
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  },
  beforeDestroy() {
    //离开页面阻止回车事件
    document.onkeydown = undefined;
  },
  destoryed() {
    //离开页面阻止回车事件
    document.onkeydown = undefined;
  },
};
</script>
<style lang="less" scoped>
.login-wrapper {
  width: 100%;
  height: 100%;
  // background:rgb(4, 22, 62,.5);
  background-image: url(../assets/image/bg.jpg);
  background-size: cover;
  .login-box {
    width: 400px;
    height: 300px;
    margin: 0 auto;
    position: relative;
    top: 46%;
  }
}
</style>