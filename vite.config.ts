import type { UserConfig } from 'vite'
import path from 'path'
import vue from '@vitejs/plugin-vue2'
import vueJsx from '@vitejs/plugin-vue-jsx'

const proxyTarget = 'https://www.hrttest.cn/' // 目标地址

export default {
  plugins: [vue(), vueJsx()],
  base: './',
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      vue: 'vue/dist/vue.esm.js',
    },
  },
  server: {
    proxy: {
      '/sys': {
        target: proxyTarget,
        changeOrigin: true,
      },
      '/admins': {
        target: proxyTarget,
        changeOrigin: true,
      },
      '/doc': {
        target: proxyTarget,
        changeOrigin: true,
      },
      '/hospital': {
        target: proxyTarget,
        changeOrigin: true,
      },
      '/users': {
        target: proxyTarget,
        changeOrigin: true,
      },
      '/api': {
        target: proxyTarget,
        changeOrigin: true,
      },
      '/import': {
        target: proxyTarget,
        changeOrigin: true,
      },
    },
  },
} satisfies UserConfig
